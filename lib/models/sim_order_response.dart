class SIMOrderResponse {
  bool? success;
  int? statusCode;
  int? code;
  String? message;
  SIMResponse? data;

  SIMOrderResponse({this.success, this.statusCode, this.code, this.data, this.message});

  SIMOrderResponse copyWith({
    bool? success,
    int? statusCode,
    int? code,
    String? messag,
    SIMResponse? data,
  }) =>
      SIMOrderResponse(
        success: success ?? this.success,
        statusCode: statusCode ?? this.statusCode,
        code: code ?? this.code,
        message: messag ?? this.message,
        data: data ?? this.data,
      );

  factory SIMOrderResponse.fromJson(Map<String, dynamic> json) => SIMOrderResponse(
        success: json["success"],
        statusCode: json["statusCode"],
        code: json["code"],
        message: json["message"],
        data: json["code"] == 0
            ? json["data"] == null
                ? null
                : SIMResponse.fromJson(json["data"])
            : null,
      );
}

class SIMResponse {
  String orderId;
  int orderTime;
  String status;

  SIMResponse({
    required this.orderId,
    required this.orderTime,
    required this.status,
  });

  SIMResponse copyWith({
    String? orderId,
    int? orderTime,
    String? status,
  }) =>
      SIMResponse(
        orderId: orderId ?? this.orderId,
        orderTime: orderTime ?? this.orderTime,
        status: status ?? this.status,
      );

  factory SIMResponse.fromJson(Map<String, dynamic> json) => SIMResponse(
        orderId: json["order_id"],
        orderTime: json["order_time"],
        status: json["status"],
      );
}
