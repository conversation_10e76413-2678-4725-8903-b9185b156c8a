import 'package:dropdown_button2/dropdown_button2.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:shopping/constants/app_assets_paths.dart';
import 'package:shopping/constants/app_colors.dart';
import 'package:shopping/constants/app_text_style.dart';
import 'package:shopping/models/pgd_address.dart';
import 'package:shopping/models/province.dart';
import 'package:shopping/pages/sim/widgets/sim_text_field_widget.dart';
import 'package:shopping/utils/extension/string_extension.dart';

class RequiredDropdownSeachFormField<T> extends StatelessWidget {
  final String label;
  final bool? isRequired;
  final List<DropdownItem<T>>? items;
  final ValueNotifier<T?> valueSelectedNotifier;
  final void Function(T?) onChanged;
  final String? Function(T?)? customValidator;
  final String hintText;
  final TextEditingController textEditingController;

  const RequiredDropdownSeachFormField(
      {super.key,
      required this.label,
      this.isRequired,
      required this.items,
      required this.valueSelectedNotifier,
      required this.onChanged,
      this.customValidator,
      this.hintText = '',
      required this.textEditingController});

  @override
  Widget build(BuildContext context) {
    return FormField<T>(
      initialValue: valueSelectedNotifier.value,
      validator: (val) {
        if (isRequired == true &&
            (val == null || (val is String && val.isEmpty))) {
          return 'Vui lòng chọn $label';
        }
        if (customValidator != null) {
          return customValidator!(val);
        }
        return null;
      },
      builder: (FormFieldState<T> state) {
        if (valueSelectedNotifier.value != state.value) {
          WidgetsBinding.instance.addPostFrameCallback((_) {
            state.didChange(valueSelectedNotifier.value);
          });
        }
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            LableWidget(
              isRequired: isRequired,
              title: label,
            ),
            const SizedBox(height: 5),
            InputDecorator(
              baseStyle:
                  AppTextStyle.s14Medium.copyWith(color: AppColors.black1E1E1E),
              decoration: InputDecoration(
                errorStyle: AppTextStyle.s12Regular.copyWith(color: Colors.red),
                hintStyle: AppTextStyle.s14Medium
                    .copyWith(color: AppColors.black1E1E1E),
                contentPadding:
                    const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                errorText: state.errorText,
                enabledBorder: OutlineInputBorder(
                  borderSide:
                      const BorderSide(width: 1, color: AppColors.greyD9D9D9),
                  borderRadius: BorderRadius.circular(8),
                ),
                focusedBorder: OutlineInputBorder(
                  borderSide:
                      const BorderSide(width: 1, color: AppColors.blue3D4FF4),
                  borderRadius: BorderRadius.circular(8),
                ),
                errorBorder: OutlineInputBorder(
                  borderSide: const BorderSide(width: 1, color: Colors.red),
                  borderRadius: BorderRadius.circular(8),
                ),
                focusedErrorBorder: OutlineInputBorder(
                  borderSide: const BorderSide(width: 1, color: Colors.red),
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: DropdownButtonHideUnderline(
                child: DropdownButton2<T>(
                  isExpanded: true,
                  style: AppTextStyle.s15SemiBold
                      .copyWith(color: AppColors.black1E1E1E),
                  hint: Text(
                    hintText,
                    style: AppTextStyle.s14Medium
                        .copyWith(color: AppColors.black1E1E1E),
                  ),
                  items: items,
                  valueListenable: valueSelectedNotifier,
                  onChanged: (val) {
                    state.didChange(val);
                    onChanged(val);
                  },
                  iconStyleData: IconStyleData(
                    icon: Padding(
                      padding: const EdgeInsets.only(right: 0),
                      child: SvgPicture.asset(
                        SvgPath.svgArrowDown,
                        colorFilter: ColorFilter.mode(AppColors.black, BlendMode.srcIn),
                      ),
                    ),
                    iconEnabledColor: AppColors.black,
                  ),
                  buttonStyleData: const ButtonStyleData(
                      height: 40,
                      width: 200,
                      decoration: BoxDecoration(color: AppColors.white)),
                  dropdownStyleData: DropdownStyleData(
                      maxHeight: MediaQuery.of(context).size.height * 0.4,
                      decoration: BoxDecoration(color: AppColors.white)),
                  menuItemStyleData: const MenuItemStyleData(),
                  dropdownSearchData: DropdownSearchData(
                    searchController: textEditingController,
                    searchBarWidgetHeight: 50,
                    searchBarWidget: Container(
                      height: 50,
                      padding: const EdgeInsets.only(
                        top: 8,
                        bottom: 4,
                        right: 8,
                        left: 8,
                      ),
                      child: TextFormField(
                        expands: true,
                        maxLines: null,
                        controller: textEditingController,
                        decoration: InputDecoration(
                          isDense: true,
                          contentPadding: const EdgeInsets.symmetric(
                            horizontal: 10,
                            vertical: 8,
                          ),
                          hintText: '',
                          hintStyle: const TextStyle(fontSize: 12),
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                      ),
                    ),
                    searchMatchFn: (item, searchValue) {
                      if (valueSelectedNotifier.value is PhongGiaoDich ||
                          item.value is PhongGiaoDich) {
                        PhongGiaoDich? _pgd = item.value as PhongGiaoDich?;
                        String _name = _pgd?.name ?? '';
                        return _name.normalizeVietnamese().contains(
                            searchValue.toLowerCase().normalizeVietnamese());
                      }
                      if (valueSelectedNotifier.value is Address ||
                          item.value is Address) {
                        Address? _a = item.value as Address;
                        String _name = _a.name ?? '';
                        return _name.normalizeVietnamese().contains(
                            searchValue.toLowerCase().normalizeVietnamese());
                      }
                      return item.value
                          .toString()
                          .toLowerCase()
                          .normalizeVietnamese()
                          .contains(
                              searchValue.toLowerCase().normalizeVietnamese());
                    },
                  ),
                  //This to clear the search value when you close the menu
                  onMenuStateChange: (isOpen) {
                    if (!isOpen) {
                      textEditingController.clear();
                    }
                  },
                ),
              ),
            ).paddingOnly(bottom: 15),
          ],
        );
      },
    );
  }
}
