import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:shopping/constants/app_assets_paths.dart';
import 'package:shopping/constants/app_colors.dart';
import 'package:shopping/constants/app_text_style.dart';
import 'package:shopping/enum/enum_payment_type.dart';
import 'package:shopping/loading_screen.dart';
import 'package:shopping/models/list_bank_infor_response.dart';
import 'package:shopping/pages/sim/policy_detail_screen.dart';
import 'package:shopping/tabs/home/<USER>';
import 'package:shopping/widgets/app_radio_button_widget.dart';

class PaymentScreen extends StatelessWidget {
  const PaymentScreen(
      {super.key,
      required this.ordId,
      required this.isFromSheetPick,
      required this.userPhoneNumber});
  final int? ordId;
  final bool? isFromSheetPick;
  final String userPhoneNumber;
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.white,
      appBar: AppBar(
        titleSpacing: 0,
        leading: GestureDetector(
          child: Container(
            width: 20,
            height: 20,
            color: Colors.transparent,
            child: SvgPicture.asset(SvgPath.svgArrowBack,
                color: AppColors.black, fit: BoxFit.scaleDown),
          ),
          onTap: () async {
            Navigator.pop(context);
          },
        ),
        backgroundColor: AppColors.white,
        elevation: 5,
        centerTitle: true,
        shadowColor: AppColors.black.withOpacity(0.4),
        surfaceTintColor: AppColors.white,
        title: Text('THANH TOÁN ONLINE',
            style: AppTextStyle.s16Bold.copyWith(color: AppColors.black2E2E2E)),
      ),
      body: GetBuilder<PaymentOnlineController>(
          init: PaymentOnlineController(
              ordId: ordId, userPhoneNumber: userPhoneNumber),
          id: PaymentOnlineControllerUpdateKey.updateData,
          builder: (controller) => Obx(
                () => controller.isLoading.value
                    ? LoadingWidget()
                    : Padding(
                        padding:
                            EdgeInsets.only(bottom: 30, left: 12, right: 12),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            //
                            Expanded(
                                child: ListView(
                                    physics: ClampingScrollPhysics(),
                                    children: [
                                  Padding(
                                    padding: const EdgeInsets.only(top: 30),
                                    child: Text("Chọn phương thức thanh toán",
                                        style: AppTextStyle.s18SemiBold
                                            .copyWith(color: AppColors.black)),
                                  ),
                                  const SizedBox(height: 10),
                                  GetBuilder<PaymentOnlineController>(
                                      id: PaymentOnlineControllerUpdateKey
                                          .updatePaymentType,
                                      builder: (controller) =>
                                          ListView.separated(
                                            padding: EdgeInsets.zero,
                                            shrinkWrap: true,
                                            physics:
                                                NeverScrollableScrollPhysics(),
                                            itemBuilder: (_, index) {
                                              PaymentOnlineType item = controller
                                                          .listPaymentOnlineType[
                                                      index] ??
                                                  PaymentOnlineType
                                                      .values[index];
                                              return InkWell(
                                                splashColor: Colors.transparent,
                                                onTap: () {
                                                  controller
                                                      .onChangedPaymetOnline(
                                                          item);
                                                },
                                                child: AppRadioButtonWidget<
                                                        PaymentOnlineType>(
                                                    item: item,
                                                    groupValue: controller
                                                        .paymentOnlineChoosed,
                                                    onChanged:
                                                        (PaymentOnlineType?
                                                            value) {
                                                      controller
                                                          .onChangedPaymetOnline(
                                                              value);
                                                    },
                                                    borderColor: controller
                                                            .isSelected(item)
                                                        ? AppColors.redCE3722
                                                        : null,
                                                    widget: _ItemPayWidget(
                                                        item: item,
                                                        index: index)),
                                              );
                                            },
                                            separatorBuilder: (_, index) =>
                                                const SizedBox(height: 15),
                                            itemCount: controller
                                                .listPaymentOnlineType.length,
                                          )),
                                  GetBuilder<PaymentOnlineController>(
                                      id: PaymentOnlineControllerUpdateKey
                                          .updatePaymentType,
                                      builder: (controller) => controller
                                              .isShowListBank
                                          ? Column(
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.start,
                                              mainAxisSize: MainAxisSize.min,
                                              children: [
                                                Padding(
                                                  padding:
                                                      EdgeInsets.only(top: 15),
                                                  child: Text(
                                                      "Lựa chọn một trong các ngân hàng bạn có",
                                                      style: AppTextStyle
                                                          .s14Medium
                                                          .copyWith(
                                                              color: AppColors
                                                                  .black)),
                                                ),
                                                const SizedBox(height: 22),
                                                GridView.builder(
                                                    padding: EdgeInsets.zero,
                                                    shrinkWrap: true,
                                                    physics:
                                                        NeverScrollableScrollPhysics(),
                                                    itemCount: controller
                                                        .listBank.length,
                                                    gridDelegate:
                                                        SliverGridDelegateWithFixedCrossAxisCount(
                                                      crossAxisCount: 3,
                                                      mainAxisSpacing: 15,
                                                      crossAxisSpacing: 15,
                                                      childAspectRatio:
                                                          107 / 49,
                                                    ),
                                                    itemBuilder: (_, index) {
                                                      Bank item = controller
                                                          .listBank[index];
                                                      return InkWell(
                                                        splashColor:
                                                            Colors.transparent,
                                                        onTap: () {
                                                          controller
                                                              .onClickBank(
                                                                  item);
                                                        },
                                                        child: item.code ==
                                                                controller
                                                                    .bankSelected
                                                                    ?.code
                                                            ? Container(
                                                                decoration:
                                                                    BoxDecoration(
                                                                  borderRadius:
                                                                      BorderRadius
                                                                          .circular(
                                                                              2),
                                                                  border: Border
                                                                      .all(
                                                                    color: AppColors
                                                                        .redCE3722,
                                                                  ),
                                                                ),
                                                                child:
                                                                    _BankItemWidget(
                                                                  logo:
                                                                      item.logo ??
                                                                          '',
                                                                ),
                                                              )
                                                            : _BankItemWidget(
                                                                logo:
                                                                    item.logo ??
                                                                        ''),
                                                      );
                                                    }),
                                                SizedBox(
                                                    height:
                                                        controller.isAccepted
                                                            ? 10
                                                            : 35)
                                              ],
                                            )
                                          : const SizedBox()),
                                ])),

                            //
                            GetBuilder<PaymentOnlineController>(
                                id: PaymentOnlineControllerUpdateKey
                                    .updatePaymentType,
                                builder: (controller) =>
                                    controller.isShowListBank
                                        ? SizedBox()
                                        : Spacer()),
                            controller.isAccepted
                                ? const SizedBox()
                                : Row(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      GetBuilder<PaymentOnlineController>(
                                          id: PaymentOnlineControllerUpdateKey
                                              .updateAgreeToPolicy,
                                          builder: (controller) =>
                                              GestureDetector(
                                                onTap: () {
                                                  controller.onChangeStateAgree(
                                                      !controller
                                                          .isAgreeToPolicy);
                                                },
                                                child: SizedBox(
                                                  width: 20,
                                                  height: 32,
                                                  child: Checkbox(
                                                    side: const BorderSide(
                                                      width: 1,
                                                      color:
                                                          AppColors.grey6A6A6A,
                                                    ),
                                                    shape:
                                                        const RoundedRectangleBorder(
                                                      borderRadius:
                                                          BorderRadius.all(
                                                        Radius.circular(5.0),
                                                      ),
                                                    ),
                                                    materialTapTargetSize:
                                                        MaterialTapTargetSize
                                                            .shrinkWrap,
                                                    activeColor:
                                                        AppColors.redCE3722,
                                                    value: controller
                                                        .isAgreeToPolicy,
                                                    onChanged: (value) {
                                                      if (value == null) return;
                                                      controller
                                                          .onChangeStateAgree(
                                                              value);
                                                    },
                                                  ),
                                                ),
                                              )),
                                      SizedBox(width: 10),
                                      GestureDetector(
                                        onTap: () {
                                          Navigator.push(
                                            context,
                                            MaterialPageRoute(
                                                builder: (context) =>
                                                    PolicyDetailScreen(
                                                      webLink:
                                                          "https://muadimuadi.vn/guide",
                                                    )),
                                          );
                                        },
                                        child: Padding(
                                          padding:
                                              const EdgeInsets.only(top: 2),
                                          child: RichText(
                                            text: TextSpan(
                                              children: [
                                                TextSpan(
                                                    text: 'Tôi đồng ý với ',
                                                    style: TextStyle(
                                                        fontSize: 14,
                                                        fontWeight:
                                                            FontWeight.w500,
                                                        color:
                                                            AppColors.black)),
                                                TextSpan(
                                                    text: 'Điều khoản sử dụng ',
                                                    style: TextStyle(
                                                        fontSize: 14,
                                                        fontWeight:
                                                            FontWeight.w500,
                                                        color: AppColors
                                                            .blue005BF9,
                                                        height: 19 / 13,
                                                        decoration:
                                                            TextDecoration
                                                                .underline)),
                                                TextSpan(
                                                    text: 'và ',
                                                    style: TextStyle(
                                                      fontSize: 14,
                                                      fontWeight:
                                                          FontWeight.w500,
                                                      color: AppColors.black,
                                                      height: 19 / 13,
                                                    )),
                                                TextSpan(
                                                    text:
                                                        'Chính \nsách riêng tư ',
                                                    style: TextStyle(
                                                        fontSize: 14,
                                                        fontWeight:
                                                            FontWeight.w500,
                                                        color: AppColors
                                                            .blue005BF9,
                                                        height: 19 / 13,
                                                        decoration:
                                                            TextDecoration
                                                                .underline)),
                                                TextSpan(
                                                    text: 'của Muadi',
                                                    style: TextStyle(
                                                      fontSize: 14,
                                                      fontWeight:
                                                          FontWeight.w500,
                                                      color: AppColors.black,
                                                      height: 19 / 13,
                                                    )),
                                              ],
                                            ),
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                            GetBuilder<PaymentOnlineController>(
                                id: PaymentOnlineControllerUpdateKey
                                    .updateStateEnabeButton,
                                builder: (controlelr) {
                                  bool enable = false;
                                  if (controlelr.isAgreeToPolicy) {
                                    if (controller.isShowListBank &&
                                        controller.bankSelected != null) {
                                      enable = true;
                                    }
                                    if (!controller.isShowListBank) {
                                      enable = true;
                                    }
                                  }
                                  return InkWell(
                                    splashColor: Colors.transparent,
                                    onTap: () async{
                                      // open web browser
                                      if (enable) {
                                       await controller.onClickOpenWebPayOnline(
                                            context, isFromSheetPick ?? false);
                                      }
                                    },
                                    child: Container(
                                      decoration: BoxDecoration(
                                          borderRadius:
                                              BorderRadius.circular(8),
                                          color: (enable)
                                              ? AppColors.redCE3723
                                              : AppColors.greyCACACA),
                                      margin: EdgeInsets.only(
                                          top: controller.isAccepted ? 0 : 31),
                                      padding: EdgeInsets.all(12),
                                      child: Center(
                                        child: Text(
                                          'XÁC NHẬN',
                                          style: AppTextStyle.s16Medium
                                              .copyWith(
                                                  height: 21 / 16,
                                                  color: AppColors.white),
                                        ),
                                      ),
                                    ),
                                  );
                                })
                          ],
                        ),
                      ),
              )),
    );
  }
}

class _ItemPayWidget extends StatelessWidget {
  const _ItemPayWidget({required this.item, required this.index});
  final PaymentOnlineType item;
  final int index;
  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 10),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Padding(
            padding: EdgeInsets.symmetric(vertical: index == 1 ? 4 : 0),
            child: SvgPicture.asset(item.getIcon),
          ),
          const SizedBox(width: 18),
          Flexible(
            child: Text(
              item.getName,
              style: AppTextStyle.s14Medium.copyWith(
                  color: AppColors.black262626,
                  overflow: TextOverflow.ellipsis),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }
}

class _BankItemWidget extends StatelessWidget {
  const _BankItemWidget({required this.logo});
  final String logo;
  @override
  Widget build(BuildContext context) {
    return Image.network(logo);
  }
}
