import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:shopping/apis/ShoppingRepository.dart';
import 'package:shopping/app_services/api_service.dart';
import 'package:shopping/app_services/app_storage.dart';
import 'package:shopping/loading_screen.dart';
import 'package:shopping/models/base_response.dart';
import 'package:shopping/models/insurance_create_order_request_form.dart';
import 'package:shopping/models/insurance_create_order_response.dart';
import 'package:shopping/shopping_controller.dart';
import 'package:shopping/utils/extension/int_extension.dart';
import 'package:shopping/utils/extension/string_extension.dart';
import 'package:shopping/widgets/app_dialog.dart';
import 'package:url_launcher/url_launcher.dart';

class CarStepConfirmController extends GetxController {
  CarStepConfirmController({required this.insuranceCreateOrdRequestForm});

  bool isConfirmed = false;

  // tên chủ xe
  String? tenChuXeTxt;
  RxBool isValidateTenChuXe = false.obs;

  // biển số xe
  String? bienSoXeTxt;
  bool isValidateBsx = false;

  // số khung
  String? soKhungxt;

  // tên chủ hợp đồng
  String? tenChuHDTxt;
  RxBool isValidateTenChuHD = false.obs;

  // sdt
  String? sdtTxt;
  RxBool isValidateSdt = false.obs;

  // enail chủ hợp đồng
  String? emailTxt;
  RxBool isValidateEmail = false.obs;

  // diachi
  String? diaChiTxt;

  //
  InsuranceCreateOrdRequestForm insuranceCreateOrdRequestForm;
  String? urlPayInsurance;

  RxBool isLoadingBtn = false.obs;

  @override
  void onInit() {
    super.onInit();
    _initData();
  }

  void _initData() {
    if (Get.isRegistered<ShoppingController>()) {
      tenChuHDTxt = Get.find<ShoppingController>().user?.fullname;
      sdtTxt = Get.find<ShoppingController>().user?.phoneNumber;
      diaChiTxt = Get.find<ShoppingController>().user?.addressFull;
    }
  }

  void onChangeChuXe(String? newValue) {
    if (newValue == null) {
      return;
    }
    tenChuXeTxt = newValue;
  }

  void onChangeBsx(String? newValue) {
    if (newValue == null) {
      return;
    }
    bienSoXeTxt = newValue.setupBienSoOto();
    update([CarStepCofirmControllerUpdateKey.updateBienSoXeOto]);
  }

  void updateStateBsx(bool newValue) {
    isValidateBsx = newValue;
    update([CarStepCofirmControllerUpdateKey.updateBienSoXeOto]);
  }

  void onChangeSoKhung(String? newValue) {
    if (newValue == null) {
      return;
    }
    soKhungxt = newValue;
  }

  void onChangeTenChuHD(String? newValue) {
    if (newValue == null) {
      return;
    }
    tenChuHDTxt = newValue;
  }

  void onChangeSdt(String? newValue) {
    if (newValue == null) {
      return;
    }
    sdtTxt = newValue;
  }

  void onChangeEmail(String newValue) {
    emailTxt = newValue;
  }

  String? validateEmail() {
    if (isValidateEmail.value == true) {
      if (emailTxt == null || (emailTxt ?? '').isEmpty) {
        return null;
      }
      if ((emailTxt ?? '').validateEmail()) {
        return 'Email không hợp lệ';
      }
    }
    return null;
  }

  void onChangeDiaChi(String? newValue) {
    if (newValue == null) {
      return;
    }
    diaChiTxt = newValue;
  }

  void onChangeCofirm(bool? newValue) {
    if (newValue == null) {
      return;
    }
    isConfirmed = newValue;
    update([CarStepCofirmControllerUpdateKey.updateCheckboxConfirm]);
  }

  void onClickBtn({required BuildContext context}) async {
    updateStateBsx(true);
    isValidateSdt.value = true;
    isValidateTenChuHD.value = true;
    isValidateTenChuXe.value = true;
    isValidateEmail.value = true;
    // call api
    if ((validateTenChuXe() ?? '').isEmpty &&
        (validateBsx() ?? '').isEmpty &&
        (validateTenChuHD() ?? '').isEmpty &&
        (validateSdt() ?? '').isEmpty &&
        (validateEmail() ?? '').isEmpty) {
      await _onCreateOrder(context: context);
    }
  }

  String? validateTenChuXe() {
    if ((tenChuXeTxt == null || (tenChuXeTxt ?? '').isEmpty) &&
        isValidateTenChuXe.value == true) {
      return 'Vui lòng nhập tên chủ xe';
    }
    return null;
  }

  String? validateTenChuHD() {
    if ((tenChuHDTxt == null || (tenChuHDTxt ?? '').isEmpty) &&
        isValidateTenChuHD.value == true) {
      return 'Vui lòng nhập tên chủ hợp đồng';
    }
    return null;
  }

  String? validateSdt() {
    if (isValidateSdt.value == true) {
      if (sdtTxt == null || (sdtTxt ?? '').isEmpty) {
        return 'Vui lòng nhập số điện thoại';
      }
      // regex phone
      if ((sdtTxt ?? '').validatePhoneNumber() == false) {
        return 'Số điện thoại không hợp lệ ';
      }
    }
    return null;
  }

  String? validateBsx() {
    if (isValidateBsx == true) {
      if (bienSoXeTxt == null || (bienSoXeTxt ?? '').isEmpty) {
        return 'Vui lòng nhập biển số xe';
      }
      // regex biển số xe máy dạng XXX-XXX.XX
      if ((bienSoXeTxt ?? '').validateBienXeOto() == false) {
        return 'Biển số xe không hợp lệ';
      }
    }
    return null;
  }

  void _onUpdateFormRequest() {
    insuranceCreateOrdRequestForm = insuranceCreateOrdRequestForm.copyWith(
        chuXe: tenChuXeTxt,
        bienSoXe: bienSoXeTxt,
        soKhung: soKhungxt,
        chuHd: tenChuHDTxt,
        phoneNumberHd: sdtTxt,
        email: emailTxt,
        diaChiHd: diaChiTxt);
  }

  Future<void> _openWebPayInsurance(BuildContext context) async {
    try {
      if (urlPayInsurance == null || (urlPayInsurance ?? '').isEmpty) {
        dialogAsk(context, 'Lỗi không tìm thấy trang thanh toán!');
      } else {
        // back về home
        Navigator.of(context).popUntil(ModalRoute.withName("/shopping_home"));
        // update add deep_link
        int? _appId = AppStorage.instance.getInt2(SKeysPK.appId) ??
            Get.find<ShoppingController>().appId;
        urlPayInsurance = (urlPayInsurance ?? '') + '${_appId?.getDeepLink}';
        //
        await launchUrl(Uri.parse(urlPayInsurance ?? ''),
            mode: LaunchMode.externalApplication);
      }
    } catch (e) {
      dialogAsk(context, 'Lỗi không tìm thấy trang thanh toán!');
    }
  }

  Future<void> _onCreateOrder({required BuildContext context}) async {
    _onUpdateFormRequest();
    isLoadingBtn.toggle();
    BaseResponse<InsuranceCreateOrdResponse> res =
        await DelayLoadingCallApi.delayCallApi(ShoppingRepository.instance
            .postCreateOrder(formRequest: insuranceCreateOrdRequestForm));
    isLoadingBtn.toggle();
    if (res.code == CODE_SUCCESS) {
      urlPayInsurance = res.data?.returnUrl;
      await _openWebPayInsurance(context);
    } else {
      dialogAsk(context, res.message);
    }
  }
}

class CarStepCofirmControllerUpdateKey {
  static const String updateCheckboxConfirm = '/update_check_box_cofirm';
  static const String updateBienSoXeOto = '/update_bien_so_xe_oto';
}
