import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:flutter_widget_from_html/flutter_widget_from_html.dart';
import 'package:get/get.dart';
import 'package:shopping/constants/app_assets_paths.dart';
import 'package:shopping/constants/app_colors.dart';
import 'package:shopping/constants/app_text_style.dart';
import 'package:webview_flutter/webview_flutter.dart';

class NewsDetailView extends StatelessWidget {
  const NewsDetailView({super.key, required this.webLink});
  final String webLink;
  @override
  Widget build(BuildContext context) {
    return Scaffold(
        backgroundColor: AppColors.white,
        appBar: AppBar(
          leading: GestureDetector(
            child: Container(
              width: 20,
              height: 20,
              color: Colors.transparent,
              child: SvgPicture.asset(SvgPath.svgArrowBack,
                  colorFilter: ColorFilter.mode(AppColors.black, BlendMode.srcIn),
                   fit: BoxFit.scaleDown),
                  
            ),
            onTap: () async {
              Navigator.pop(context);
            },
          ),
          backgroundColor: AppColors.white,
          elevation: 5,
          shadowColor: AppColors.black.withOpacity(0.4),
          surfaceTintColor: AppColors.white,
          title: Text(
            'TIN TỨC',
            style: AppTextStyle.s16Bold.copyWith(color: AppColors.black),
          ),
          centerTitle: true,
        ),
        body: SingleChildScrollView(
          child: Padding(
            padding: EdgeInsets.symmetric(vertical: 15, horizontal: 15),
            child: HtmlWidget(webLink),
          ),
        )
        // WebViewScreen(link: webLink),
        );
  }
}

class WebViewScreen extends StatefulWidget {
  final String link;
  WebViewScreen({Key? key, required this.link}) : super(key: key);

  @override
  State<WebViewScreen> createState() => _WebViewScreenState();
}

class _WebViewScreenState extends State<WebViewScreen> {
  late WebViewController controller;
  Rx<bool> isError = false.obs;

  @override
  initState() {
    super.initState();
    _initWeb(widget.link);
  }

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      debugShowCheckedModeBanner: false,
      theme: ThemeData(
        primarySwatch: Colors.blue,
      ),
      home: Scaffold(
        body: SafeArea(
            child: Obx(
          () => isError.value
              ? Container(
                  color: Colors.white,
                  child: Center(
                    child: Text(
                      'error loading the page'.tr,
                      style: const TextStyle(
                          fontSize: 20, fontWeight: FontWeight.w400),
                    ),
                  ),
                )
              : WebViewWidget(controller: controller),
        )),
      ),
    );
  }

  _initWeb(String link) {
    if (Uri.parse(link).isAbsolute) {
      controller = WebViewController()
        ..setJavaScriptMode(JavaScriptMode.unrestricted)
        ..setBackgroundColor(Colors.white)
        ..setNavigationDelegate(
          NavigationDelegate(
            onProgress: (int progress) {},
            onPageStarted: (String url) {},
            onPageFinished: (String url) {},
            onWebResourceError: (WebResourceError error) {},
            onNavigationRequest: (NavigationRequest request) {
              return NavigationDecision.navigate;
            },
          ),
        )
        ..loadRequest(Uri.parse(link));
    } else {
      isError.value = true;
    }
  }
}
