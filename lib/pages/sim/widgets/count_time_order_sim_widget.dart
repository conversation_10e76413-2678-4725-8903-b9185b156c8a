import 'package:flutter/material.dart';
import 'package:shopping/constants/app_colors.dart';
import 'package:shopping/constants/app_text_style.dart';
import 'package:shopping/utils/convert/time_util.dart';

class CountTimeOrderSimWidget extends StatelessWidget {
  const CountTimeOrderSimWidget({super.key, required this.time});
  final int time;
  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(5),
      decoration: BoxDecoration(
        color: AppColors.redF5D7D3,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Center(
        child: RichText(
          textAlign: TextAlign.center,
          text: TextSpan(
              text:
                  'Vui lòng điền thông tin và hoàn thiện đơn hàng trong vòng ',
              style: AppTextStyle.s13Medium.copyWith(
                color: AppColors.black2E2E2E,
              ),
              children: <TextSpan>[
                TextSpan(
                  text: TimeUtil.formatTime(time: time),
                  style: AppTextStyle.s13Bold.copyWith(
                    color: AppColors.redCE3722,
                  ),
                ),
              ]),
        ),
      ),
    );
  }
}
