import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:infinite_scroll_pagination/infinite_scroll_pagination.dart';
import 'package:shopping/models/order.dart';
import 'package:shopping/tabs/order/order_controller.dart';
import 'package:shopping/tabs/order/widgets/empty_order_widget.dart';
import 'package:shopping/tabs/order/widgets/order_widget.dart';
import 'package:shopping/widgets/app_loading_widget.dart';

class RefundOrderTab extends GetView<OrderController> {
  @override
  Widget build(BuildContext context) {
    return PagedListView.separated(
      keyboardDismissBehavior: ScrollViewKeyboardDismissBehavior.onDrag,
      pagingController: controller.refundController,
      builderDelegate: PagedChildBuilderDelegate<Order>(
        itemBuilder: (c, i, index) => OrderWidget(order: i),
        noItemsFoundIndicatorBuilder: (context) => EmptyOrderWidget(),
        firstPageErrorIndicatorBuilder: (context) => AppLoadingWidget(),
        newPageProgressIndicatorBuilder: (context) => AppLoadingWidget(),
      ),
      separatorBuilder: (c, i) => SizedBox(
        height: 20,
      ),
    );
  }
}
