import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:shopping/app_services/app_storage.dart';
import 'package:shopping/constants/app_assets_paths.dart';
import 'package:shopping/constants/app_colors.dart';
import 'package:shopping/constants/app_text_style.dart';
import 'package:shopping/models/home_model.dart';
import 'package:shopping/models/list_sim_response.dart';
import 'package:shopping/pages/sim/item_sim_action.dart';

class ItemSimWidget extends StatelessWidget {
  const ItemSimWidget({super.key, required this.itemSim, this.homeData});
  final ItemSim itemSim;
  final HomeData? homeData;
  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () {
        // set new value storage
        AppStorage.instance.setBool2(
            SKeysPK.isBookSimFromHome, homeData != null ? true : false);
        // buy SIM
        ItemSimAction.openSheetBookSim(
          context: context,
          itemSim: itemSim,
          homeData: homeData,
        );
      },
      child: Container(
        color: AppColors.blueD6EEF1,
        height: 63,
        padding: EdgeInsets.symmetric(horizontal: 10),
        child: Row(
          children: [
            Text(itemSim.msisdn ?? '',
                style: AppTextStyle.s12SemiBold
                    .copyWith(height: 15 / 12, color: AppColors.black)),
            Spacer(),
            Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                PriceTagWidget(price: '0'),
                SizedBox(height: 3),
                PriceTagWidget(
                  price: '50.000',
                  priceStyle: AppTextStyle.s13Regular.copyWith(
                      color: AppColors.black,
                      decoration: TextDecoration.lineThrough),
                  tagStyle: AppTextStyle.s8.copyWith(
                      fontWeight: FontWeight.w500, color: AppColors.black),
                )
              ],
            ),
            SizedBox(width: 24),
            GestureDetector(
              onTap: () {
                // set new value storage
                AppStorage.instance.setBool2(
                    SKeysPK.isBookSimFromHome, homeData == null ? false : true);
                // buy SIM
                ItemSimAction.openSheetBookSim(
                  context: context,
                  itemSim: itemSim,
                  homeData: homeData,
                );
              },
              child: SvgPicture.asset(SvgPath.svgButtonCard),
            )
          ],
        ),
      ),
    );
  }
}

class PriceTagWidget extends StatelessWidget {
  const PriceTagWidget(
      {super.key, required this.price, this.priceStyle, this.tagStyle});
  final String price;
  final TextStyle? priceStyle;
  final TextStyle? tagStyle;
  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          price,
          style: priceStyle ??
              AppTextStyle.s13SemiBold.copyWith(color: AppColors.black),
        ),
        Text(
          'đ',
          style: tagStyle ??
              AppTextStyle.s8.copyWith(
                  fontWeight: FontWeight.w600, color: AppColors.black),
        ),
      ],
    );
  }
}

class LableListSIMWidget extends StatelessWidget {
  const LableListSIMWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 42,
      padding: EdgeInsets.symmetric(horizontal: 10),
      decoration: BoxDecoration(
          color: AppColors.pinkF5D7D3,
          borderRadius: BorderRadius.only(
              topLeft: Radius.circular(14), topRight: Radius.circular(14))),
      child: Row(
        children: [
          Text(
            'Số điện thoại'.toUpperCase(),
            style: AppTextStyle.s11Medium
                .copyWith(color: AppColors.black, height: 13 / 11),
          ),
          Spacer(),
          Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Text(
                'Phí chọn số'.toUpperCase(),
                style: AppTextStyle.s11Medium
                    .copyWith(color: AppColors.black, height: 13 / 11),
              ),
              Text(
                '(Chưa bao gồm giá SIM, gói cước)',
                style: AppTextStyle.s9.copyWith(
                    color: AppColors.black,
                    height: 13 / 11,
                    fontWeight: FontWeight.w500),
              ),
            ],
          ),
          SizedBox(width: 54)
        ],
      ),
    );
  }
}
