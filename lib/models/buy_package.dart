class BuyPackageResponse {
  BuyPackage? data;
  BuyPackageResponse({
    this.data,
  });
  BuyPackageResponse.fromJson(Map<String, dynamic> json) {
    data = BuyPackage.fromJson(json["data"]);
  }
}

class BuyPackage {
  int? orderId;

  BuyPackage({
    this.orderId,
  });

  BuyPackage.fromJson(Map<String, dynamic> json) {
    orderId = json["order_id"];
  }
}

class ValidateBuyPackageResponse {
  ValidateBuyPackage? data;

  ValidateBuyPackageResponse({
    this.data,
  });

  ValidateBuyPackageResponse.fromJson(Map<String, dynamic> json) {
    data = ValidateBuyPackage.fromJson(json["data"]);
  }
}

class ValidateBuyPackage {
  int? orderId;
  String? messages;
  String? orderCode;

  ValidateBuyPackage({this.orderId, this.messages, this.orderCode});

  ValidateBuyPackage.fromJson(Map<String, dynamic> json) {
    orderId = json["order_id"];
    orderCode = json["order_code"];
    messages = json["messages"];
  }
}
