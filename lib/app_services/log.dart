import 'dart:convert';
import 'dart:io';
import 'dart:math';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:dart_ipify/dart_ipify.dart';
class UDPLog {
  late String app_name;
  late String app_ver;
  late String app_build_number;
  late String app_type;
  late String user_id = "Nan";
  late String device_info = "";
  late String ipV4 = "NaN";
  late String ipV6 = "NaN";
  UDPLog._privateConstructor();

  static final UDPLog instance = UDPLog._privateConstructor();


  Future<bool> init_udp() async {
    PackageInfo packageInfo = await  PackageInfo.fromPlatform();
    app_name = packageInfo.appName;
    app_ver = packageInfo.version;
    app_build_number = packageInfo.buildNumber;
    get_ip_v4();
    get_ip_v6();
    return true;
  }

  void get_ip_v4() async{
    ipV4 = await Ipify.ipv4();
  }
  void get_ip_v6() async {
    ipV6 = await Ipify.ipv64();
  }


  void setUserID(String _user_id){
    user_id = _user_id;
  }
  void sendMessage(String msg) async{
    var _msg = '{"project":"MuaDi","msg":"------------------------------------------------------\\n${app_name} - Version ${app_ver} - Build Number ${app_build_number}\\nIPV4:${ipV4}\\nIPV6:${ipV6}\\nUserID:${user_id}\\n${msg}\\n------------------------------------------------------"}';
    Random random = new Random();
    int randomNumber = random.nextInt(100);
    Future.wait([RawDatagramSocket.bind(InternetAddress.anyIPv4, 2000 + randomNumber)])
        .then((values) {
      RawDatagramSocket udpSocket = values[0];
      udpSocket.listen((RawSocketEvent e) {
        switch (e) {
          case RawSocketEvent.read:
            Datagram dg = udpSocket.receive()!;
            dg.data.forEach((x) => print(x));
            break;
          case RawSocketEvent.write:
            udpSocket.send(new Utf8Codec().encode(_msg), new InternetAddress('***************'), 44953);
            udpSocket.close();
            break;
          case RawSocketEvent.closed:
            print('Client disconnected.');
        }
      });
    }).onError((error, stackTrace) {
      print(error);
    });

  }


}

class Log {

  static void d(String message) {
    print(message);
  }
  static void i(String message) {
    print(message);
  }

  static void w(String message) {
    print(message);
  }

  static void e(String message) {
    print(message);
    UDPLog.instance.sendMessage(message);
  }

}
