class InsuranceFilterResponse {
  Data? data;

  InsuranceFilterResponse({
    this.data,
  });

  factory InsuranceFilterResponse.fromJson(Map<String, dynamic> json) =>
      InsuranceFilterResponse(
        data: json["data"] == null ? null : Data.fromJson(json["data"]),
      );

  Map<String, dynamic> toJson() => {
        "data": data?.toJson(),
      };
}

class Data {
  List<LoaiXe>? loaiXe;
  List<MaGiamGia>? maGiamGia;
  List<SpMuaThem>? spMuaThem;

  Data({
    this.loaiXe,
    this.maGiamGia,
    this.spMuaThem,
  });

  factory Data.fromJson(Map<String, dynamic> json) => Data(
        loaiXe: json["loai_xe"] == null
            ? []
            : List<LoaiXe>.from(
                json["loai_xe"]!.map((x) => LoaiXe.fromJson(x))),
        maGiamGia: json["ma_giam_gia"] == null
            ? []
            : List<MaGiamGia>.from(
                json["ma_giam_gia"]!.map((x) => MaGiamGia.fromJson(x))),
        spMuaThem: json["sp_mua_them"] == null
            ? []
            : List<SpMuaThem>.from(
                json["sp_mua_them"]!.map((x) => SpMuaThem.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "loai_xe": loaiXe == null
            ? []
            : List<dynamic>.from(loaiXe!.map((x) => x.toJson())),
        "ma_giam_gia": maGiamGia == null
            ? []
            : List<dynamic>.from(maGiamGia!.map((x) => x.toJson())),
        "sp_mua_them": spMuaThem == null
            ? []
            : List<dynamic>.from(spMuaThem!.map((x) => x.toJson())),
      };
}

class LoaiXe {
  String? id;
  String? name;

  LoaiXe({
    this.id,
    this.name,
  });

  factory LoaiXe.fromJson(Map<String, dynamic> json) => LoaiXe(
        id: json["id"],
        name: json["name"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "name": name,
      };
  bool get isXeChoHang {
    return this.id == 'H';
  }
}

class MaGiamGia {
  String? id;
  String? name;

  MaGiamGia({
    this.id,
    this.name,
  });

  factory MaGiamGia.fromJson(Map<String, dynamic> json) => MaGiamGia(
        id: json["id"],
        name: json["name"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "name": name,
      };
}

class SpMuaThem {
  String? code;
  String? name;
  String? description;

  SpMuaThem({
    this.code,
    this.name,
    this.description,
  });

  factory SpMuaThem.fromJson(Map<String, dynamic> json) => SpMuaThem(
        code: json["code"],
        name: json["name"],
        description: json["description"],
      );

  Map<String, dynamic> toJson() => {
        "code": code,
        "name": name,
        "description": description,
      };
}
