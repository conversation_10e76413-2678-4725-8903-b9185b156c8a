import 'package:flutter/material.dart';
import 'package:shopping/constants/app_colors.dart';
import 'package:shopping/constants/app_text_style.dart';

const empty_text = 'Không được để trống';

class AppTextFieldNumber extends StatefulWidget {
  const AppTextFieldNumber({
    super.key,
    this.controller,
    this.hintText,
    this.focusNode,
    this.validate,
  });
  final TextEditingController? controller;
  final String? hintText;
  final FocusNode? focusNode;
  final bool? validate;

  @override
  State<AppTextFieldNumber> createState() => _AppTextFieldNumberState();
}

class _AppTextFieldNumberState extends State<AppTextFieldNumber> {
  bool? val;
  @override
  void initState() {
    super.initState();
    val = false;
  }

  @override
  Widget build(BuildContext context) {
    return TextFormField(
      onChanged: (value) {
        if (value == '') {
          setState(() {
            val = true;
          });
        } else {
          setState(() {
            val = false;
          });
        }
      },
      onFieldSubmitted: (value) {
        if (value == '') {
          setState(() {
            val = true;
          });
        } else {
          setState(() {
            val = false;
          });
        }
      },
      keyboardType: TextInputType.number,
      controller: widget.controller,
      maxLength: 12,
      decoration: InputDecoration(
        errorText: val == true ? empty_text : null,
        labelText: '',
        isDense: true,
        contentPadding: const EdgeInsets.symmetric(
          horizontal: 10,
          vertical: 12,
        ),
        hintText: widget.hintText,
        hintStyle: AppTextStyle.s14.copyWith(color: AppColors.black2E2E2E, height: 20 / 14),
        counterText: '',
        errorBorder: OutlineInputBorder(
          borderSide: const BorderSide(width: 1, color: Colors.red),
          borderRadius: BorderRadius.circular(10),
        ),
        border: OutlineInputBorder(
          borderSide: const BorderSide(width: 1, color: AppColors.blue005BF9),
          borderRadius: BorderRadius.circular(10),
        ),
        enabledBorder: OutlineInputBorder(
          borderSide: const BorderSide(width: 1, color: AppColors.blue005BF9),
          borderRadius: BorderRadius.circular(10),
        ),
        focusedBorder: OutlineInputBorder(
          borderSide: const BorderSide(width: 1, color: AppColors.blue005BF9),
          borderRadius: BorderRadius.circular(10),
        ),
      ),
    );
  }
}

class CustomAppTextField extends StatelessWidget {
  const CustomAppTextField({super.key, this.controller, this.hintText, this.maxLength, this.focusNode});
  final TextEditingController? controller;
  final String? hintText;
  final int? maxLength;
  final FocusNode? focusNode;
  @override
  Widget build(BuildContext context) {
    return TextFormField(
      style: AppTextStyle.s14Medium.copyWith(color: AppColors.black2E2E2E, height: 20 / 14),
      keyboardType: TextInputType.number,
      controller: controller,
      maxLength: maxLength,
      decoration: InputDecoration(
        isDense: true,
        contentPadding: const EdgeInsets.symmetric(
          horizontal: 12,
          vertical: 13,
        ),
        hintText: hintText,
        hintStyle: AppTextStyle.s14Medium.copyWith(color: AppColors.black2E2E2E, height: 20 / 14),
        counterText: '',
        enabledBorder: OutlineInputBorder(
          borderSide: const BorderSide(width: 1, color: AppColors.greyD9D9D9),
          borderRadius: BorderRadius.circular(8),
        ),
        focusedBorder: OutlineInputBorder(
          borderSide: const BorderSide(width: 1, color: AppColors.greyD9D9D9),
          borderRadius: BorderRadius.circular(8),
        ),
      ),
    );
  }
}
