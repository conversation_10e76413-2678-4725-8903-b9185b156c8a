

import 'package:shopping/models/user_Info.dart';

class SSOData {
  String? accessToken;
  String? refreshToken;
  UserInfo? userInfo;

  SSOData({this.accessToken, this.refreshToken, this.userInfo});


  SSOData.fromJson(Map<String, dynamic> json) {
    accessToken = json['accessToken'];
    refreshToken = json['refreshToken'];
    userInfo = json['userInfo'] != null
        ? new UserInfo.fromJson(json['userInfo'])
        : null;
  }

}