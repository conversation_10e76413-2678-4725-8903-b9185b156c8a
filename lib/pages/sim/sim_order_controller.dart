import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:shopping/apis/ShoppingRepository.dart';
import 'package:shopping/constants/enum_receiving_method_widget.dart';
import 'package:shopping/constants/enum_step_ord_sim.dart';
import 'package:shopping/enum/enum_sim_type.dart';
import 'package:shopping/models/buy_sim_models.dart';
import 'package:shopping/models/pgd_address.dart';
import 'package:shopping/models/product.dart';
import 'package:shopping/models/province.dart';
import 'package:shopping/models/sim_model.dart';
import 'package:shopping/pages/sim/item_sim_action.dart';
import 'package:shopping/pages/sim/sim_pay_screen.dart';
import 'package:shopping/pages/sim/step_pick_product_controller.dart';
import 'package:shopping/utils/extension/string_extension.dart';
import 'package:shopping/widgets/app_dialog.dart';

class SimOrderController extends GetxController {
  SimOrderController(
      {this.ordId,
      this.product,
      required this.context,
      required this.phoneNumber,
      required this.userPhoneNumber});
  // line step status bar
  final List<StepOrdSimStatus> listStepOrdName = StepOrdSimStatus.values;
  ReceivingMethodByType? receivingMethodChoosed =
      ReceivingMethodByType.specificLocation;
  SIMOrder? simOrder;
  //address
  List<Address> listProvince = <Address>[].obs;
  List<int?> listIntAddress = <int?>[].obs;
  RxMap<int, List<Address>>? listAddress;

  RxMap<int, List<PhongGiaoDich>>? listPhongGiaoDich;
  var isValidateName = false.obs;
  RxBool isValidateEmail = false.obs;
  final GlobalKey<FormState> pollKey = GlobalKey<FormState>();

  // use to create
  final int? ordId;
  final Product? product;
  // use to show info
  final String? phoneNumber;
  final String? userPhoneNumber;
  final BuildContext context;
  FilterResponse? filterResponse;
  Rx<String> messErr = ''.obs;
  Rx<bool> isLoading = false.obs;
  // count time
  StepPickProductController stepPickProductController =
      StepPickProductController();
  // eSIM
  SimType simTypeChoosed = SimType.physical;

  /// use for search address
  // cấp tỉnh
  final TextEditingController textEditingCityController =
      TextEditingController();
  RxList<Address> listCity = <Address>[].obs;
  ValueNotifier<Address?> selectedCityNotifier = ValueNotifier<Address?>(null);
  // cấp huyện
  final TextEditingController textEditingDistrictController =
      TextEditingController();
  RxList<Address> listDistrict = <Address>[].obs;
  ValueNotifier<Address?> selectedDistrictNotifier =
      ValueNotifier<Address?>(null);
  // cấp xã
  final TextEditingController textEditingVillageController =
      TextEditingController();
  RxList<Address> listVillage = <Address>[].obs;
  ValueNotifier<Address?> selectedVillageNotifier =
      ValueNotifier<Address?>(null);
  // cấp phố
  final TextEditingController textEditingHamletController =
      TextEditingController();
  RxList<Address> listHamlet = <Address>[].obs;
  ValueNotifier<Address?> selectedHamletNotifier =
      ValueNotifier<Address?>(null);
  // cấp phòng giao dịch
  final TextEditingController textEditingPGDController =
      TextEditingController();
  RxList<PhongGiaoDich> listPGD = <PhongGiaoDich>[].obs;
  ValueNotifier<PhongGiaoDich?> selectedPGDNotifier =
      ValueNotifier<PhongGiaoDich?>(null);
  //
  @override
  void onInit() async {
    super.onInit();
    //
    _clearData();
    //
    // step pick controller
    if (Get.isRegistered<StepPickProductController>()) {
      stepPickProductController = Get.find<StepPickProductController>();
    }
    simOrder = new SIMOrder();
    listAddress = RxMap({0: [], 1: [], 2: [], 3: [], 4: []});
    listPhongGiaoDich = RxMap({0: []});
    setListAddressIndex();
    getListAddress(isAll: true);
    //
    initFilter();
    simOrder = simOrder?.copyWith(orderId: ordId);
    simOrder = simOrder?.copyWith(productId: product?.id);
    simOrder = simOrder?.copyWith(productName: product?.name);
    simOrder = simOrder?.copyWith(giaGoi: product?.price);
    simOrder = simOrder?.copyWith(phoneNumber: phoneNumber);
    simOrder = simOrder?.copyWith(userPhoneNumber: userPhoneNumber);
    simOrder = simOrder?.copyWith(fullName: "");
    simOrder = simOrder?.copyWith(diachi: "");
    _setupDataAddress();
  }

  void runValidate() {
    isValidateName.value = true;
    if (simTypeChoosed == SimType.eSim) {
      isValidateEmail.value = true;
    }
    update();
  }

  String? emailValidateErr() {
    if (isValidateEmail.value == false) {
      return null;
    }
    if (simOrder?.email?.isEmpty == true) {
      return 'Email không được để trống';
    } else if ((simOrder!.email ?? '').validateEmail()) {
      return 'Địa chỉ email không hợp lệ';
    }
    return null;
  }

  void checkDroppDown() {
    pollKey.currentState!.validate();
  }

  void setListAddressIndex() {
    listIntAddress.add(0);
    listIntAddress.add(0);
    listIntAddress.add(0);
    listIntAddress.add(0);
    listIntAddress.add(0);
  }

  void setFullName(String fullName) {
    simOrder = simOrder?.copyWith(fullName: fullName);
  }

  void setAddress(String address) {
    simOrder = simOrder?.copyWith(diachi: address);
  }

  void setEmail(String newEmail) {
    simOrder = simOrder?.copyWith(email: newEmail);
  }

  resetData() {
    listIntAddress.clear();
    clearData();
  }

  clearData() {
    simOrder = simOrder?.copyWith(nameProvince: "");
    simOrder = simOrder?.copyWith(nameQuan: "");
    simOrder = simOrder?.copyWith(namePhuong: "");
    simOrder = simOrder?.copyWith(namePho: "");
    simOrder = simOrder?.copyWith(diachi: "");
    simOrder = simOrder?.copyWith(namePhongGd: "");
    simOrder = simOrder?.copyWith(namePho: "");
    _clearData();
  }

  getListAddress({bool? isInit, int? index, bool? isAll}) async {
    for (int i = (index != null ? index + 1 : 0);
        i < listIntAddress.length;
        i++) {
      if (i == 0) {
        listAddress?[0]?.clear();
        listAddress?[0]?.addAll(await getAddressByIndex());
      } else {
        if (isAll == true) {
          if (i == 4) {
            listPhongGiaoDich?[0]?.clear();
            listPhongGiaoDich?[0]?.addAll(await getPhongGiaoDich(
                cityId: listIntAddress[0], districtId: listIntAddress[1]));
          }
          if (listIntAddress[i - 1] != null) {
            listAddress?[i]?.clear();
            listAddress?[i]?.addAll(
                await getAddressByIndex(index: i, id: listIntAddress[i - 1]));
          } else {
            listAddress?[i]?.clear();
          }
        } else {
          if (i == index! + 1) {
            if (i == 4 &&
                receivingMethodChoosed ==
                    ReceivingMethodByType.tradingLocation) {
              listPhongGiaoDich?[0]?.clear();
              selectedPGDNotifier.value = null;
              listPhongGiaoDich?[0]?.addAll(await getPhongGiaoDich(
                  isInit: true,
                  cityId: listIntAddress[0],
                  districtId: listIntAddress[1]));
            } else {
              listAddress?[i]?.clear();
              selectedPGDNotifier.value = null;
              listAddress?[i]?.addAll(await getAddressByIndex(
                  isInit: true, index: i, id: listIntAddress[i - 1]));
            }
          } else {
            listAddress?[i]?.clear();
          }
        }
      }
    }
    _setupDataAddress();
    update();
  }

  getAddressByIndex({bool? isInit, int? index, int? id}) async {
    var result;
    var res = await ShoppingRepository.instance
        .get_address_info2(index: index, id: id);
    if (res.code == 0) {
      result = res.data;
      if (isInit != null && res.data!.isEmpty) {
        List<Address> list = [];
        var address = new Address(
            id: -20, name: "Không có dữ liệu để hiển thị", itId: "-100");
        list.add(address);
        result = list;
      }
    } else {
      result = [];
    }
    return result;
  }

  getPhongGiaoDich({bool? isInit, int? cityId, int? districtId}) async {
    var result;

    var res = await ShoppingRepository.instance
        .get_trading_location(cityId: cityId, districtId: districtId);
    if (res.statusCode == 200) {
      result = res.data?.data.items;
      if (isInit != null && res.data!.data.items.isEmpty) {
        List<PhongGiaoDich> list = [];
        var pgd = new PhongGiaoDich(
            idPgd: -10,
            name: "Không có phòng giao dịch tại khu vực này",
            address: "");
        list.add(pgd);
        result = list;
      }
    } else {
      result = [];
    }
    return result;
  }

  void pickAddress({int? index, Address? value}) {
    print("index ${index} value ${value?.id} name ${value?.name}");
    if (index == 0) {
      simOrder = simOrder?.copyWith(nameProvince: value?.name ?? "");
      selectedCityNotifier.value = value;
    }
    if (index == 1) {
      simOrder = simOrder?.copyWith(nameQuan: value?.name ?? "");
      simOrder = simOrder?.copyWith(quanItId: int.parse(value?.itId ?? ""));
      selectedDistrictNotifier.value = value;
    }
    if (index == 2) {
      simOrder = simOrder?.copyWith(namePhuong: value?.name ?? "");
      simOrder = simOrder?.copyWith(phuongItId: int.parse(value?.itId ?? ""));
      selectedVillageNotifier.value = value;
    }
    if (index == 3) {
      simOrder = simOrder?.copyWith(namePho: value?.name ?? "");
      simOrder = simOrder?.copyWith(phoItId: int.parse(value?.itId ?? ""));
      selectedHamletNotifier.value = value;
    }
    int? id = (listAddress?[index])
        ?.firstWhere((element) => element.id == value?.id)
        .id;
    for (var i = 0; i < 5; i++) {
      if (i == index) {
        listIntAddress[i] = id;
      } else if (i > index!) {
        listIntAddress[i] = -1;
      }
    }
    getListAddress(isInit: true, index: index);
    setAddressToCustomer();
    _setupDataAddress();
  }

  setUpInfoCustomer() {
    simOrder = simOrder?.copyWith(
        hinhThucNhanSim: receivingMethodChoosed == null // esim
            ? 1
            : receivingMethodChoosed?.getValue);
    _updatePhiGiaoHang();
    _updateGiaSim();
  }

  void _updatePhiGiaoHang() {
    if (simTypeChoosed == SimType.eSim || receivingMethodChoosed == null) {
      simOrder = simOrder?.copyWith(phiGiaoHang: 0);
    } else if (receivingMethodChoosed ==
        ReceivingMethodByType.specificLocation) {
      simOrder = simOrder?.copyWith(
          phiGiaoHang: filterResponse?.item!.giaSim?.phiGiaoHangTaiNha);
    } else if (receivingMethodChoosed ==
        ReceivingMethodByType.tradingLocation) {
      simOrder = simOrder?.copyWith(
          phiGiaoHang: filterResponse?.item!.giaSim?.phiGiaoHangTaiQuay);
    }
  }

  void _updateGiaSim() {
    // simOrder = simOrder?.copyWith(
    //     giaSim: filterResponse?.item?.giaSim?.getGiaSim(simTypeChoosed));
    if (simTypeChoosed == SimType.eSim) {
      simOrder =
          simOrder?.copyWith(giaSim: filterResponse?.item?.giaSim?.giaESim);
    } else if (simTypeChoosed == SimType.physical) {
      simOrder =
          simOrder?.copyWith(giaSim: filterResponse?.item?.giaSim?.giaSimVatLy);
    }
  }

  bool validate() {
    final enableName = simOrder?.fullName?.trim().isNotEmpty == true;
    final enableDiaChi = simOrder?.hinhThucNhanSim == 1
        ? true
        : simOrder?.namePhongGd?.isNotEmpty == true;
    final enabletTinh = simOrder?.nameProvince?.isNotEmpty == true;
    final enabletQuan = simOrder?.nameQuan?.isNotEmpty == true;
    final enabletPhuong = simOrder?.namePhuong?.isNotEmpty == true;
    final enabletAp = simOrder?.namePho?.isNotEmpty == true;
    final enabletEmail = simTypeChoosed == SimType.physical
        ? true
        : (simOrder?.email ?? '').validateEmail() == false;
    final totalEnable = enabletAp &&
        enabletPhuong &&
        enabletQuan &&
        enabletTinh &&
        enableName &&
        enableDiaChi == true &&
        enabletEmail;
    return totalEnable;
  }

  setAddressToCustomer() {
    if (listIntAddress[0] != null && listIntAddress[0] != -1) {
      simOrder = simOrder?.copyWith(tinhId: listIntAddress[0]);
    }
    if (listIntAddress[1] != null && listIntAddress[1] != -1) {
      simOrder = simOrder?.copyWith(quanId: listIntAddress[1]);
    } else {
      simOrder = simOrder?.copyWith(quanId: -1);
    }
    if (listIntAddress[2] != null && listIntAddress[2] != -1) {
      simOrder = simOrder?.copyWith(phuongId: listIntAddress[2]);
    } else {
      simOrder = simOrder?.copyWith(phuongId: -1);
    }
    if (listIntAddress[3] != null && listIntAddress[3] != -1) {
      simOrder = simOrder?.copyWith(phoId: listIntAddress[3]);
    } else {
      simOrder = simOrder?.copyWith(phoId: -1);
    }
    if (listIntAddress[4] != null && listIntAddress[4] != -1) {
      simOrder = simOrder?.copyWith(diemGdId: listIntAddress[4]);
    } else {
      simOrder = simOrder?.copyWith(diemGdId: 0);
    }
    _setupDataAddress();
    update();
  }

  void pickPhongGiaoDich({int? index, PhongGiaoDich? value}) {
    if (index == 4) {
      simOrder = simOrder?.copyWith(namePhongGd: value?.address ?? "");
      simOrder = simOrder?.copyWith(diachi: value?.address ?? "");
      selectedPGDNotifier.value = value;
    }
    int? id = (listPhongGiaoDich?[0])
        ?.firstWhere((element) => element.idPgd == value?.idPgd)
        .idPgd;
    for (var i = 0; i < 5; i++) {
      if (i == index) {
        listIntAddress[i] = id;
      } else if (i > index!) {
        listIntAddress[i] = -1;
      }
    }
    getListAddress(isInit: true, index: index);
    setAddressToCustomer();
  }

  Future<void> onSelectReceivingMethod(
      ReceivingMethodByType? receivingMethod) async {
    receivingMethodChoosed = receivingMethod;
    _updatePhiGiaoHang();
    update([SIMOrderControllerUpdateKey.updateAddressReceive]);
  }

  Future onCreateOrderSim({required BuildContext context}) async {
    setUpInfoCustomer();
    runValidate();
    checkDroppDown();
    if (validate() == true) {
      simOrder = simOrder?.copyWith(diachi: simOrder?.getAddress(simOrder!));
      // check time
      if (stepPickProductController.countTime.value <= 0) {
        // show pop up
        dialogAsk(
          context,
          'Đã hết thời gian giữ số. Vui lòng thực hiện lại.',
          textButton: 'ĐÓNG',
          isBackAction: true,
          callOk: () {
            Navigator.of(context).pop();
            ActionBack.onBack(context: context);
            // Navigator.of(context).popUntil(ModalRoute.withName("/page_list_sim"));
          },
        );
      } else {
        Navigator.push(
            context,
            MaterialPageRoute(
                builder: (context) =>
                    SIMPayScreen(customer: simOrder ?? new SIMOrder())));
      }
    }
  }

  Future initFilter() async {
    isLoading.toggle();
    var res = await ShoppingRepository.instance.getFilter();
    messErr.value = res?.message ?? '';
    isLoading.toggle();
    if (res?.code == 0) {
      filterResponse = res?.data;
      simOrder =
          simOrder?.copyWith(giaSim: res?.data?.item?.giaSim?.giaSimVatLy);
      simOrder = simOrder?.copyWith(
          phiHoaMang: res?.data?.item?.giaSim?.phiHoaMangTraTruoc);
      _updatePhiGiaoHang();
      _updateGiaSim();
      update([SIMOrderControllerUpdateKey.updateFilter]);
    }
    if (messErr.value.isNotEmpty) {
      dialogAsk(context, messErr.value);
    }
  }

  void updateSimType(SimType? newValue) {
    isValidateEmail.value = false;
    if (newValue == null) {
      return;
    }
    simTypeChoosed = newValue;
    if (simTypeChoosed == SimType.eSim) {
      onSelectReceivingMethod(null);
    } else {
      onSelectReceivingMethod(ReceivingMethodByType.specificLocation);
    }
    simOrder = simOrder?.copyWith(loaiSim: simTypeChoosed.getValue);
    update([
      SIMOrderControllerUpdateKey.updateSimType,
      SIMOrderControllerUpdateKey.updateFilter,
    ]);
    _updateGiaSim();
  }

  bool isSimPhysical() {
    if (simTypeChoosed == SimType.eSim) {
      return false;
    }
    return true;
  }

  // update dropdown search address
  void _clearData() {
    listCity.clear();
    selectedCityNotifier.value = null;
    listDistrict.clear();
    selectedDistrictNotifier.value = null;
    listVillage.clear();
    selectedVillageNotifier.value = null;
    listHamlet.clear();
    selectedHamletNotifier.value = null;
    listPGD.clear();
    selectedPGDNotifier.value = null;
  }

  void _setupDataAddress() {
    listCity.value = listAddress?[0] ?? [];
    listDistrict.value = listAddress?[1] ?? [];
    listVillage.value = listAddress?[2] ?? [];
    listHamlet.value = listAddress?[3] ?? [];
    listPGD.value = listPhongGiaoDich?[0] ?? [];
    _setValueSelected();
  }

  void _setValueSelected() {
    if (listIntAddress[0] == null || listIntAddress[0] == -1) {
      selectedCityNotifier.value = null;
    }
    if (listIntAddress[1] == null || listIntAddress[1] == -1) {
      selectedDistrictNotifier.value = null;
    }
    if (listIntAddress[2] == null || listIntAddress[2] == -1) {
      selectedVillageNotifier.value = null;
    }
    if (listIntAddress[3] == null || listIntAddress[3] == -1) {
      selectedHamletNotifier.value = null;
    }
    if (listIntAddress[4] == null || listIntAddress[4] == -1) {
      selectedPGDNotifier.value == null;
    }
  }
}

class SIMOrderControllerUpdateKey {
  static const String updateAddressReceive =
      'sim_order_controller/update_address_receive';
  static const String updateFilter = 'sim_order_controller/update_filter';
  static const String updateSimType = 'sim_order_controller/update_sim_type';
}
