import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:shopping/constants/app_colors.dart';
import 'package:shopping/constants/app_text_style.dart';
import 'package:shopping/constants/enum_step_ord_insurance.dart';
import 'package:shopping/enum/enum_ins_land.dart';
import 'package:shopping/models/insurance_create_order_request_form.dart';
import 'package:shopping/pages/insurance/pages/land/land_step_confirm_controller.dart';
import 'package:shopping/pages/insurance/widgets/common_buy_insurance_view.dart';
import 'package:shopping/pages/package/widgets/filter_endraw_widget.dart';
import 'package:shopping/pages/sim/widgets/sim_text_field_widget.dart';

class LandStepConfirmScreen extends StatelessWidget {
  const LandStepConfirmScreen(
      {super.key, required this.insuranceCreateOrdRequestForm});
  final InsuranceCreateOrdRequestForm insuranceCreateOrdRequestForm;
  @override
  Widget build(BuildContext context) {
    return GetBuilder<LandStepConfirmController>(
      init: LandStepConfirmController(
          insuranceCreateOrdRequestForm: insuranceCreateOrdRequestForm),
      id: LandStepCofirmControllerUpdateKey.updateCheckboxConfirm,
      builder: (controller) => CommonBuyInsuranceView(
        title: 'BẢO HIỂM NHÀ TƯ NHÂN',
        stepType: StepOrdInsuranceType.confirmInfor,
        bottonWidget: GetBuilder<LandStepConfirmController>(
            id: LandStepCofirmControllerUpdateKey.updateCheckboxConfirm,
            builder: (controller) => InkWell(
                  splashColor: Colors.transparent,
                  onTap: () {
                    controller.onChangeCofirm(!controller.isConfirmed);
                  },
                  child: CheckBoxLineWidget(
                    title:
                        'Tôi xác nhận và cam đoan các thông tin trên là chính xác',
                    isSelected: controller.isConfirmed,
                    onChanged: (p0) {
                      controller.onChangeCofirm(p0);
                    },
                  ),
                )),
        lableBtn: 'THANH TOÁN',
        enableBtn: controller.isConfirmed,
        isLoadingBtn: controller.isLoadingBtn.value,
        onClickBtn: () {
          controller.onClickBtn(context: context);
        },
        body: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: EdgeInsets.only(top: 18),
              child: Text('Thông tin ngôi nhà',
                  style: AppTextStyle.s18SemiBold
                      .copyWith(color: AppColors.black)),
            ),
            const SizedBox(height: 11),
            Obx(
              () => SIMTextFieldWidget(
                title: 'Họ tên chủ ngôi nhà',
                isRequired: true,
                maxLength: 200,
                hintText: 'Nhập họ tên chủ ngôi nhà',
                text: controller.tenChuNhaTxt,
                errorText: controller.validateTenChuNha(),
                onChanged: (p0) {
                  controller.isValidateTenChuNha.value = false;
                  controller.onChangeTenChuNha(p0);
                },
                onSubmitted: (p0) {
                  controller.isValidateTenChuNha.value = true;
                  controller.onChangeTenChuNha(p0);
                },
              ),
            ),
            const SizedBox(height: 11),
            Obx(
              () => SIMTextFieldWidget(
                title: 'Số CCCD/Hộ chiếu',
                isRequired: true,
                maxLength: 50,
                hintText: 'Nhập số CCCD hoặc Hộ chiếu',
                text: controller.cccdTxt,
                errorText: controller.validateCccd(),
                onChanged: (p0) {
                  controller.isValidateCccd.value = false;
                  controller.onChangeCccd(p0);
                },
                onSubmitted: (p0) {
                  controller.isValidateCccd.value = true;
                  controller.onChangeCccd(p0);
                },
              ),
            ),
            const SizedBox(height: 11),
            Obx(
              () => SIMTextFieldWidget(
                title: 'Số điện thoại',
                isRequired: true,
                maxLength: 12,
                hintText: 'Nhập số điện thoại',
                text: controller.sdtTxt,
                keyBoardType: TextInputType.number,
                errorText: controller.validateSdt(),
                onChanged: (p0) {
                  controller.isValidateSdt.value = false;
                  controller.onChangeSdt(p0);
                },
                onSubmitted: (p0) {
                  controller.isValidateSdt.value = true;
                  controller.onChangeSdt(p0);
                },
              ),
            ),
            const SizedBox(height: 11),
            Obx(
              () => SIMTextFieldWidget(
                title: 'Địa chỉ',
                maxLength: 200,
                hintText: 'Nhập địa chỉ',
                isRequired: true,
                text: controller.diaChiNhaTxt,
                errorText: controller.validateDiaChiNha(),
                onChanged: (p0) {
                  controller.isValidateDiaChiNha.value = false;
                  controller.onChangeDiaChiNha(p0);
                },
                onSubmitted: (p0) {
                  controller.isValidateDiaChiNha.value = true;
                  controller.onChangeDiaChiNha(p0);
                },
              ),
            ),
            const SizedBox(height: 11),
            Row(
              children: [
                Flexible(
                  flex: 1,
                  child: Obx(
                    () => SIMTextFieldWidgetOnlyNumber(
                      title: 'Số tầng',
                      isRequired: true,
                      maxLength: 3,
                      hintText: 'Nhập số tầng',
                      text: controller.soTangTxt,
                      errorText: controller.validateSoTang(),
                      onChanged: (p0) {
                        controller.isValidateSoTang.value = false;
                        controller.onChangeSoTang(p0);
                      },
                      onSubmitted: (p0) {
                        controller.isValidateSoTang.value = true;
                        controller.onChangeSoTang(p0);
                      },
                    ),
                  ),
                ),
                SizedBox(width: 20),
                Flexible(
                  flex: 1,
                  child: Obx(
                    () => SIMTextFieldWidgetOnlyNumber(
                      title: 'Diện tích (m2)',
                      isRequired: true,
                      maxLength: 20,
                      hintText: 'Nhập diện tích',
                      keyBoardType:
                          TextInputType.numberWithOptions(decimal: true),
                      text: controller.dientichTxt,
                      errorText: controller.validateDienTich(),
                      onChanged: (p0) {
                        controller.isValidateDienTich.value = false;
                        controller.onChangeDienTich(p0);
                      },
                      onSubmitted: (p0) {
                        controller.isValidateDienTich.value = true;
                        controller.onChangeDienTich(p0);
                      },
                    ),
                  ),
                ),
              ],
            ),
            GetBuilder<LandStepConfirmController>(
              id: LandStepCofirmControllerUpdateKey.updateLandStatus,
              builder: (controller) => CustomCommonDropdown<LandStatus>(
                lable: 'Quyền sở hữu',
                isRequired: true,
                lableTextStyle:
                    AppTextStyle.s14Medium.copyWith(color: AppColors.black),
                contentTextStyle:
                    AppTextStyle.s14Medium.copyWith(color: AppColors.black),
                items: LandStatus.values,
                itemAsString: (p0) => p0.getName,
                value: controller.landStatusChoosed,
                onChanged: (LandStatus? newValue) {
                  controller.onChangeQuyenSH(newValue);
                },
              ),
            ),
            const SizedBox(height: 11),
            GetBuilder<LandStepConfirmController>(
                id: LandStepCofirmControllerUpdateKey.updateCheckboxVay,
                builder: (controller) => InkWell(
                      splashColor: Colors.transparent,
                      onTap: () {
                        controller.updateCheckboxVay();
                      },
                      child: CheckBoxLineWidget(
                          title: 'Đang vay thế chấp với ngân hàng',
                          isSelected: controller.checkVayTheChap,
                          onChanged: (p0) {
                            controller.updateCheckboxVay();
                          }),
                    )),
            Padding(
              padding: EdgeInsets.only(top: 11),
              child: Text(
                'Thông tin chủ hợp đồng',
                style:
                    AppTextStyle.s18SemiBold.copyWith(color: AppColors.black),
              ),
            ),
            const SizedBox(height: 11),
            SIMTextFieldWidget(
              title: 'Họ tên chủ hợp đồng',
              isRequired: true,
              maxLength: 200,
              hintText: 'Nhập họ và tên',
              text: controller.tenChuHDTxt,
              errorText: controller.validateTenChuHd(),
              onChanged: (p0) {
                controller.isValidateTenChuHD.value = false;
                controller.onChangeTenChuHD(p0);
              },
              onSubmitted: (p0) {
                controller.isValidateTenChuHD.value = true;
                controller.onChangeTenChuHD(p0);
              },
            ),
            const SizedBox(height: 11),
            SIMTextFieldWidgetOnlyNumber(
              title: 'Số điện thoại liên hệ',
              isRequired: true,
              maxLength: 12,
              hintText: 'Nhập số điện thoại liên hệ',
              keyBoardType: TextInputType.number,
              text: controller.sdtChuHDTxt,
              errorText: controller.validateSdtLH(),
              onChanged: (p0) {
                controller.isValidateSdtChuHD.value = false;
                controller.onChangeSdtLH(p0);
              },
              onSubmitted: (p0) {
                controller.isValidateSdtChuHD.value = true;
                controller.onChangeSdtLH(p0);
              },
            ),
            const SizedBox(height: 11),
            Obx(
              () => SIMTextFieldWidget(
                title: 'Email',
                maxLength: 200,
                hintText: 'Nhập email',
                text: controller.emailTxt,
                errorText: controller.validateEmail(),
                onChanged: (p0) {
                  controller.isValidateEmail.value = false;
                  controller.onChangeEmail(p0);
                },
                onSubmitted: (p0) {
                  controller.isValidateEmail.value = true;
                  controller.onChangeEmail(p0);
                },
              ),
            ),
            const SizedBox(height: 11),
            SIMTextFieldWidget(
              title: 'Địa chỉ',
              maxLength: 200,
              hintText: 'Nhập địa chỉ',
              text: controller.diaChiChuHdTxt,
              onChanged: (p0) => controller.onChangeDiaChiChuHD(p0),
              onSubmitted: (p0) => controller.onChangeDiaChiChuHD(p0),
            ),
            const SizedBox(height: 5)
          ],
        ),
      ),
    );
  }
}
