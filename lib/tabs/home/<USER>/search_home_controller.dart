import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:infinite_scroll_pagination/infinite_scroll_pagination.dart';
import 'package:loader_overlay/loader_overlay.dart';
import 'package:shopping/apis/ShoppingRepository.dart';
import 'package:shopping/app_services/api_service.dart';
import 'package:shopping/loading_screen.dart';
import 'package:shopping/models/base_response.dart';

import '../../../constants/enum_package_type.dart';
import '../../../models/product.dart';

class SearchHomeController extends GetxController {
  final BuildContext context;
  Rx<bool> isLoading = false.obs;
  SearchHomeController({required this.context});
  final List<PackageType> listTab = [
    PackageType.all,
    PackageType.data,
    PackageType.SMS,
    PackageType.combo,
  ];
  final Rx<PackageType> orderTypeChoosed = (PackageType.all).obs;
  RxMap<int, int>? listCount = RxMap();

  static int per_page = 50;
  var loading = false.obs;
  var pageIndex = 0.obs;
  var group = -1.obs;
  var keyword = ''.obs;
  TextEditingController searchController = TextEditingController();
  FocusNode focusNode = FocusNode();

  final PagingController<int, Product> pageController = PagingController(firstPageKey: 1);
  // message lỗi
  Rx<String> messageFail = ''.obs;
  @override
  void onInit() {
    pageController.addPageRequestListener((pageKey) {
      getProduct(group: orderTypeChoosed.value.getValue, keyword: keyword.value, pageKey: pageKey);
    });
    getCountProduct();
    super.onInit();
  }

  @override
  void onClose() {
    super.onClose();
    pageController.dispose();
  }

  void onDisMissKeyboard() {
    focusNode.unfocus();
  }

  void onSearch() {
    pageController.itemList = [];
    keyword.value = searchController.text.trim();
    getCountProduct();
    getProduct(group: orderTypeChoosed.value.getValue, keyword: keyword.value, pageKey: 1);
  }

  void getCountProduct() async {
    final res = await ShoppingRepository.instance.getCountProduct2(keyword: keyword.value);
    if (res.isNotEmpty) {
      listCount?.clear();
      listCount?.addAll(res);
      update();
    }
  }

  void onChangeTab(PackageType orderType) async {
    if (orderTypeChoosed.value != orderType) {
      orderTypeChoosed.value = orderType;
      getProduct(group: orderTypeChoosed.value.getValue, keyword: keyword.value, pageKey: 1);
    }
  }

  void getProduct({int? group, int? pageKey, String? keyword}) async {
    context.loaderOverlay.show();
    isLoading.toggle();
    onDisMissKeyboard();
    BaseResponse<ProductsResponse> res;
    res = await DelayLoadingCallApi.delayCallApi(ShoppingRepository.instance.get_products2(keyword: keyword, group: group, perPage: per_page, page: pageKey));
    if (res.code == CODE_SUCCESS) {
      if (res.message != null && (res.message?.isNotEmpty ?? false)) {
        messageFail.value = res.message ?? '';
        pageController.itemList?.clear();
        context.loaderOverlay.hide();
        isLoading.toggle();
      } else {
        messageFail.value = '';
        final List<Product>? listP = res.data?.items;
        final isLastPage = listP!.length < per_page;
        if (pageKey == 1) {
          pageController.itemList?.clear();
        }
        if (isLastPage) {
          pageController.appendLastPage(listP);
        } else {
          final nextPageKey = pageKey! + 1;
          pageController.appendPage(listP, nextPageKey);
        }
        context.loaderOverlay.hide();
        isLoading.toggle();
      }
    } else {
      messageFail.value = '';
      context.loaderOverlay.hide();
      isLoading.toggle();
    }
    update();
  }
}
