enum CycleType { day, week, month }

extension CycleTypeExt on CycleType {
  String get nameCycleType {
    switch (this) {
      case CycleType.day:
        return '<PERSON><PERSON><PERSON> ngày';
      case CycleType.week:
        return '<PERSON><PERSON><PERSON> tuần';
      case CycleType.month:
        return '<PERSON><PERSON><PERSON> tháng';
    }
  }
  String get getLastName {
    switch (this) {
      case CycleType.day:
        return 'ngày';
      case CycleType.week:
        return 'tuần';
      case CycleType.month:
        return 'tháng';
    }
  }

  int get getValue {
    switch (this) {
      case CycleType.day:
        return 1;
      case CycleType.week:
        return 2;
      case CycleType.month:
        return 3;
    }
  }
}

enum PriceFromType { f0T100, f100T300, f300T600, f600T1M, f1M }

extension PriceFromExt on PriceFromType {
  String get namePriceFrom {
    switch (this) {
      case PriceFromType.f0T100:
        return '0 - 100.000đ';
      case PriceFromType.f100T300:
        return '> 100.000đ - 300.000đ';
      case PriceFromType.f300T600:
        return '> 300.000đ - 600.000đ';
      case PriceFromType.f600T1M:
        return '> 600.000đ - 1.000.000đ';
      case PriceFromType.f1M:
        return '> 1.000.000đ';
    }
  }

  List<int> get getListPrice {
    switch (this) {
      case PriceFromType.f0T100:
        return [0, 100000];
      case PriceFromType.f100T300:
        return [100000, 300000];
      case PriceFromType.f300T600:
        return [300000, 600000];
      case PriceFromType.f600T1M:
        return [600000, 1000000];
      case PriceFromType.f1M:
        return [1000000];
    }
  }
}

enum OrderByType { reduce, increase }

extension OrderByExt on OrderByType {
  String get nameFilter {
    switch (this) {
      case OrderByType.reduce:
        return 'Giá từ cao đến thấp';
      case OrderByType.increase:
        return 'Giá từ thấp đến cao';
    }
  }

  String get getValue {
    switch (this) {
      case OrderByType.reduce:
        return '-price';
      case OrderByType.increase:
        return 'price';
    }
  }
}

enum TypeGoi { didong, addon }

extension TypeGoiExt on TypeGoi {
  String get nameGoi {
    switch (this) {
      case TypeGoi.didong:
        return 'Gói động';
      case TypeGoi.addon:
        return 'Gói addon';
    }
  }

  int get value {
    switch (this) {
      case TypeGoi.didong:
        return 1;
      case TypeGoi.addon:
        return 2;
    }
  }
}
