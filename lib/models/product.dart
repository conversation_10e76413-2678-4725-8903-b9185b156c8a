import 'package:get/get.dart';
import 'package:shopping/constants/enum_filter_product.dart';
import 'package:shopping/constants/enum_insurance_type.dart';
import 'package:shopping/enum/enum_payment_type.dart';
import 'package:shopping/pages/package/widgets/filter_endraw_widget.dart';
import 'package:shopping/utils/extension/int_extension.dart';

class ProductsResponse {
  List<Product>? items;
  Meta? meta;

  ProductsResponse({
    this.items,
    this.meta,
  });

  ProductsResponse.fromJson(Map<String, dynamic> json) {
    items = json["items"] == null
        ? []
        : List<Product>.from(json["items"]!.map((x) => Product.fromJson(x)));
    meta = json["_meta"] == null ? null : Meta.fromJson(json["_meta"]);
  }
}

class Product {
  int? id;
  String? name;
  String? code;
  num? price;
  int? cycle;
  int? cycleType;
  List<int>? paymentType;
  String? description;
  int? group;
  int? isHotDeal;
  ConfigDisplay? configDisplay;

  Product({
    this.id,
    this.name,
    this.code,
    this.price,
    this.cycle,
    this.cycleType,
    this.paymentType,
    this.description,
    this.group,
    this.isHotDeal,
    this.configDisplay,
  });

  Product.fromJson(Map<String, dynamic> json) {
    id = json["id"];
    name = json["name"];
    code = json["code"];
    price = json["price"];
    cycle = json["cycle"];
    cycleType = json["cycle_type"];
    paymentType = json["payment_type"] == null
        ? []
        : List<int>.from(json["payment_type"]!.map((e) => e));
    description = json["description"];
    group = json["group"];
    isHotDeal = json["is_hot_deal"];
    configDisplay = json["config_display"] == null
        ? null
        : ConfigDisplay.fromJson(json["config_display"]);
  }

  String getContent(int index) {
    String key = getKeyContent(index);
    if (key.isEmpty) return '';
    ConfigDisplayForm? position = this
        .configDisplay
        ?.positions
        ?.firstWhereOrNull((e) => e.index == index);
    if (key == 'other') return (position?.otherContent ?? '');
    if (key == 'code') return (this.code ?? '');
    if (key == 'name') return (this.name ?? '');
    if (key == 'cycle') return getLableChuKy();
    // if (key == 'price') return '${this.price?.convertToVietnamesMoney()}';
    if (key == 'price') return ConvertMoney.convertVNDMoney(this.price);
    return '';
  }

  String getKeyContent(int index) {
    // if (index > (this.configDisplay?.positions ?? []).length) {
    //   return '';
    // }
    ConfigDisplayForm? position = this
        .configDisplay
        ?.positions
        ?.firstWhereOrNull((e) => e.index == index);
    return position?.positionKey ?? '';
  }

  bool isPriceContent(int index) {
    return (getKeyContent(index) == 'price');
  }

  bool isShowIconWithPosition(int index) {
    // khi content ko rỗng và nếu là vị trí 4 thì phải có icon => true
    if (getContent(index).trim().isNotEmpty) {
      return true;
    }
    return false;
  }

  String getChuKy() {
    return '${cycle} ${FilterCondition.listCycleType.firstWhere((e) => e.getValue == cycleType).getLastName}';
  }

  String getLableChuKy() {
    if (getChuKy().trim().isEmpty) {
      return '';
    }
    return 'Chu kỳ: ${getChuKy()}';
  }

  List<String>? get getListLogo {
    return this.configDisplay?.iconImages;
  }

  // check payment type
  List<PaymentType?> get getPaymentType {
    if (paymentType == null || paymentType?.length == 0) return [];
    return (paymentType ?? [])
        .map((e) =>
            PaymentType.values.firstWhereOrNull((p0) => p0.getValue == e))
        .toList();
  }

  bool get isOnlyPayTKC {
    // nếu list rông, hoặc chỉ chứa 1 kiểu thanh toán là qua tài khoản chính
    if (getPaymentType.isEmpty ||
        (getPaymentType.any((e) => e == PaymentType.tk0d) == false &&
            getPaymentType.any((e) => e == PaymentType.tkc))) return true;
    return false;
  }

  bool get isOnlyPayOnline {
    // nếu list chỉ chứa 1 kiểu thanh toán online
    if (getPaymentType.any((e) => e == PaymentType.tkc) == false &&
        getPaymentType.any((e) => e == PaymentType.tk0d)) return true;
    return false;
  }

  // bảo hiểm
  InsuranceType? get getInsType {
    return InsuranceType.values
        .firstWhereOrNull((e) => this.group == e.getValue);
  }

  String get getLogoIconInsType {
    InsuranceType? ins = getInsType;
    if (ins == null) {
      return '';
    } else {
      return ins.getIcon;
    }
  }
}

class ConfigDisplay {
  List<ConfigDisplayForm>? positions;
  List<int>? icons;
  List<String>? iconImages;

  ConfigDisplay({this.positions, this.icons, this.iconImages});

  ConfigDisplay.fromJson(Map<String, dynamic> json) {
    positions = json["positions"] == null
        ? []
        : List<ConfigDisplayForm>.from(
            json["positions"]!.map((x) => ConfigDisplayForm.fromJson(x)));

    icons = json["icons"] == null
        ? []
        : List<int>.from(json["icons"]!.map((e) => e));
    iconImages = json["icon_images"] == null
        ? []
        : List<String>.from(json["icon_images"]!.map((x) => x));
  }
}

class ConfigDisplayForm {
  String? positionKey;
  String? otherContent;
  int? index;

  ConfigDisplayForm({
    this.positionKey,
    this.otherContent,
    this.index,
  });

  ConfigDisplayForm.fromJson(Map<String, dynamic> json) {
    positionKey = json["position_key"];
    otherContent = json["other_content"];
    index = json["index"];
  }
}

class Meta {
  int? totalCount;
  int? pageCount;
  int? currentPage;
  int? perPage;

  Meta({
    this.totalCount,
    this.pageCount,
    this.currentPage,
    this.perPage,
  });

  Meta.fromJson(Map<String, dynamic> json) {
    totalCount = json["totalCount"];
    pageCount = json["pageCount"];
    currentPage = json["currentPage"];
    perPage = json["perPage"];
  }
}

class Positions {
  String? positionKey;
  String? otherContent;
  int? index;

  Positions({this.positionKey, this.otherContent, this.index});

  Positions.fromJson(Map<String, dynamic> json) {
    positionKey = json['position_key'];
    otherContent = json['other_content'];
    index = json['index'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['position_key'] = this.positionKey;
    data['other_content'] = this.otherContent;
    data['index'] = this.index;
    return data;
  }
}
