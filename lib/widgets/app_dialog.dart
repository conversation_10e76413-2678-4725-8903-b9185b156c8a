import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:shopping/constants/app_assets_paths.dart';
import 'package:shopping/constants/app_colors.dart';
import 'package:shopping/constants/app_text_style.dart';

dialogAsk(
  context,
  message, {
  isShowLater = false,
  Function()? callOk,
  bool? isBackAction = false,
  String? textButton,
  bool? isExit = false,
}) async {
  return showDialog(
    context: context,
    barrierDismissible: true,
    builder: (BuildContext context) {
      return PopScope(
        canPop: true,
        child: Center(
          child: Wrap(
            children: [
              Material(
                borderRadius: BorderRadius.circular(12),
                child: Container(
                  width: MediaQuery.of(context).size.width - 80,
                  alignment: Alignment.center,
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  padding: const EdgeInsets.symmetric(horizontal: 15),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Container(
                        padding: EdgeInsets.only(top: 24, left: 89, right: 89, bottom: 25),
                        child: SvgPicture.asset(
                          SvgPath.svgQuestionMark,
                        ),
                      ),
                      Text(
                        "THÔNG BÁO",
                        style: AppTextStyle.s18Medium,
                      ),
                      SizedBox(
                        height: 10,
                      ),
                      Padding(
                        padding: const EdgeInsets.only(left: 5, right: 5),
                        child: Text(
                          message,
                          style: AppTextStyle.s13Regular,
                          textAlign: TextAlign.center,
                        ),
                      ),
                      const SizedBox(
                        height: 14,
                      ),
                      Row(
                        children: [
                          Visibility(
                              visible: isShowLater,
                              child: Expanded(
                                child: SizedBox(
                                  child: ElevatedButton(
                                    onPressed: () async {
                                      Navigator.of(context).pop();
                                    },
                                    style: ElevatedButton.styleFrom(backgroundColor: AppColors.greyCACACA, shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8))),
                                    child: Padding(
                                      padding: const EdgeInsets.all(12),
                                      child: Text(
                                        'Hủy',
                                        style: AppTextStyle.s16Medium.copyWith(color: Colors.white),
                                      ),
                                    ),
                                  ),
                                ),
                              )),
                          Visibility(
                              visible: isShowLater,
                              child: const SizedBox(
                                width: 8,
                              )),
                          Expanded(
                              child: SizedBox(
                            child: ElevatedButton(
                              onPressed: () async {
                                if (isBackAction == true) {
                                  callOk?.call();
                                } else {
                                  if (isExit == true) {
                                    Navigator.pop(context);
                                  }
                                  Navigator.pop(context);
                                }
                              },
                              style: ElevatedButton.styleFrom(
                                  backgroundColor: AppColors.green30AAB7,
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                  maximumSize: Size(double.infinity, 50)),
                              child: Padding(
                                padding: const EdgeInsets.all(12),
                                child: Text(
                                  textButton ?? 'ĐỒNG Ý',
                                  style: AppTextStyle.s16Medium.copyWith(color: Colors.white),
                                ),
                              ),
                            ),
                          ))
                        ],
                      ),
                      SizedBox(height: 9),
                    ],
                  ),
                ),
              )
            ],
          ),
        ),

      );
    },
  );
}
