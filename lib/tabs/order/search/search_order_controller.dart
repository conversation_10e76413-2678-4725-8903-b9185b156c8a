import 'package:get/get.dart';
import 'package:shopping/enum/enum_order_type.dart';

class SearchOrderController extends GetxController {
  /// tab
  final List<OrderType> listTab = [
    OrderType.all,
    OrderType.progress,
    OrderType.success,
    OrderType.fail,
  ];
  final Rx<OrderType> orderTypeChoosed = (OrderType.all).obs;

  @override
  void onInit() {
    super.onInit();
  }

  void onChangeTab(OrderType orderType) {
    if (orderTypeChoosed.value == orderType) {
      return;
    }
    orderTypeChoosed.value = orderType;
  }
}
