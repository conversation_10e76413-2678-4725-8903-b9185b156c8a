import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';

import 'package:shopping/constants/app_colors.dart';
import 'package:shopping/constants/app_text_style.dart';
import 'package:shopping/enum/enum_payment_type.dart';
import 'package:shopping/models/list_bank_infor_response.dart';
import 'package:shopping/pages/sim/sim_pay_controller.dart';
import 'package:shopping/widgets/app_radio_button_widget.dart';

class MethodPayWidget extends StatelessWidget {
  const MethodPayWidget({super.key, required this.controller});
  final SimPayController controller;
  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        ListView.separated(
          padding: EdgeInsets.zero,
          shrinkWrap: true,
          physics: NeverScrollableScrollPhysics(),
          itemCount: controller.listPaymentOnlineType.length,
          itemBuilder: (context, index) {
            PaymentOnlineType item = controller.listPaymentOnlineType[index] ??
                PaymentOnlineType.values[index];
            return InkWell(
              splashColor: Colors.transparent,
              onTap: () {
                controller.onChangedPaymetType(item);
              },
              child: AppRadioButtonWidget<PaymentOnlineType>(
                  item: item,
                  groupValue: controller.paymentOnlineChoosed,
                  onChanged: (PaymentOnlineType? value) {
                    controller.onChangedPaymetType(value);
                  },
                  widget: Padding(
                    padding: const EdgeInsets.symmetric(vertical: 10),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        SvgPicture.asset(item.getIconPayForSim),
                        const SizedBox(width: 18),
                        Flexible(
                          child: Text(
                            item.getNameSimPayment,
                            style: AppTextStyle.s14Medium.copyWith(
                                color: AppColors.black262626,
                                overflow: TextOverflow.ellipsis),
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ],
                    ),
                  )),
            );
          },
          separatorBuilder: (context, index) => SizedBox(
              height: index + 1 >= controller.listPaymentOnlineType.length
                  ? null
                  : 15),
        ),
        controller.isShowListBank
            ? Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Padding(
                    padding: EdgeInsets.only(top: 15),
                    child: Text("Lựa chọn một trong các ngân hàng bạn có",
                        style: AppTextStyle.s14Medium
                            .copyWith(color: AppColors.black)),
                  ),
                  const SizedBox(height: 22),
                  GridView.builder(
                      padding: EdgeInsets.zero,
                      shrinkWrap: true,
                      physics: NeverScrollableScrollPhysics(),
                      itemCount: controller.listBank.length,
                      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                        crossAxisCount: 3,
                        mainAxisSpacing: 15,
                        crossAxisSpacing: 15,
                        childAspectRatio: 107 / 49,
                      ),
                      itemBuilder: (_, index) {
                        Bank item = controller.listBank[index];
                        return InkWell(
                          splashColor: Colors.transparent,
                          onTap: () {
                            controller.onClickBank(item);
                          },
                          child: item.code == controller.bankSelected?.code
                              ? Container(
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(2),
                                    border: Border.all(
                                      color: AppColors.redCE3722,
                                    ),
                                  ),
                                  child: Image.network(item.logo ?? ''),
                                )
                              : Image.network(item.logo ?? ''),
                        );
                      }),
                ],
              )
            : const SizedBox()
      ],
    );
  }
}
