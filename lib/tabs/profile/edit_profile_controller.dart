import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:loader_overlay/loader_overlay.dart';
import 'package:shopping/apis/ShoppingRepository.dart';
import 'package:shopping/models/province.dart';
import 'package:shopping/models/user_Info.dart';
import 'package:shopping/shopping_controller.dart';
import 'package:shopping/utils/extension/string_extension.dart';
import 'package:shopping/widgets/app_dialog.dart';

import '../../enum/sex_enum.dart';
import 'profile_controller.dart';

class EditProfileController extends GetxController {
  late String vdcpToken;
  List<Address> listProvince = <Address>[].obs;

  Future<void> loadContents(int pageType) async {}

  RxMap<int, List<Address>>? listAddress;
  UserInfo? user;
  List<int?> listIntAddress = <int?>[].obs;
  var isValidateName = false.obs;
  var isValidatePhone = false.obs;
  var isValidateEmail = false.obs;
  var isValidateDob = false.obs;

  @override
  void onInit() async {
    super.onInit();
    listAddress = RxMap({0: [], 1: [], 2: [], 3: []});
    // getProvince();
    //get user from local;
    user = Get.find<ProfileController>().user;
    setListAddressIndex();
    getListAddress(isAll: true);
  }

  void setListAddressIndex() {
    listIntAddress.add(user?.cityId);
    listIntAddress.add(user?.districtId);
    listIntAddress.add(user?.wardId);
    listIntAddress.add(user?.streetId);
  }

  void getListAddress({int? index, bool? isAll}) async {
    for (int i = (index != null ? index + 1 : 0); i < listIntAddress.length; i++) {
      if (i == 0) {
        listAddress?[0]?.clear();
        listAddress?[0]?.addAll(await getAddressByIndex());
      } else {
        if (isAll == true) {
          if (listIntAddress[i - 1] != null) {
            listAddress?[i]?.clear();
            listAddress?[i]?.addAll(await getAddressByIndex(index: i, id: listIntAddress[i - 1]));
          } else {
            listAddress?[i]?.clear();
          }
        } else {
          if (i == index! + 1) {
            listAddress?[i]?.clear();
            listAddress?[i]?.addAll(await getAddressByIndex(index: i, id: listIntAddress[i - 1]));
          } else {
            listAddress?[i]?.clear();
          }
        }
      }
    }
    update();
  }

  getAddressByIndex({int? index, int? id}) async {
    var result;
    var res = await ShoppingRepository.instance.get_address_info2(index: index, id: id);
    if (res.code == 0) {
      result = res.data;
    } else {
      result = [];
    }
    return result;
  }

  void pickAddress({int? index, Address? value}) {
    int? id = (listAddress?[index])?.firstWhere((element) => element.id == value?.id).id;
    for (var i = 0; i < 4; i++) {
      if (i == index) {
        listIntAddress[i] = id;
      } else if (i > index!) {
        listIntAddress[i] = -1;
      }
    }
    getListAddress(index: index);
    setAddressToUser();
  }

  setAddressToUser() {
    if (listIntAddress[0] != null && listIntAddress[0] != -1) {
      user = user?.copyWith(cityId: listIntAddress[0]);
    }
    if (listIntAddress[1] != null && listIntAddress[1] != -1) {
      user = user?.copyWith(districtId: listIntAddress[1]);
    } else {
      user = user?.copyWith(districtId: -1);
    }
    if (listIntAddress[2] != null && listIntAddress[2] != -1) {
      user = user?.copyWith(wardId: listIntAddress[2]);
    } else {
      user = user?.copyWith(wardId: -1);
    }
    if (listIntAddress[3] != null && listIntAddress[3] != -1) {
      user = user?.copyWith(streetId: listIntAddress[3]);
    } else {
      user = user?.copyWith(streetId: -1);
    }

    update();
  }

  //edit field
  editName(String value) {
    user = user?.copyWith(fullname: value);
    update();
  }

  editPhone(String value) {
    user = user?.copyWith(phoneNumber: value);
    update();
  }

  editGender(SexEnum value) {
    user = user?.copyWith(sex: value.id);
    update();
  }

  editMail(String value) {
    user = user?.copyWith(email: value);
    update();
  }

  editDob(dynamic value) {
    String dob = '';
    if (value is String) {
      dob = value;
    } else if (value is DateTime) {
      dob = DateFormat('dd/MM/yyyy').format(value);
    }

    user = user?.copyWith(birthday: dob);
    update();
  }

  editAddress(String value) {
    user = user?.copyWith(address: value);
    update();
  }

  editCCCD(String value) {
    user = user?.copyWith(cccd: value);
    update();
  }

  bool validate() {
    // trim tên và sdt
    user = user?.copyWith(phoneNumber: user?.phoneNumber?.trim(), fullname: user?.fullname?.trim());
    final enableName = (user?.fullname?.trim().validateName() == false && user?.fullname?.trim().isNotEmpty == true);
    final enablePhone = (user?.phoneNumber?.trim().validatePhone() == false && user?.phoneNumber?.trim().isNotEmpty == true);
    final enableEmail = (user?.email?.isEmpty == true) || (user?.email?.validateEmail() == false && user?.email?.isNotEmpty == true);
    final enableDob = (user?.birthday?.isEmpty == true) || (user?.birthday?.validateDate() == true && user?.birthday?.isNotEmpty == true);

    final totalEnable = enableName == true && enablePhone == true && enableEmail == true && enableDob == true;
    return totalEnable;
  }

  void runValidate() {
    isValidateName.value = true;
    isValidatePhone.value = true;
    isValidateEmail.value = true;
    isValidateDob.value = true;
    update();
  }

  saveUser(BuildContext context, {Function()? callBack}) async {
    // if (user?.birthday?.isEmpty == true) {
    //   dialogAsk(
    //     context,
    //     'Vui lòng nhập ngày sinh!',
    //   );
    // } else {
    // isValidate.value = ltru;
    // if (user?.fullname?.validateName() == true) {
    //   dialogAsk(
    //     context,
    //     'Tên không hợp lệ!',
    //   );
    // } else if (user?.birthday?.validateDate() == false) {
    //   dialogAsk(
    //     context,
    //     'Ngày sinh không hợp lệ!',
    //   );
    // } else {

    if (validate() == true) {
      context.loaderOverlay.show();
      await Future.delayed(Duration(seconds: 2));
      final res = await ShoppingRepository.instance.updateUserInfor2(user);
      if (res.code == 0) {
        user = res.data!;
        update();
        context.loaderOverlay.hide();
        dialogAsk(context, 'Lưu thông tin tài khoản thành công', isBackAction: true, callOk: callBack);
        Get.find<ProfileController>().updateUser(user);
        if (Get.isRegistered<ShoppingController>()) {
          Get.find<ShoppingController>().updateUserInfor(user);
        }
      } else {
        context.loaderOverlay.hide();
        if (res.message?.isNotEmpty == true) {
          dialogAsk(context, '${res.message}');
        } else {
          dialogAsk(context, 'Lưu thông tin không thành công');
        }
      }
      update();
    } else {
      await dialogAsk(
        context,
        'Thông tin chưa hợp lệ',
      );
    }
  }

  @override
  void onClose() {
    super.onClose();
  }
}
