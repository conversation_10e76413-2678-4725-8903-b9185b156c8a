class ShopEndPoints {
  static const post_sso = 'auth/vdcp-sso';
  static const get_site = 'site';
  static const info = 'auth/info';
  static const post_vdcp_access = 'auth/vdcp-access';
  //address
  static const get_city_info = 'adds/get-city-info';
  static String get_district_infor(int id) => 'adds/$id/get-district-info';
  static String get_ward_infor(int id) => 'adds/$id/get-ward-info';
  static String get_street_infor(int id) => 'adds/$id/get-street-info';

  // package
  static const get_products = 'products';
  static const get_products_count = 'products/count';
  static get_product(int id) => 'products/$id';

  //user
  static const update_info = 'users/update-info';
  static const upload_avatar = 'user/upload-avatar';

  //home
  static const home = 'site/home';
  static const homeBanner = 'site/banner';
  static const getListSim = 'sim-so/list-sim';
  // sim
  static const getRandomSim = 'sim-so/get-random-number';
  static const postResendOTPNumberBook = 'sim-so/resend-otp-number-book';
  static const postBookSim = 'sim-so/book-sim';
  static const postUnBookSim = 'sim-so/un-book-sim';
  static const getPhongGiaoDich = 'sim-so/get-phong-giao-dich';
  static const postOTPNumberBook = 'sim-so/get-otp-number-book';
  static const getFilterSim = 'sim-so/filter';
  //order
  static const get_order = 'orders';
  static get_order_by_id(int id) => 'orders/$id';
  static const post_orders_buy_package_di_dong = 'orders/buy-package-di-dong';
  static const post_orders_resend_otp = 'orders/resend-otp';
  static const post_validate_buy_package_di_dong =
      'orders/validate-buy-package-di-dong';
  static const postCreate = 'sim-so/create';
  // payment online
  static const getValidateMobile = 'orders/validate-mobile';
  static const getListBankInfor = 'payments/get-list-info';
  static getPaymentInfor(int ordId) => 'payments/$ordId/get-payment-info';
  // insurance
  static String getInsuranceFilter(int insId) => 'insurances/$insId/filter';
  static const String postInsuranceGetPrice = 'insurances/get-price';
  static const String postInsuranceCreateOrder = 'insurances/create-order';
  // report flag
  static const getReportFlag = 'site/top10-toa-sang';
}
