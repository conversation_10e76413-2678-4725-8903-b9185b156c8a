import 'package:flutter/material.dart';
import 'package:shopping/tabs/order/order_detail_screen.dart';
import 'package:shopping/utils/extension/int_extension.dart';

import '../../../constants/app_colors.dart';
import '../../../constants/app_text_style.dart';
import '../../../enum/enum_order_type.dart';
import '../../../models/order.dart';

class OrderWidget extends StatelessWidget {
  const OrderWidget({
    super.key,
    this.order,
  });
  final Order? order;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () {
        Navigator.push(
            context,
            MaterialPageRoute(
                builder: (context) => OrderDetailScreen(
                      orderId: order?.id,
                    )));
      },
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(10),
          border: Border.all(
            color: AppColors.greyD1D1D1,
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: const EdgeInsets.only(
                  top: 10, bottom: 10, left: 15, right: 10),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Row(
                    children: [
                      Text(
                        "${order?.timeAt}",
                        style: AppTextStyle.s12Regular.copyWith(
                            color: AppColors.grey6A6A6A, height: 17 / 12),
                      ),
                      Expanded(
                        child: Align(
                          alignment: FractionalOffset.bottomRight,
                          child: Container(
                            width: 88,
                            height: 18,
                            decoration: BoxDecoration(
                                color: OrderType.fromStatus(order?.status ?? 0)
                                    .statusColor,
                                borderRadius: BorderRadius.circular(5)),
                            child: Center(
                              child: Text(
                                OrderType.fromStatus(order?.status ?? 0)
                                    .statusName,
                                style: AppTextStyle.s10Regular.copyWith(
                                  color: AppColors.whiteFFFFFF,
                                  height: 17 / 12,
                                ),
                              ),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                  SizedBox(
                    height: 2,
                  ),
                  _buildOrderItem(
                    title: "Mã đơn hàng: ",
                    value: "${order?.code}",
                    // order: order,
                  ),
                  SizedBox(
                    height: 2,
                  ),
                  _buildOrderItem(
                    title: 'Sản phẩm: ',
                    value: '${order?.getItemProductInfor}',
                  ),
                  order?.isInsABICOrder == true
                      ? const SizedBox()
                      : _buildOrderItem(
                          title: 'Số lượng: ',
                          value: '${order?.productItems?[0].quantity}',
                        ),
                  SizedBox(
                    height: 2,
                  ),
                  _buildOrderItem(
                    title: "Tổng giá trị đơn hàng: ",
                    value: '${order?.totalPrice.convertToVietnamesMoney()} đ',
                    // order: order,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

Widget _buildOrderItem({
  required String title,
  required String value,
  Order? order,
}) {
  return Row(
    crossAxisAlignment: CrossAxisAlignment.start,
    children: [
      Text(title,
          style: AppTextStyle.s13Regular
              .copyWith(color: AppColors.black, height: 17 / 12)),
      SizedBox(
        width: 10,
      ),
      Expanded(
        child: Align(
          alignment: FractionalOffset.centerRight,
          child: Text(
            textAlign: TextAlign.end,
            value,
            style: AppTextStyle.s13Bold.copyWith(
              color: order != null
                  ? OrderType.fromStatus(order.status ?? 0).textColor
                  : AppColors.black,
              height: 17 / 12,
            ),
          ),
        ),
      ),
    ],
  );
}
