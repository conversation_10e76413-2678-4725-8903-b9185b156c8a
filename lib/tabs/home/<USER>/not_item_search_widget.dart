import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:shopping/constants/app_assets_paths.dart';

import '../../../constants/app_text_style.dart';

class NotItemSearchWidget extends StatelessWidget {
  const NotItemSearchWidget({super.key, required this.messageFail});
  final String messageFail;
  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Container(
          height: 84,
          width: 84,
          decoration: BoxDecoration(
            color: Color(0xffFFF0E9),
            shape: BoxShape.circle,
          ),
          child: Center(
            child: SvgPicture.asset(SvgPath.svgFilterEmpty),
          ),
        ),
        SizedBox(height: 20),
        Text(
          (messageFail.isEmpty) ? 'Không có dữ liệu để hiển thị' : messageFail,
          textAlign: TextAlign.center,
          style: AppTextStyle.s15Regular.copyWith(
            color: Colors.black,
          ),
        ),
        SizedBox(height: 50),
      ],
    );
  }
}
