import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:infinite_scroll_pagination/infinite_scroll_pagination.dart';
import 'package:shopping/widgets/app_loading_widget.dart';

import '../../../models/order.dart';
import '../order_controller.dart';
import 'empty_order_widget.dart';
import 'order_widget.dart';

class AllOrderTab extends GetView<OrderController> {
  const AllOrderTab({super.key});

  @override
  Widget build(BuildContext context) {
    return PagedListView.separated(
      keyboardDismissBehavior: ScrollViewKeyboardDismissBehavior.onDrag,
      padding: EdgeInsets.zero,
      pagingController: controller.allController,
      builderDelegate: PagedChildBuilderDelegate<Order>(
        itemBuilder: (c, i, index) => OrderWidget(
          order: i,
        ),
        noItemsFoundIndicatorBuilder: (context) => EmptyOrderWidget(),
        firstPageErrorIndicatorBuilder: (context) => AppLoadingWidget(),
        newPageProgressIndicatorBuilder: (context) => AppLoadingWidget(),
      ),
      separatorBuilder: (c, i) => SizedBox(
        height: 15,
      ),
    );
  }
}
