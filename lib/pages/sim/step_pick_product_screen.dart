import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:infinite_scroll_pagination/infinite_scroll_pagination.dart';
import 'package:shopping/apis/ShoppingRepository.dart';
import 'package:shopping/constants/app_assets_paths.dart';
import 'package:shopping/constants/app_colors.dart';
import 'package:shopping/constants/app_text_style.dart';
import 'package:shopping/constants/enum_step_ord_sim.dart';
import 'package:shopping/loading_screen.dart';
import 'package:shopping/models/home_model.dart';
import 'package:shopping/models/product.dart';
import 'package:shopping/pages/package/package_screen.dart';
import 'package:shopping/pages/package/widgets/product_widget.dart';
import 'package:shopping/pages/sim/item_sim_action.dart';
import 'package:shopping/pages/sim/sim_order_screen.dart';
import 'package:shopping/pages/sim/step_pick_product_controller.dart';
import 'package:shopping/pages/sim/widgets/count_time_order_sim_widget.dart';
import 'package:shopping/pages/sim/widgets/dash_line_widget.dart';
import 'package:shopping/pages/sim/widgets/filter_product_with_sim.dart';
import 'package:shopping/pages/sim/widgets/stepper_widget.dart';
import 'package:shopping/tabs/home/<USER>/item_package_action.dart';
import 'package:shopping/utils/responsive_utils.dart';
import 'package:shopping/widgets/app_dialog.dart';

class StepPickProductScreen extends StatelessWidget {
  const StepPickProductScreen(
      {super.key,
      this.homeData,
      required this.ordId,
      required this.phoneNumber,
      required this.userPhoneNumber});
  final HomeData? homeData;
  final int? ordId;
  final String? phoneNumber;
  final String? userPhoneNumber;
  @override
  Widget build(BuildContext context) {
    final double w = MediaQuery.of(context).size.width;
    final int _cntItem = ResponsiveUtils.isMobile(context)
        ? 2
        : ResponsiveUtils.isTablet(context)
            ? 3
            : 4;
    // itemWidth = (device width - 2 viền - khoảng cách giữa các item)/số lượng item 1 row
    final double _itemWidth = (w - 30 - (_cntItem - 1) * 15) / _cntItem;
    return GetBuilder<StepPickProductController>(
        init: StepPickProductController(homeData: homeData),
        builder: (controller) => Scaffold(
              backgroundColor: AppColors.white,
              appBar: AppBar(
                leading: GestureDetector(
                  child: Container(
                    width: 20,
                    height: 20,
                    color: Colors.transparent,
                    child: SvgPicture.asset(SvgPath.svgArrowBack,
                        colorFilter: ColorFilter.mode(AppColors.black, BlendMode.srcIn),
                        ),
                        
                  ),
                  onTap: () async {
                    ShoppingRepository.instance.postUnBookSim(ordId: ordId);
                    ActionBack.onBack(context: context);
                  },
                ),
                titleSpacing: 0,
                backgroundColor: AppColors.white,
                elevation: 5,
                shadowColor: AppColors.black.withOpacity(0.4),
                surfaceTintColor: AppColors.white,
                centerTitle: true,
                title: Text(
                  'SIM SỐ',
                  style: AppTextStyle.s16Bold
                      .copyWith(height: 20 / 16, color: AppColors.black),
                ),
                actions: [SizedBox()],
              ),
              endDrawerEnableOpenDragGesture: false,
              onEndDrawerChanged: (isOpened) {
                controller.onInitDataToOpenFilter();
              },
              endDrawer:
                  FilterProductWithSimEndrawWidget(controller: controller),
              body: Padding(
                padding: EdgeInsets.only(top: 12, left: 15, right: 15),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    SizedBox(
                      height: 53,
                      child: LineStepStatusWidget(
                        currentStepIndex: controller.listStepOrdName
                            .indexOf(StepOrdSimStatus.pickProduct),
                        lisStepStatus: controller.listStepOrdName
                            .map((e) => e.getName)
                            .toList(),
                      ),
                    ),
                    const SizedBox(height: 18),
                    DashLineWidget(),
                    SizedBox(height: 17),
                    Obx(() => CountTimeOrderSimWidget(
                        time: controller.countTime.value)),
                    const SizedBox(height: 18),
                    Align(
                      alignment: Alignment.centerRight,
                      child: Builder(
                        builder: (context) => GestureDetector(
                          onTap: () {
                            // open endraw
                            Scaffold.of(context).openEndDrawer();
                          },
                          child: Container(
                            color: Colors.transparent,
                            child: Padding(
                              padding: const EdgeInsets.only(bottom: 4),
                              child: Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  SvgPicture.asset(SvgPath.svgIconFilter),
                                  Obx(() => Text(
                                        '${controller.countNumTypeFilter.value == 0 ? "   " : "(${controller.countNumTypeFilter.value})"}',
                                        style: AppTextStyle.s12Medium
                                            .copyWith(color: AppColors.black),
                                      )),
                                ],
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
                    // list item package
                    const SizedBox(height: 10),
                    Expanded(
                      child: GetBuilder<StepPickProductController>(
                          id: StepPickProductControllerUpdateKey
                              .updateListProduct,
                          builder: (controller) => PagedGridView(
                                keyboardDismissBehavior:
                                    ScrollViewKeyboardDismissBehavior.onDrag,
                                pagingController: controller.pageController,
                                physics: ClampingScrollPhysics(),
                                gridDelegate:
                                    SliverGridDelegateWithFixedCrossAxisCount(
                                  crossAxisCount: _cntItem,
                                  mainAxisSpacing: 15,
                                  crossAxisSpacing: 15,
                                  childAspectRatio: _itemWidth / 180,
                                ),
                                builderDelegate:
                                    PagedChildBuilderDelegate<Product>(
                                  itemBuilder: (c, i, index) {
                                    return GestureDetector(
                                      onTap: () {
                                        ItemPackageAction.openDetail(context, i,
                                            isBuyWithSim: true,
                                            ordId: ordId,
                                            phoneNumber: phoneNumber,
                                            userPhoneNumber: userPhoneNumber);
                                      },
                                      child: ProductPackageWidget(
                                        isBuyWithSim: true,
                                        product: i,
                                        openBuy: () {
                                          // check time
                                          if (controller.countTime.value <= 0) {
                                            // show pop up
                                            dialogAsk(
                                              context,
                                              'Đã hết thời gian giữ số. Vui lòng thực hiện lại.',
                                              textButton: 'ĐÓNG',
                                              isBackAction: true,
                                              callOk: () {
                                                Navigator.of(context).pop();
                                                //
                                                ActionBack.onBack(
                                                    context: context);
                                              },
                                            );
                                          } else {
                                            Navigator.push(
                                                context,
                                                MaterialPageRoute(
                                                    builder: (context) =>
                                                        SIMOrderScreen(
                                                          ordId: ordId,
                                                          product: i,
                                                          phoneNumber:
                                                              phoneNumber,
                                                          userPhoneNumber:
                                                              userPhoneNumber,
                                                        )));
                                          }
                                        },
                                      ),
                                    );
                                  },
                                  noItemsFoundIndicatorBuilder: (context) {
                                    return EmptyListWidget(
                                        messageFail:
                                            controller.messageFail.value,
                                        isSearchingOrFilter: (controller
                                                .countNumTypeFilter.value !=
                                            0));
                                  },
                                  firstPageProgressIndicatorBuilder:
                                      (context) => SizedBox(),
                                  newPageProgressIndicatorBuilder: (context) =>
                                      LoadingWidget(),
                                ),
                              )),
                    )
                  ],
                ),
              ),
            ));
  }
}
