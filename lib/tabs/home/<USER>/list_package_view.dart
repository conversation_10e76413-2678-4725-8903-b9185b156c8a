import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:shopping/constants/app_assets_paths.dart';
import 'package:shopping/constants/app_colors.dart';
import 'package:shopping/constants/app_text_style.dart';
import 'package:shopping/constants/enum_package_type.dart';
import 'package:shopping/enum/home_type.dart';
import 'package:shopping/models/home_model.dart';
import 'package:shopping/pages/package/package_screen.dart';
import 'package:shopping/pages/package/widgets/product_widget.dart';
import 'package:shopping/tabs/home/<USER>/hot_deal_widget.dart';
import 'package:shopping/tabs/home/<USER>/item_package_action.dart';

class ListPackageView extends StatelessWidget {
  const ListPackageView({super.key, required this.homeData});
  final HomeData homeData;
  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Expanded(
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  Image.network(
                    homeData.icon ?? '',
                    width: 28,
                    height: 28,
                  ),
                  SizedBox(width: 5),
                  Expanded(
                    child: Text(
                      homeData.name ?? '',
                      style: AppTextStyle.s16Bold
                          .copyWith(overflow: TextOverflow.ellipsis),
                    ),
                  ),
                ],
              ),
            ),
            GestureDetector(
              onTap: () {
                Navigator.push(
                    context,
                    MaterialPageRoute(
                        builder: (context) => PackageScreen(
                              packageType: PackageType.unknown,
                              homeData: homeData,
                            )));
              },
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Text("Xem thêm",
                      style: AppTextStyle.s12Medium.copyWith(
                        color: AppColors.black,
                      )),
                  SizedBox(width: 5),
                  SvgPicture.asset(SvgPath.svgWatchMore)
                ],
              ),
            )
          ],
        ),
        const SizedBox(height: 16),
        SizedBox(
          width: MediaQuery.of(context).size.width,
          height: 180,
          child: OverflowBox(
            maxWidth: MediaQuery.of(context).size.width,
            child: SizedBox(
              height: 180,
              child: ListView.separated(
                padding: EdgeInsets.only(right: 15),
                physics: const ClampingScrollPhysics(),
                scrollDirection: Axis.horizontal,
                shrinkWrap: true,
                itemCount: homeData.contents?.length ?? 0,
                itemBuilder: (_, index) {
                  return GestureDetector(
                      onTap: () {
                        ItemPackageAction.openDetail(
                            context, homeData.contents![index].product!);
                      },
                      child: Padding(
                          padding: EdgeInsets.only(
                            left: (index == 0 && ((homeData.contents?.length ?? 0) * 200 + ((homeData.contents?.length ?? 0) - 1) * 20 > MediaQuery.of(context).size.width - 30)) ? 15 : 0,
                          ),
                          child: homeData.getHomeType == HomeType.product
                              ? SizedBox(
                                  width: 200,
                                  height: 180,
                                  child: ProductPackageWidget(
                                    isProductHome: true,
                                    openBuy: () {
                                      ItemPackageAction.openSheetBuy(context,
                                          homeData.contents![index].product!);
                                    },
                                    product: homeData.contents?[index].product,
                                  ),
                                )
                              : homeData.getHomeType == HomeType.hotDeal
                                  ? ItemHotDealWidget(
                                      openBuy: () {
                                        ItemPackageAction.openSheetBuy(context,
                                            homeData.contents![index].product!);
                                      },
                                      product:
                                          homeData.contents?[index].product,
                                    )
                                  : const SizedBox()));
                },
                separatorBuilder: (c, i) => SizedBox(width: 15),
              ),
            ),
          ),
        ),
      ],
    );
  }
}

// class ItemPackageWidget extends StatelessWidget {
//   const ItemPackageWidget(
//       {super.key, required this.openBuy, required this.product});
//   final Function() openBuy;
//   final Product product;
//   @override
//   Widget build(BuildContext context) {
//     return SizedBox(
//       width: 140,
//       height: 176,
//       child: Stack(
//         children: [
//           Container(
//             child: SvgPicture.asset(
//               SvgPath.svgBgPackage2,
//               fit: BoxFit.fill,
//             ),
//           ),
//           Positioned(
//               top: 12,
//               left: 9,
//               right: 9,
//               bottom: 9,
//               child: ItemPackageInfor(
//                 openBuy: openBuy,
//                 product: product,
//               ))
//         ],
//       ),
//     );
//   }
// }

// class ItemPackageInfor extends StatelessWidget {
//   const ItemPackageInfor(
//       {super.key, required this.openBuy, required this.product});
//   final Function() openBuy;
//   final Product product;
//   @override
//   Widget build(BuildContext context) {
//     return Column(
//       mainAxisSize: MainAxisSize.max,
//       crossAxisAlignment: CrossAxisAlignment.start,
//       children: [
//         Row(
//           mainAxisSize: MainAxisSize.max,
//           children: [
//             SvgPicture.asset(
//               SvgPath.svgIconBox,
//               color: AppColors.black,
//             ),
//             const SizedBox(width: 6),
//             Expanded(
//               child: Text(
//                 product.getContent(1),
//                 style: AppTextStyle.s16Bold.copyWith(
//                   color: AppColors.black,
//                 ),
//                 overflow: TextOverflow.ellipsis,
//               ),
//             )
//           ],
//         ),
//         const SizedBox(height: 15),
//         Text(
//           product.getContent(2),
//           style: AppTextStyle.s20Bold.copyWith(
//             color: AppColors.black,
//           ),
//         ),
//         const SizedBox(height: 9),
//         ItemPackageLineDetail(
//           iconPath: SvgPath.svgIconCapacity,
//           infor: product.getContent(3),
//           color: AppColors.black,
//         ),
//         const SizedBox(height: 2),
//         (product.configDisplay?.icons ?? []).isEmpty
//             ? const SizedBox(height: 20)
//             : SvgPicture.asset(SvgPath.svgIconTiktok),
//         const SizedBox(height: 4),
//         GestureDetector(
//           onTap: openBuy,
//           child: Container(
//               width: 124,
//               height: 32,
//               decoration: BoxDecoration(
//                 color: AppColors.blue005BF9,
//                 borderRadius: BorderRadius.circular(10),
//               ),
//               child: Center(
//                 child: Text("ĐĂNG KÝ",
//                     style: AppTextStyle.s12Medium
//                         .copyWith(color: AppColors.white, height: 17 / 12)),
//               )),
//         )
//       ],
//     );
//   }
// }

// class ItemPackageLineDetail extends StatelessWidget {
//   const ItemPackageLineDetail(
//       {super.key,
//       required this.iconPath,
//       required this.infor,
//       required this.color});
//   final String iconPath;
//   final String infor;
//   final Color color;
//   @override
//   Widget build(BuildContext context) {
//     return SizedBox(
//       width: 130,
//       child: Row(
//         children: [
//           SvgPicture.asset(
//             iconPath,
//             color: color,
//           ),
//           const SizedBox(width: 4),
//           Expanded(
//             child: Text(infor,
//                 style: AppTextStyle.s11Regular.copyWith(
//                     height: 16 / 11,
//                     overflow: TextOverflow.ellipsis,
//                     color: color)),
//           )
//         ],
//       ),
//     );
//   }
// }
