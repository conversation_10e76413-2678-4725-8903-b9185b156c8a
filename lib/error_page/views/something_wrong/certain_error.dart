import 'package:flutter/material.dart';
import 'package:shopping/constants/app_text_style.dart';

import '../../components/reusable_primary_button.dart';
import '../../constants/text_style.dart';

class CertainError extends StatelessWidget {
  final Function() action;
  const CertainError(this.action, {Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          Image.asset(
            'packages/shopping/assets/error/certain_error.png',
            fit: BoxFit.cover,
            height: MediaQuery.of(context).size.height,
            width: MediaQuery.of(context).size.width,
          ),
          const Positioned(
            bottom: 230,
            left: 50,
            right: 50,
            child: Text(
              'Uh oh!',
              style: kTitleTextStyle,
              textAlign: TextAlign.center,
            ),
          ),
          Positioned(
            bottom: 170,
            left: 50,
            right: 50,
            child: Text(
              'Có lỗi xảy ra,\nXin vui lòng thử lại.',
              style: AppTextStyle.s13Regular,
              textAlign: TextAlign.center,
            ),
          ),
          Positioned(
            bottom: 100,
            left: 120,
            right: 120,
            child: ReusablePrimaryButton(
              childText: 'Thử lại',
              buttonColor: Colors.green,
              childTextColor: Colors.white,
              onPressed: () {
                action();
              },
            ),
          ),
        ],
      ),
    );
  }
}
