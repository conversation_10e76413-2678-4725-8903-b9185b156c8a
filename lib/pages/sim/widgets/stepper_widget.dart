import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:shopping/constants/app_assets_paths.dart';
import 'package:shopping/constants/app_colors.dart';
import 'package:shopping/constants/app_text_style.dart';
import 'package:shopping/constants/enum_step_ord_sim.dart';

class StepperWidget extends StatelessWidget {
  StepperWidget(
      {super.key,
      required this.content,
      required this.stepStatus,
      required this.index,
      this.leftColor,
      this.rightColor,
      this.checkStep});
  final String content;
  final StepStatus stepStatus;
  final int index;
  final Color? leftColor;
  final Color? rightColor;
  final bool? checkStep;
  @override
  Widget build(BuildContext context) {
    return IntrinsicWidth(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Expanded(
                  child: Container(
                margin: EdgeInsets.only(top: 15),
                color: leftColor,
                height: 2,
              )),
              stepStatus == StepStatus.done
                  ? Container(
                      height: 30,
                      width: 30,
                      margin: EdgeInsets.only(bottom: 6),
                      child: SvgPicture.asset(SvgPath.svgStepperCheck))
                  : stepStatus == StepStatus.process
                      ? checkStep == true
                          ? Container(
                              height: 30,
                              width: 30,
                              margin: EdgeInsets.only(bottom: 6),
                              child: SvgPicture.asset(SvgPath.svgStepperCheck))
                          : Container(
                              height: 30,
                              width: 30,
                              margin: EdgeInsets.only(bottom: 6),
                              decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(30),
                                  color: AppColors.redCE3722),
                              child: Center(
                                child: Text(
                                  '${index}',
                                  style: AppTextStyle.s14Medium.copyWith(
                                      height: 17 / 14, color: AppColors.white),
                                ),
                              ))
                      : Container(
                          height: 30,
                          width: 30,
                          margin: EdgeInsets.only(bottom: 6),
                          decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(30),
                              color: AppColors.greyD9D9D9),
                          child: Center(
                            child: Text(
                              '${index}',
                              style: AppTextStyle.s14Medium.copyWith(
                                  height: 17 / 14, color: AppColors.grey8E8E8E),
                            ),
                          )),
              Expanded(
                  child: Container(
                margin: EdgeInsets.only(top: 15),
                color: rightColor,
                height: 2,
              ))
            ],
          ),
          Text(
            content,
            style: AppTextStyle.s14Medium
                .copyWith(height: 17 / 14, color: AppColors.black),
          )
        ],
      ),
    );
  }
}

class LineStepStatusWidget extends StatelessWidget {
  const LineStepStatusWidget(
      {super.key,
      required this.currentStepIndex,
      required this.lisStepStatus,
      this.checkStep});
  final List<String> lisStepStatus;
  final int currentStepIndex;
  final bool? checkStep;
  @override
  Widget build(BuildContext context) {
    return ListView.builder(
        scrollDirection: Axis.horizontal,
        itemBuilder: (_, index) {
          StepStatus _stepStatus = index < currentStepIndex
              ? StepStatus.done
              : index == currentStepIndex
                  ? StepStatus.process
                  : StepStatus.next;
          return Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                margin: EdgeInsets.only(top: 15),
                color: index <= currentStepIndex
                    ? AppColors.redCE3722
                    : AppColors.grey8E8E8E,
                height: 2,
                width: index == 0
                    ? 0
                    : calculateWidth(MediaQuery.of(context).size.width - 30),
                // : ((MediaQuery.of(context).size.width - 30) / 400) < 15
                //     ? 20
                //     : ((MediaQuery.of(context).size.width - 30) / 400)
              ),
              StepperWidget(
                checkStep: checkStep,
                content: lisStepStatus[index],
                stepStatus: _stepStatus,
                index: index + 1,
                leftColor: index == 0
                    ? null
                    : _stepStatus == StepStatus.next
                        ? AppColors.grey8E8E8E
                        : AppColors.redCE3722,
                rightColor: index == lisStepStatus.length - 1
                    ? null
                    : _stepStatus == StepStatus.done
                        ? AppColors.redCE3722
                        : AppColors.grey8E8E8E,
              ),
            ],
          );
        },
        itemCount: lisStepStatus.length);
  }

  double calculateWidth(double maxW) {
    print(maxW);
    // case menu status ord SIM
    if (lisStepStatus.length == 4) {
      if (maxW / 400 < 15) return 20;
      return maxW / 400;
    }
    // case menu status ord insurance ABIC
    if ((maxW - 240) / 3 < 85) return 85;
    return (maxW - 240) / 3;
  }
}
