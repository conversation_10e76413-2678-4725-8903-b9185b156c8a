import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:shopping/constants/app_assets_paths.dart';
import 'package:shopping/constants/app_colors.dart';
import 'package:shopping/constants/app_text_style.dart';
import 'package:shopping/enum/enum_order_type.dart';
import 'package:shopping/tabs/order/search/search_order_controller.dart';

class SearchOrderScreen extends StatelessWidget {
  const SearchOrderScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return GetBuilder<SearchOrderController>(
      init: SearchOrderController(),
      builder: (controller) => Scaffold(
        appBar: AppBar(
          titleSpacing: 0,
          elevation: 0,
          scrolledUnderElevation: 0,
          leading: GestureDetector(
            child: SizedBox(
              width: 20,
              height: 20,
              child: SvgPicture.asset(SvgPath.svgArrowBack, fit: BoxFit.scaleDown),
            ),
            onTap: () {
              Navigator.pop(context);
            },
          ),
          centerTitle: false,
          title: Padding(
            padding: const EdgeInsets.only(right: 24),
            child: TextField(
              style: AppTextStyle.s14Regular.copyWith(color: AppColors.black2E2E2E),
              maxLength: 200,
              // controller: controller.searchController,
              // onChanged: controller.onChangeSetValue,
              cursorColor: AppColors.black2E2E2E,
              keyboardType: TextInputType.text,
              autofocus: true,
              decoration: InputDecoration(
                hintText: 'Nhập từ khoá tìm kiếm',
                hintStyle: AppTextStyle.s14Regular.copyWith(color: AppColors.black2E2E2E),
                filled: true,
                fillColor: AppColors.greyF1F1F3,
                counterText: '',
                border: const UnderlineInputBorder(),
                enabledBorder: OutlineInputBorder(
                  borderSide: const BorderSide(width: 1, color: AppColors.greyF1F1F3),
                  borderRadius: BorderRadius.circular(100),
                ),
                focusedBorder: OutlineInputBorder(
                  borderSide: const BorderSide(width: 1, color: AppColors.greyF1F1F3),
                  borderRadius: BorderRadius.circular(100),
                ),
                suffixIcon: Row(
                  mainAxisSize: MainAxisSize.min,
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    GestureDetector(
                      behavior: HitTestBehavior.opaque,
                      // onTap: controller.onTapSearch,
                      child: Padding(
                          padding: const EdgeInsets.all(15),
                          child: SvgPicture.asset(
                            SvgPath.svgIconSearch,
                          )),
                    )
                  ],
                ),
              ),
            ),
          ),
        ),
        body: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 20),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              SizedBox(
                height: 30,
                child: ListView.builder(
                  scrollDirection: Axis.horizontal,
                  itemCount: controller.listTab.length,
                  itemBuilder: (context, index) {
                    return Obx(() => GestureDetector(
                          onTap: () {
                            controller.onChangeTab(controller.listTab[index]);
                          },
                          child: Container(
                            margin: EdgeInsets.only(right: 5),
                            padding: EdgeInsets.symmetric(horizontal: 12, vertical: 4),
                            decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(20), border: controller.listTab[index] == controller.orderTypeChoosed.value ? Border.all(color: AppColors.blue005BF9) : null),
                            child: Text(controller.listTab[index].nameOrderType,
                                style: AppTextStyle.s14.copyWith(color: controller.listTab[index] == controller.orderTypeChoosed.value ? AppColors.blue005BF9 : AppColors.black071529)),
                          ),
                        ));
                  },
                ),
              )
            ],
          ),
        ),
      ),
    );
  }
}
