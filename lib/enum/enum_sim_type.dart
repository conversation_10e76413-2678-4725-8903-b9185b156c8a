enum SimType { physical, eSim }

extension EnumSimTypeExt on SimType {
  String get getName {
    switch (this) {
      case SimType.physical:
        return 'SIM vật lý';
      case SimType.eSim:
        return 'eSIM';
    }
  }

  int get getValue {
    switch (this) {
      case SimType.physical:
        return 2;
      case SimType.eSim:
        return 1;
    }
  }

  String? get getDescription {
    switch (this) {
      case SimType.eSim:
        return '(<PERSON><PERSON> dụng cho các thiết bị có hỗ trợ esim tại Việt Nam)';
      default:
        return null;
    }
  }
}
