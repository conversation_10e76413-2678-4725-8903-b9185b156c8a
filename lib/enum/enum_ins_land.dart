enum LandType { nhaChungCu, nhaBietThu, nhaDocLap, nhaLienKe, nhaTapThe }

enum LandTimeType { one, two, three }

enum LandStatus { chuSoHuu, nhaThue }

extension LandTypeExt on LandType {
  String get getName {
    switch (this) {
      case LandType.nhaBietThu:
        return 'Nhà biệt thự';
      case LandType.nhaChungCu:
        return 'Nhà chung cư';
      case LandType.nhaDocLap:
        return 'Nhà độc lập';
      case LandType.nhaLienKe:
        return 'Nhà liền kề';
      case LandType.nhaTapThe:
        return 'Nhà tập thể';
    }
  }
}

extension LandTimeTypeExt on LandTimeType {
  String get getName {
    switch (this) {
      case LandTimeType.one:
        return '1 năm';
      case LandTimeType.two:
        return '2 năm';
      case LandTimeType.three:
        return '3 năm';
    }
  }

  int get getDurationTime {
    switch (this) {
      case LandTimeType.one:
        return 1;
      case LandTimeType.two:
        return 2;
      case LandTimeType.three:
        return 3;
    }
  }
}

extension LandStatusExt on LandStatus {
  String get getName {
    switch (this) {
      case LandStatus.chuSoHuu:
        return 'Chủ sở hữu';
      case LandStatus.nhaThue:
        return 'Nhà thuê';
    }
  }

  String get getValue {
    switch (this) {
      case LandStatus.chuSoHuu:
        return 'CHUSOHUU';
      case LandStatus.nhaThue:
        return 'THUENHA';
    }
  }
}
