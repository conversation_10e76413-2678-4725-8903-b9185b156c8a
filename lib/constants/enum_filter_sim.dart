enum SupplierType { vinaphone }

extension SupplierExt on SupplierType {
  String get getName {
    switch (this) {
      case SupplierType.vinaphone:
        return 'vinaphone';
    }
  }
}

enum PhonenumberStartAt {
  vn091,
  vn094,
  vn088,
  vn085,
  vn084,
  vn083,
  vn082,
  vn081
}

extension PhonenumberStartAtExt on PhonenumberStartAt {
  String get getName {
    switch (this) {
      case PhonenumberStartAt.vn081:
        return '081';
      case PhonenumberStartAt.vn082:
        return '082';
      case PhonenumberStartAt.vn083:
        return '083';
      case PhonenumberStartAt.vn084:
        return '084';
      case PhonenumberStartAt.vn085:
        return '085';
      case PhonenumberStartAt.vn088:
        return '088';
      case PhonenumberStartAt.vn091:
        return '091';
      case PhonenumberStartAt.vn094:
        return '094';
    }
  }
  int get getValue{
     switch (this) {
      case PhonenumberStartAt.vn081:
        return 8481;
      case PhonenumberStartAt.vn082:
        return 8482;
      case PhonenumberStartAt.vn083:
        return 8483;
      case PhonenumberStartAt.vn084:
        return 8484;
      case PhonenumberStartAt.vn085:
        return 8485;
      case PhonenumberStartAt.vn088:
        return 8488;
      case PhonenumberStartAt.vn091:
        return 8491;
      case PhonenumberStartAt.vn094:
        return 8494;
    }
  }
}
