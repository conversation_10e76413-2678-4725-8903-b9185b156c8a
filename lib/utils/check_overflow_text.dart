import 'package:flutter/material.dart';

class CheckOverFlow {
  static bool doesTextOverflow(
      {required String text,
      required TextStyle style,
      required double maxWidth}) {
    final textSpan = TextSpan(text: text, style: style);
    final textPainter = TextPainter(
      text: textSpan,
      maxLines: 1,
      textDirection: TextDirection.ltr,
    );
    textPainter.layout(maxWidth: maxWidth);
    return textPainter.width >= maxWidth;
  }

  static double textWidth({required String text, required TextStyle style}) {
    final textSpan = TextSpan(text: text, style: style);
    final textPainter = TextPainter(
      text: textSpan,
      maxLines: 1,
      textDirection: TextDirection.ltr,
    );
    textPainter.layout();
    return textPainter.width;
  }

  static int calculateNumIcon(
      {required double maxWidth,
      required double textWidth,
      required int listIconLength, required int iconSize}) {
    double listLen = maxWidth - textWidth - 2;
    int numIc = listLen ~/iconSize;
    return numIc <= listIconLength ? numIc : listIconLength;
  }
}
