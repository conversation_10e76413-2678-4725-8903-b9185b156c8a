import 'package:flutter/material.dart';

import '../../../constants/app_colors.dart';
import '../../../constants/app_text_style.dart';

class ItemTabWidget extends StatelessWidget {
  const ItemTabWidget({
    super.key,
    this.name,
    this.isActive,
  });
  final String? name;
  final bool? isActive;

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(right: 5),
      padding: EdgeInsets.symmetric(horizontal: 12, vertical: 4),
      decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(20),
          border: isActive == true
              ? Border.all(color: AppColors.blue30AAB7)
              : null),
      child: Text(
        '${name}',
        style: AppTextStyle.s13Medium.copyWith(
          color: isActive == true ? AppColors.blue30AAB7 : AppColors.black,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }
}
