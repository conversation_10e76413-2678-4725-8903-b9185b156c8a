import 'dart:io';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

import '../constants/app_colors.dart';

class AppLoadingWidget extends StatelessWidget {
  const AppLoadingWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Platform.isAndroid ? CircularProgressIndicator(color: AppColors.blue005BF9) : CupertinoActivityIndicator(color: AppColors.blue005BF9),
    );
  }
}
