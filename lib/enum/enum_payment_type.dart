import 'package:shopping/constants/app_assets_paths.dart';
import 'package:shopping/gen/assets.dart';

enum PaymentType { tkc, tk0d }

extension PaymentTypeExt on PaymentType {
  int get getValue {
    switch (this) {
      case PaymentType.tkc:
        return 1;
      case PaymentType.tk0d:
        return 2;
    }
  }

  String get getName {
    switch (this) {
      case PaymentType.tkc:
        return "Thanh toán qua tài khoản chính điện thoại";
      case PaymentType.tk0d:
        return "Thanh toán qua QR code, Thẻ ATM";
    }
  }

  String get getIcon {
    switch (this) {
      case PaymentType.tkc:
        return Assets.PACKAGES_SHOPPING_ASSETS_SVGS_SVG_ICON_TKC_SVG;
      case PaymentType.tk0d:
        return Assets.PACKAGES_SHOPPING_ASSETS_SVGS_SVG_ICON_TKC0D_SVG;
    }
  }
}

enum PaymentOnlineType { qr, atm }

extension PaymentOnlineTypeExt on PaymentOnlineType {
  String get getValue {
    switch (this) {
      case PaymentOnlineType.qr:
        return 'QR';
      case PaymentOnlineType.atm:
        return 'DC';
    }
  }

  String get getName {
    switch (this) {
      case PaymentOnlineType.qr:
        return "Ứng dụng Mobile Banking quét mã QR";
      case PaymentOnlineType.atm:
        return "Thẻ ATM và tài khoản ngân hàng";
    }
  }

  String get getNameSimPayment {
    switch (this) {
      case PaymentOnlineType.qr:
        return "Thanh toán online";
      case PaymentOnlineType.atm:
        return "Thanh toán online";
    }
  }

  String get getIcon {
    switch (this) {
      case PaymentOnlineType.qr:
        return Assets.PACKAGES_SHOPPING_ASSETS_SVGS_SVG_ICON_QR_SVG;
      case PaymentOnlineType.atm:
        return Assets.PACKAGES_SHOPPING_ASSETS_SVGS_SVG_ICON_ATM_SVG;
    }
  }
  String get getIconPayForSim {
    switch (this) {
      case PaymentOnlineType.qr:
        return SvgPath.svgMethodPayOnline;
      case PaymentOnlineType.atm:
        return Assets.PACKAGES_SHOPPING_ASSETS_SVGS_SVG_ICON_ATM_SVG;
    }
  }
}
