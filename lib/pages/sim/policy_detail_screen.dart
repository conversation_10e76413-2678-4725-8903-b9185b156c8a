// ignore_for_file: deprecated_member_use

import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:shopping/constants/app_assets_paths.dart';
import 'package:shopping/constants/app_colors.dart';
import 'package:shopping/constants/app_text_style.dart';
import 'package:shopping/loading_screen.dart';
import 'package:shopping/pages/sim/policy_controller.dart';
import 'package:webview_flutter/webview_flutter.dart';

class PolicyDetailScreen extends StatelessWidget {
  PolicyDetailScreen({super.key, required this.webLink});
  final String webLink;

  @override
  Widget build(BuildContext context) {
    return GetBuilder<PolicyController>(
        init: PolicyController(webLink),
        builder: (controller) => LoadingOverlay(
            child: Scaffold(
                appBar: AppBar(
                  centerTitle: true,
                  backgroundColor: AppColors.white,
                  scrolledUnderElevation: 0,
                  title: const Text(
                    "CHÍNH SÁCH",
                  ),
                  surfaceTintColor: AppColors.white,
                  elevation: 5,
                  shadowColor: AppColors.black.withOpacity(0.4),
                  titleTextStyle: AppTextStyle.s16Bold.copyWith(color: Colors.black),
                  leading: IconButton(
                    icon: SvgPicture.asset(
                      SvgPath.svgIconBack,
                    ),
                    onPressed: () => Navigator.of(context).pop(),
                  ),
                ),
                body: Container(padding: const EdgeInsets.only(top: 16), child: WebViewScreen(controller.webLink)))));
  }
}

// ignore: must_be_immutable
class WebViewScreen extends StatefulWidget {
  String link;
  WebViewScreen(this.link, {Key? key}) : super(key: key);

  @override
  State<WebViewScreen> createState() => _WebViewScreenState();
}

class _WebViewScreenState extends State<WebViewScreen> {
  late WebViewController controller;
  Rx<bool> isError = false.obs;

  @override
  initState() {
    super.initState();
    _initWeb(widget.link);
  }

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      debugShowCheckedModeBanner: false,
      theme: ThemeData(
        primarySwatch: Colors.blue,
      ),
      home: Scaffold(
        body: SafeArea(
            child: Obx(
          () => isError.value
              ? Container(
                  color: Colors.white,
                  child: Center(
                    child: Text(
                      'error loading the page'.tr,
                      style: const TextStyle(fontSize: 20, fontWeight: FontWeight.w400),
                    ),
                  ),
                )
              : WebViewWidget(controller: controller),
        )),
      ),
    );
  }

  _initWeb(String link) {
    if (Uri.parse(link).isAbsolute) {
      controller = WebViewController()
        ..setJavaScriptMode(JavaScriptMode.unrestricted)
        ..setBackgroundColor(Colors.white)
        ..setNavigationDelegate(
          NavigationDelegate(
            onProgress: (int progress) {},
            onPageStarted: (String url) {},
            onPageFinished: (String url) {},
            onWebResourceError: (WebResourceError error) {},
            onNavigationRequest: (NavigationRequest request) {
              return NavigationDecision.navigate;
            },
          ),
        )
        ..loadRequest(Uri.parse(link));
    } else {
      isError.value = true;
    }
  }
}
