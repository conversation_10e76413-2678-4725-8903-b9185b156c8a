import 'package:shopping/enum/home_type.dart';

import 'product.dart';

class HomeResponse {
  List<HomeData>? data;

  HomeResponse({this.data});

  HomeResponse.fromJson(Map<String, dynamic> json) {
    data = json["data"] == null
        ? []
        : List<HomeData>.from(json["data"]!.map((x) => HomeData.fromJson(x)));
  }
}

class HomeData {
  int? id;
  String? name;
  int? type;
  int? productGroup;
  int? orderNumber;
  int? status;
  List<Contents>? contents;
  String? icon;

  HomeData(
      {this.id,
      this.name,
      this.type,
      this.productGroup,
      this.orderNumber,
      this.status,
      this.contents,
      this.icon});

  HomeData.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    icon = json['icon'];
    type = json['type'];
    productGroup = json['product_group'];
    orderNumber = json['order_number'];
    status = json['status'];
    contents = json["contents"] == null
        ? []
        : List<Contents>.from(
            json["contents"]!.map((x) => Contents.fromJson(x)));
  }
  HomeType? get getHomeType {
    return HomeType.values.where((e) => e.getValue == type).firstOrNull;
  }

  // bool get isListSIM {
  //   return getHomeType == ProductGroupType.sim.getValue;
  // }
}

class Contents {
  int? id;
  int? orderNumber;
  Product? product;

  Contents({this.id, this.orderNumber, this.product});

  Contents.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    orderNumber = json['order_number'];
    product =
        json["product"] == null ? null : Product.fromJson(json["product"]);
  }
}

// class Positions {
//   String? positionKey;
//   String? otherContent;
//   int? index;

//   Positions({this.positionKey, this.otherContent, this.index});

//   Positions.fromJson(Map<String, dynamic> json) {
//     positionKey = json['position_key'];
//     otherContent = json['other_content'];
//     index = json['index'];
//   }
// }
class HomeModelResponse {
  bool? success;
  int? statusCode;
  int? code;
  List<HomeData>? data;

  HomeModelResponse({
    this.success,
    this.statusCode,
    this.code,
    this.data,
  });

  HomeModelResponse.fromJson(Map<String, dynamic> json) {
    data = json["data"] == null
        ? []
        : List<HomeData>.from(json["data"]!.map((x) => HomeData.fromJson(x)));
  }
  HomeModelResponse.withError(int code_response_null, String handleError) {
    this.statusCode = code_response_null;
    this.success = false;
    // this.message = handleError;
    // print('msg => ${message}');
  }
}
