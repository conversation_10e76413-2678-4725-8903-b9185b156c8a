class ListSimResponse {
  List<ItemSim>? items;

  ListSimResponse({
    this.items,
  });

  factory ListSimResponse.fromJson(Map<String, dynamic> json) =>
      ListSimResponse(
        items:
            List<ItemSim>.from(json["items"].map((x) => ItemSim.fromJson(x))),
      );
}

class ItemSim {
  String? msisdn;
  String? idKhoSim;

  ItemSim({
    this.msisdn,
    this.idKhoSim,
  });

  factory ItemSim.fromJson(Map<String, dynamic> json) => ItemSim(
        msisdn: json["msisdn"],
        idKhoSim: json["idKhoSim"],
      );
}
