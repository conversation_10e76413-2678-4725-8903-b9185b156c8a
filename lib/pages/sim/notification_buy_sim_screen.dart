import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:shopping/constants/app_assets_paths.dart';
import 'package:shopping/constants/app_colors.dart';
import 'package:shopping/constants/app_text_style.dart';
import 'package:shopping/loading_screen.dart';
import 'package:shopping/models/sim_order_response.dart';
import 'package:shopping/pages/sim/item_sim_action.dart';
import 'package:shopping/pages/sim/notification_buy_sim_controller.dart';
import 'package:shopping/utils/convert/time_util.dart';

class NotificationBuyPackageScreen extends StatelessWidget {
  NotificationBuyPackageScreen({super.key, this.simResponse});
  final SIMResponse? simResponse;

  @override
  Widget build(BuildContext context) {
    return GetBuilder<NotificationBuySimController>(
      init: NotificationBuySimController(simResponse),
      builder: (controller) => LoadingOverlay(
        child: Scaffold(
          backgroundColor: AppColors.white,
          appBar: AppBar(
            centerTitle: true,
            backgroundColor: AppColors.white,
            scrolledUnderElevation: 0,
            title: const Text(
              "THÔNG BÁO",
            ),
            surfaceTintColor: AppColors.white,
            elevation: 5,
            shadowColor: AppColors.black.withValues(alpha: 0.4),
            titleTextStyle: AppTextStyle.s16Bold.copyWith(color: Colors.black),
            leading: IconButton(
              icon: SvgPicture.asset(
                SvgPath.svgIconBack,
              ),
              onPressed: () {
                ActionBack.onBack(context: context);
                // Navigator.of(context).popUntil(ModalRoute.withName("/page_list_sim"));
              },
            ),
          ),
          body: SingleChildScrollView(
            padding: EdgeInsets.symmetric(horizontal: 15, vertical: 30),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Column(crossAxisAlignment: CrossAxisAlignment.start, mainAxisSize: MainAxisSize.min, children: [
                  Container(
                      decoration: BoxDecoration(boxShadow: [
                        BoxShadow(
                          offset: Offset(0, 4),
                          blurRadius: 20,
                          color: Colors.black.withValues(alpha: 0.3),
                        )
                      ], color: AppColors.white, borderRadius: BorderRadius.circular(14)),
                      width: double.infinity,
                      height: 453,
                      child: Column(
                        children: [
                          SizedBox(height: 20),
                          Padding(
                            padding: const EdgeInsets.only(left: 32, right: 24),
                            child: Container(
                              width: double.infinity,
                              height: 179,
                              child: Stack(children: [
                                SvgPicture.asset(
                                  SvgPath.svgBgSuccess,
                                  fit: BoxFit.fill,
                                ),
                                Positioned(
                                  child: Padding(
                                    padding: const EdgeInsets.only(top: 54),
                                    child: Align(
                                        alignment: Alignment.center,
                                        child: Container(
                                          child: SvgPicture.asset(
                                            SvgPath.svgSuccess,
                                            fit: BoxFit.fitHeight,
                                          ),
                                        )),
                                  ),
                                )
                              ]),
                            ),
                          ),
                          SizedBox(
                            height: 12,
                          ),
                          Center(
                              child: Text(
                            "ĐẶT HÀNG THÀNH CÔNG!",
                            style: AppTextStyle.s18Bold,
                          )),
                          const SizedBox(height: 12),
                          Center(
                              child: Text(
                            "Cảm ơn bạn, chúng tôi sẽ liên hệ lại với bạn \n sớm nhất để xác nhận đơn hàng.\n Hãy để ý điện thoại nhé",
                            style: AppTextStyle.s14Medium.copyWith(height: 20 / 14),
                            textAlign: TextAlign.center,
                          )),
                          const SizedBox(height: 35),
                          infoOrder("Mã đơn hàng", "${controller.simResponse?.orderId ?? ""}"),
                          const SizedBox(height: 15),
                          infoOrder("Thời gian đặt", "${TimeUtil.milisecondsToDate(controller.simResponse?.orderTime ?? 0)}"),
                          const SizedBox(height: 15),
                          stateOrderDetail(controller.simResponse?.status ?? ""),
                        ],
                      ))
                ])
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget infoOrder(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(left: 9, right: 10),
      child: Row(
        children: [
          Text(
            label,
            style: AppTextStyle.s14Regular.copyWith(color: AppColors.black, height: 17 / 14),
          ),
          SizedBox(
            width: 10,
          ),
          Expanded(
              child: Align(
            alignment: FractionalOffset.bottomRight,
            child: Text(
              value,
              style: AppTextStyle.s14SemiBold.copyWith(color: AppColors.black, height: 17 / 14),
              textAlign: TextAlign.right,
            ),
          ))
        ],
      ),
    );
  }

  Widget stateOrderDetail(String status) {
    return Padding(
      padding: const EdgeInsets.only(left: 9, right: 10),
      child: Row(
        children: [
          Text(
            "Trạng thái",
            style: AppTextStyle.s14Regular.copyWith(color: AppColors.black, height: 17 / 12),
          ),
          Expanded(
            child: Align(
              alignment: FractionalOffset.bottomRight,
              child: Container(
                width: 117,
                height: 27,
                decoration: BoxDecoration(color: AppColors.orangeFF6540, borderRadius: BorderRadius.circular(5)),
                child: Center(
                  child: Text(status, style: AppTextStyle.s14Regular.copyWith(color: AppColors.whiteFFFFFF, height: 17 / 12)),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
