import 'dart:io';

import 'package:dio/src/response.dart' as prefix_Res;
import 'package:shopping/app_services/api_service.dart';

class BaseResponse<T> {
  int? code = CODE_ERROR;
  String? message;
  bool? success = true;
  int duration = 0;
  T? data;
  BaseResponse(prefix_Res.Response response, T fromJson(Map<String, dynamic> json)) {
    this.code = response.data[RESPONSE_CODE];
    this.message = response.data[RESPONSE_MSG];
    this.duration = response.extra["duration"] != null ? response.extra["duration"] as int : 0;
    if (response.statusCode == HttpStatus.ok) {
      if (this.code != CODE_SUCCESS) {
        if (response.data[RESPONSE_DATA] is Map) {
          data = fromJson(response.data[RESPONSE_DATA]);
        }
      } else if (this.code == CODE_SUCCESS && response.data[RESPONSE_DATA] == null) {
        this.code = CODE_RESPONSE_NULL;
      } else {
        data = from<PERSON>son(response.data[RESPONSE_DATA]);
      }
    } else {
      this.success = false;
    }
  }
  BaseResponse.withError(int code_response_null, String handleError) {
    this.code = code_response_null;
    this.success = false;
    this.message = handleError;
    print('msg => ${message}');
  }
  BaseResponse.fromJson(prefix_Res.Response response){
    this.code = response.data[RESPONSE_CODE];
    this.message = response.data[RESPONSE_MSG];
  }

  getStatusCode() {
    return this.code ?? 99;
  }
}
