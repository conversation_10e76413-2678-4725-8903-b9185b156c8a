import 'package:shopping/constants/app_assets_paths.dart';

enum InsuranceType { moto, car, land }

extension EnumInsuranceTypeExt on InsuranceType {
  String get getIcon {
    switch (this) {
      case InsuranceType.moto:
        return SvgPath.svgInsMoto;
      case InsuranceType.car:
        return SvgPath.svgInsCar;
      case InsuranceType.land:
        return SvgPath.svgInsLand;
    }
  }

  int get getValue {
    switch (this) {
      case InsuranceType.moto:
        return 5;
      case InsuranceType.car:
        return 6;
      case InsuranceType.land:
        return 7;
    }
  }
}
