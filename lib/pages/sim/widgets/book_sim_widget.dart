import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:shopping/app_services/app_storage.dart';
import 'package:shopping/constants/app_assets_paths.dart';
import 'package:shopping/constants/app_colors.dart';
import 'package:shopping/constants/app_text_style.dart';
import 'package:shopping/models/list_sim_response.dart';
import 'package:shopping/pages/sim/item_sim_action_controller.dart';
import 'package:shopping/tabs/home/<USER>/item_package_action.dart';
import 'package:shopping/widgets/app_text_field_number.dart';

class BookSimWidget extends StatelessWidget {
  const BookSimWidget(
      {super.key, required this.itemSim, required this.openOTP});
  final ItemSim? itemSim;
  final Function(
      {required int? ordId,
      required String? phoneNumber,
      required String? userPhoneNumber}) openOTP;
  Widget build(BuildContext context) {
    return GetBuilder<BookSimController>(
        init: BookSimController(itemSim: itemSim),
        builder: (controller) => Padding(
            padding: const EdgeInsets.only(left: 15, right: 15, top: 6),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  height: 22,
                  child: Stack(
                    children: [
                      Padding(
                        padding: const EdgeInsets.only(bottom: 3),
                        child: Align(
                          alignment: Alignment.center,
                          child: Container(
                            width: 45,
                            height: 5,
                            decoration: BoxDecoration(
                                color: AppColors.greyE5E6EC,
                                borderRadius: BorderRadius.circular(10)),
                          ),
                        ),
                      ),
                      Align(
                        alignment: Alignment.centerRight,
                        child: GestureDetector(
                          onTap: () {
                            // close
                            Navigator.of(context).pop();
                            // clear value storage
                            AppStorage.instance
                                .removeString2(SKeysPK.isBookSimFromHome);
                          },
                          child: SvgPicture.asset(SvgPath.svgIconClose),
                        ),
                      )
                    ],
                  ),
                ),
                Align(
                  alignment: Alignment.center,
                  child: Text(
                    "Chọn số - Mua SIM",
                    textAlign: TextAlign.center,
                    style: AppTextStyle.s18Bold,
                  ),
                ),
                const SizedBox(height: 16),
                LineBuyPackageWidget(
                  name: "Số SIM",
                  detail: "${itemSim?.msisdn}",
                ),
                Text("Vui lòng nhập số điện thoại của bạn để giữ số SIM",
                    style: AppTextStyle.s14Medium),
                const SizedBox(height: 20),
                CustomAppTextField(
                  maxLength: 12,
                  hintText: "Nhập số điện thoại",
                  controller: controller.phoneController,
                  // focusNode: controller.phoneFocusNode,
                ),
                const SizedBox(height: 20),
                GetBuilder<BookSimController>(
                    id: BookSimControllerUpdateKey.updateOrdId,
                    builder: (controller) => GestureDetector(
                          onTap: () async {
                            // request get OTP
                            await controller.onClickRequestOTP(
                              context: context,
                              openOTP: (
                                  {required int? ordId,
                                  required String? phoneNumber,
                                  required String? userPhoneNumber}) {
                                openOTP.call(
                                    ordId: ordId,
                                    phoneNumber: phoneNumber,
                                    userPhoneNumber:
                                        controller.phoneController.text.trim());
                              },
                            );
                            // get OTP success -> next step
                          },
                          child: Container(
                              width: double.infinity,
                              padding: EdgeInsets.all(12),
                              decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(8),
                                  color: AppColors.green30AAB7),
                              child: Obx(
                                () => !controller.isLoading.value
                                    ? Center(
                                        child: Text(
                                        "TIẾP TỤC",
                                        style: AppTextStyle.s16Medium
                                            .copyWith(color: AppColors.white),
                                      ))
                                    : Center(
                                        child: CircularProgressIndicator(
                                            color: AppColors.whiteFFFFFF)),
                              )),
                        )),
                SizedBox(height: 32),
              ],
            )));
  }
}
