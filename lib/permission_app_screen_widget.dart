part of 'permission_app_screen.dart';

Widget _buildLogo() {
  return SizedBox(
    height: 250,
    child: SvgPicture.asset(
      SvgPath.svgLogoApp,
    ),
  );
}

Widget _buildLearnMore() {
  return RichText(
    text: TextSpan(
      children: [
        TextSpan(
            text: '<PERSON><PERSON> cần các thông tin từ tài khoản của bạn để phục vụ bạn trong quá trình sử dụng. ',
            style: TextStyle(fontSize: 14, fontWeight: FontWeight.w400, color: AppColors.black, height: 21 / 15)),
        TextSpan(
          text: 'Tìm hiểu thêm',
          style: TextStyle(fontSize: 14, fontWeight: FontWeight.w400, color: AppColors.blue3D4FF4, height: 21 / 15),
          // style: AppTextStyle.s14Medium.copyWith(
          //   color: AppColors.blue3D4FF4,
          //   height: 21 / 15,
          // ),
        )
      ],
    ),
  );
}

Widget _buildAcceptTerm(
  bool? value,
  ValueChanged<bool?> onChanged,
) {
  return Row(
    crossAxisAlignment: CrossAxisAlignment.start,
    children: [
      // SizedBox(
      //   width: 20,
      // ),
      SizedBox(
        width: 20,
        height: 32,
        child: Checkbox(
          side: const BorderSide(
            width: 1,
            color: AppColors.grey6A6A6A,
          ),
          shape: const RoundedRectangleBorder(
            borderRadius: BorderRadius.all(
              Radius.circular(5.0),
            ),
          ),
          materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
          activeColor: AppColors.blue005BF9,
          value: value,
          onChanged: (value) => onChanged(value),
        ),
      ),
      SizedBox(
        width: 9,
      ),
      Expanded(
        child: Padding(
          padding: const EdgeInsets.only(top: 6.0),
          child: RichText(
            text: TextSpan(
              children: [
                TextSpan(text: 'Tôi đồng ý với các ', style: TextStyle(fontSize: 13, fontWeight: FontWeight.w400, color: AppColors.grey666666)
                    // style: AppTextStyle.s13Medium.copyWith(
                    //   color: AppColors.grey666666,
                    // ),
                    ),
                TextSpan(
                    text: 'điều khoản sử dụng của muadi',
                    style: TextStyle(
                      fontSize: 13,
                      fontWeight: FontWeight.w400,
                      color: AppColors.blue005BF9,
                      height: 19 / 13,
                    )
                    // AppTextStyle.s13Medium.copyWith(
                    //   color: AppColors.blue005BF9,
                    //   height: 19 / 13,
                    // ),
                    )
              ],
            ),
          ),
        ),
      ),
    ],
  );
}

Widget _buildTextAllowed() {
  return RichText(
    text: TextSpan(
      children: [
        TextSpan(
          text: 'Cho phép ',
          style: AppTextStyle.s20SemiBold.copyWith(
            color: AppColors.black,
          ),
        ),
        TextSpan(
          text: 'mua',
          style: AppTextStyle.s20SemiBold.copyWith(
            color: AppColors.redCE3722,
          ),
        ),
        TextSpan(
          text: 'di ',
          style: AppTextStyle.s20SemiBold.copyWith(
            color: AppColors.blue30AAB7,
          ),
        ),
        TextSpan(
          text: 'nhận các thông tin của bạn',
          style: AppTextStyle.s20SemiBold.copyWith(
            color: AppColors.black,
          ),
        ),
      ],
    ),
  );
}

Widget _buildDefaultBox({
  required String title,
  required bool value,
  required ValueChanged<bool?> onChanged,
  bool isName = true,
}) {
  return Container(
    // width: 310,
    height: 48,
    padding: EdgeInsets.symmetric(horizontal: 10),
    decoration: BoxDecoration(
      color: AppColors.greyF5F6F9,
      // border: Border.all(
      //   color: AppColors.greyD9D9D9,
      //   width: 1,
      // ),
      borderRadius: BorderRadius.circular(10),
    ),
    child: Row(
      children: [
        Padding(
          padding: const EdgeInsets.only(right: 15),
          child: CupertinoSwitch(
            value: value,
            activeTrackColor: AppColors.blue30AAB7,
            onChanged: (bool? value) {
              onChanged(value);
            },
          ),
        ),
        Expanded(
          child: Text(
            title,
            style: AppTextStyle.s14Medium.copyWith(
                color: AppColors.black,
                height: 17 / 12,
                overflow: TextOverflow.ellipsis),
          ),
        ),
      ],
    ),
  );
}
