import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:flutter_widget_from_html/flutter_widget_from_html.dart';
import 'package:shopping/constants/app_assets_paths.dart';
import 'package:shopping/constants/app_colors.dart';
import 'package:shopping/constants/app_text_style.dart';
import 'package:shopping/models/product.dart';

class InsuranceDetailWidget extends StatelessWidget {
  const InsuranceDetailWidget(
      {super.key, required this.scrollController, required this.productIns});
  final ScrollController scrollController;
  final Product? productIns;
  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(left: 15, right: 15, top: 6),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            height: 22,
            child: Stack(
              children: [
                Padding(
                  padding: const EdgeInsets.only(bottom: 3),
                  child: Align(
                    alignment: Alignment.center,
                    child: Container(
                      width: 45,
                      height: 5,
                      decoration: BoxDecoration(
                          color: AppColors.greyE5E6EC,
                          borderRadius: BorderRadius.circular(10)),
                    ),
                  ),
                ),
                Align(
                  alignment: Alignment.centerRight,
                  child: GestureDetector(
                    onTap: () {
                      // close
                      Navigator.of(context).pop();
                    },
                    child: SvgPicture.asset(SvgPath.svgIconClose),
                  ),
                ),
              ],
            ),
          ),
          Text(
            productIns?.name ?? 'N/A',
            style: AppTextStyle.s20SemiBold.copyWith(color: AppColors.black),
          ),
          const SizedBox(height: 15),
          Container(
            height: (MediaQuery.of(context).size.height * 0.9) - 250,
            child: SingleChildScrollView(
              controller: scrollController,
              child: Container(
                  margin: EdgeInsets.only(left: 15, right: 15),
                  child: HtmlWidget(
                    productIns?.description ?? '',
                    customStylesBuilder: (element) {
                      switch (element.localName) {
                        case 'table':
                          return {
                            'border': '1px solid',
                            'border-collapse': 'collapse',
                          };
                        case 'td':
                          return {'border': '1px solid'};
                      }

                      return null;
                    },
                  )),
            ),
          ),
          // SizedBox(
          //   height: (MediaQuery.of(context).size.height * 0.9) - 250,
          //   child: SingleChildScrollView(
          //     physics: ClampingScrollPhysics(),
          //     controller: scrollController,
          //     child: Text(
          //         " Là loại hình bảo hiểm bắt buộc người tham gia giao thông trên lãnh thổ nước CHXHCN VN phải tham gia bảo hiểm bắt buộc TNDS theo quy định của pháp luật.QUYỀN LỢI BẢO HIỂM:ham gia giao thông trên lãnh thổ nước CHXHCN VN phải tham gia bảo hiểm bắt buộc TNDS theo quy định của pháp luật.QUYỀN LỢI BẢO HIỂM:ham gia giao thông trên lãnh thổ nước CHXHCN VN phải tham gia bảo hiểm bắt buộc TNDS theo quy định của pháp luật.QUYỀN LỢI BẢO HIỂM:ham gia giao thông trên lãnh thổ nước CHXHCN VN phải tham gia bảo hiểm bắt buộc TNDS theo quy định của pháp luật.QUYỀN LỢI BẢO HIỂM:ham gia giao thông trên lãnh thổ nước CHXHCN VN phải tham gia bảo hiểm bắt buộc TNDS theo quy định của pháp luật.QUYỀN LỢI BẢO HIỂM:ham gia giao thông trên lãnh thổ nước CHXHCN VN phải tham gia bảo hiểm bắt buộc TNDS theo quy định của pháp luật.QUYỀN LỢI BẢO HIỂM:ham gia giao thông trên lãnh thổ nước CHXHCN VN phải tham gia bảo hiểm bắt buộc TNDS theo quy định của pháp luật.QUYỀN LỢI BẢO HIỂM:ham gia giao thông trên lãnh thổ nước CHXHCN VN phải tham gia bảo hiểm bắt buộc TNDS theo quy định của pháp luật.QUYỀN LỢI BẢO HIỂM:ham gia giao thông trên lãnh thổ nước CHXHCN VN phải tham gia bảo hiểm bắt buộc TNDS theo quy định của pháp luật.QUYỀN LỢI BẢO HIỂM:ham gia giao thông trên lãnh thổ nước CHXHCN VN phải tham gia bảo hiểm bắt buộc TNDS theo quy định của pháp luật.QUYỀN LỢI BẢO HIỂM:ham gia giao thông trên lãnh thổ nước CHXHCN VN phải tham gia bảo hiểm bắt buộc TNDS theo quy định của pháp luật.QUYỀN LỢI BẢO HIỂM:ham gia giao thông trên lãnh thổ nước CHXHCN VN phải tham gia bảo hiểm bắt buộc TNDS theo quy định của pháp luật.QUYỀN LỢI BẢO HIỂM:ham gia giao thông trên lãnh thổ nước CHXHCN VN phải tham gia bảo hiểm bắt buộc TNDS theo quy định của pháp luật.QUYỀN LỢI BẢO HIỂM:ham gia giao thông trên lãnh thổ nước CHXHCN VN phải tham gia bảo hiểm bắt buộc TNDS theo quy định của pháp luật.QUYỀN LỢI BẢO HIỂM:ham gia giao thông trên lãnh thổ nước CHXHCN VN phải tham gia bảo hiểm bắt buộc TNDS theo quy định của pháp luật.QUYỀN LỢI BẢO HIỂM:ham gia giao thông trên lãnh thổ nước CHXHCN VN phải tham gia bảo hiểm bắt buộc TNDS theo quy định của pháp luật.QUYỀN LỢI BẢO HIỂM:"),
          //   ),
          // )
        ],
      ),
    );
  }
}
