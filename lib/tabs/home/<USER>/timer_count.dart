import 'dart:async';

import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:shopping/constants/app_text_style.dart';
import 'package:shopping/pages/sim/item_sim_action_controller.dart';
import 'package:shopping/tabs/home/<USER>';

import '../../../constants/app_colors.dart';
import '../../../utils/convert/time_util.dart';

class TimerCount extends StatefulWidget {
  const TimerCount({super.key, this.resentOtp, this.rs, this.isCountDown, this.isBuySim, this.isCallApiSuccess});
  final Function()? resentOtp;
  final bool? rs;
  final bool? isCountDown;
  final bool? isBuySim;
  final bool? isCallApiSuccess;

  @override
  State<TimerCount> createState() => _TimerCountState();
}

class _TimerCountState extends State<TimerCount> {
  Timer? timer;
  int count = 180;
  @override
  void initState() {
    super.initState();
    Future.delayed(Duration(milliseconds: 500), () {
      Timer.periodic(const Duration(seconds: 1), (t) {
        if (timer == null) {
          timer = t;
        }
        if (count == 0) {
          //  t.cancel();
        } else {
          //
          if (widget.isBuySim ?? false) {
            // dùng khi mua sim
            if (Get.isRegistered<BookSimOTPController>()) {
              Get.find<BookSimOTPController>().setResend(false);
            } else {
              Get.put(BookSimOTPController()).setResend(false);
            }
          } else {
            // dùng khi mua gói cước thông thường
            if (Get.isRegistered<BuyPackageController>(tag: "_OTPBuyPackageWidget") == true) {
              Get.find<BuyPackageController>(tag: "_OTPBuyPackageWidget").setResend(false);
            } else {
              Get.put(BuyPackageController(), tag: "_OTPBuyPackageWidget").setResend(false);
            }
          }

          setState(() {
            count -= 1;
            //print(count);
          });
        }
      });
    });
  }

  @override
  void didUpdateWidget(mode) {
    super.didUpdateWidget(mode);
    if (widget.rs == true) {
      count = 180;
      // setState(() {});
    }
  }

  @override
  void dispose() {
    super.dispose();
    timer?.cancel();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Center(
          child: Visibility(
            visible: widget.isBuySim == true ? false : widget.isCountDown ?? true,
            child: RichText(
              text: TextSpan(
                  text: 'Thời gian còn hiệu lực:',
                  style: AppTextStyle.s13Regular.copyWith(
                    color: AppColors.black2E2E2E,
                  ),
                  children: <TextSpan>[
                    TextSpan(
                      text: TimeUtil.formatTime(time: count),
                      style: AppTextStyle.s13Bold.copyWith(
                        color: AppColors.redCE3722,
                      ),
                    ),
                  ]),
            ),
          ),
        ),
        const SizedBox(height: 10),
        Align(
          alignment: Alignment.center,
          child: RichText(
            text: TextSpan(
              text: 'Không nhận được mã OTP? ',
              style: AppTextStyle.s13Regular.copyWith(height: 20 / 14, color: AppColors.black2E2E2E),
              children: <TextSpan>[
                TextSpan(
                  recognizer: TapGestureRecognizer()
                    ..onTap = () {
                      if (widget.isBuySim == true) {
                        if (widget.isCallApiSuccess == true) {
                          widget.resentOtp?.call();
                        }
                      } else if (count == 0) {
                        if (widget.isCountDown == true) {
                          widget.resentOtp?.call();
                        }
                      }
                    },
                  text: 'Gửi lại',
                  style: AppTextStyle.s13SemiBold.copyWith(
                    height: 20 / 14,
                    color: widget.isBuySim == true
                        ? widget.isCallApiSuccess == true
                            ? widget.isCountDown == true
                                ? AppColors.blue005BF9
                                : AppColors.grey6A6A6A
                            : AppColors.grey6A6A6A
                        : count == 0
                            ? widget.isCountDown == true
                                ? AppColors.blue005BF9
                                : AppColors.grey6A6A6A
                            : AppColors.grey6A6A6A,
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}
