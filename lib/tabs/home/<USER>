import 'package:flutter/cupertino.dart';

import 'package:get/get.dart';
import 'package:shopping/apis/ShoppingRepository.dart';
import 'package:shopping/app_services/api_service.dart';
import 'package:shopping/app_services/app_storage.dart';
import 'package:shopping/enum/enum_payment_type.dart';
import 'package:shopping/loading_screen.dart';
import 'package:shopping/models/base_response.dart';
import 'package:shopping/models/list_bank_infor_response.dart';
import 'package:shopping/models/payment_infor_response.dart';
import 'package:shopping/shopping.dart';
import 'package:shopping/shopping_controller.dart';
import 'package:shopping/utils/extension/int_extension.dart';
import 'package:shopping/utils/extension/run_mode_extension.dart';
import 'package:shopping/utils/toast_utils.dart';
import 'package:url_launcher/url_launcher.dart';

class PaymentOnlineController extends GetxController {
  PaymentOnlineController({required this.ordId, required this.userPhoneNumber});
  final int? ordId;
  final String userPhoneNumber;
  PaymentOnlineType paymentOnlineChoosed = PaymentOnlineType.qr;
  bool isShowListBank = false;
  Rx<bool> isLoading = false.obs;
  ListBankInforResponse? listBankInforResponse;
  List<PaymentOnlineType?> listPaymentOnlineType = [];
  List<Bank> listBank = [];
  String? errMessage = '';
  bool isAccepted =
      Get.find<ShoppingController>().shopState == ShopState.LOGIN_OK;
  bool isAgreeToPolicy = true;
  Bank? bankSelected;
  // get infor pay
  PaymentInforResponse? paymentInforResponse;
  // String web browser
  String urlPayOnWeb = '';
  @override
  void onInit() async {
    super.onInit();
    await _initData();
    // isAgreeToPolicy = isAccepted;
    // update([
    //   PaymentOnlineControllerUpdateKey.updateAgreeToPolicy,
    //   PaymentOnlineControllerUpdateKey.updateStateEnabeButton
    // ]);
  }

  Future _initData() async {
    isLoading.toggle();
    BaseResponse<ListBankInforResponse> res =
        await DelayLoadingCallApi.delayCallApi(
            ShoppingRepository.instance.getListBankInfor());
    isLoading.toggle();
    if (res.code == CODE_SUCCESS) {
      listBankInforResponse = res.data;
      listPaymentOnlineType =
          listBankInforResponse?.getListPaymentOnineType ?? [];
      if (listPaymentOnlineType.length == 2) {
        listPaymentOnlineType = PaymentOnlineType.values;
      }
      paymentOnlineChoosed =
          listPaymentOnlineType.first ?? PaymentOnlineType.qr;
      checkShowBank();
      listBank = listBankInforResponse?.bank ?? [];
    } else {
      errMessage = res.message;
    }
    update([PaymentOnlineControllerUpdateKey.updateData]);
  }

  void onChangedPaymetOnline(PaymentOnlineType? value) {
    if (value == null) {
      return;
    }
    paymentOnlineChoosed = value;
    checkShowBank();
    update([
      PaymentOnlineControllerUpdateKey.updatePaymentType,
      PaymentOnlineControllerUpdateKey.updateStateEnabeButton
    ]);
  }

  bool isSelected(PaymentOnlineType value) {
    return paymentOnlineChoosed == value;
  }

  void checkShowBank() {
    if (paymentOnlineChoosed == PaymentOnlineType.atm) {
      isShowListBank = true;
    } else {
      isShowListBank = false;
    }
  }

  void onChangeStateAgree(bool value) {
    isAgreeToPolicy = value;
    update([
      PaymentOnlineControllerUpdateKey.updateAgreeToPolicy,
      PaymentOnlineControllerUpdateKey.updateStateEnabeButton
    ]);
  }

  void onClickBank(Bank item) {
    if (bankSelected == item) {
      bankSelected = null;
    } else {
      bankSelected = item;
    }
    update([
      PaymentOnlineControllerUpdateKey.updatePaymentType,
      PaymentOnlineControllerUpdateKey.updateStateEnabeButton
    ]);
  }

  Future onClickOpenWebPayOnline(
      BuildContext context, bool isFromSheetPick) async {
    await _getPaymentInfor();
    Map<String, dynamic>? map = paymentInforResponse?.toJson();
    if (paymentInforResponse == null || ordId == null || map == null) {
      return;
    }
    RunMode _rMode = ApiService.instance.runMode;
    String _baseUrlWeb = _rMode.getUrlOnWeb;
    String _payUrl = map.entries
        .map((e) =>
            '${Uri.encodeComponent(e.key)}=${Uri.encodeComponent('${e.value}')}')
        .join('&');
    String _payType = 'payType=${paymentOnlineChoosed.getValue}';
    String _customizeCallBack =
        'callBackUrl=$_baseUrlWeb/payment/payment-online/result/$ordId';
    String _customizeReqDomain = 'reqDomain=$_baseUrlWeb';
    String _customizeWindowColor = 'windowColor=%23ef5459';
    String _phoneNumber = 'phone_number=$userPhoneNumber';
    int? _appId = AppStorage.instance.getInt2(SKeysPK.appId) ??
        Get.find<ShoppingController>().appId;
    String _deepLink = 'deep_link=${_appId?.getDeepLink}&is_mobile=true';
    String _bankCode;
    if (isShowListBank && bankSelected != null) {
      _bankCode = '&bankCode=${bankSelected?.code}';
    } else {
      _bankCode = '';
    }
    urlPayOnWeb +=
        '$_baseUrlWeb/payment/payment-online/$ordId?$_payUrl&$_payType&$_customizeCallBack&$_customizeReqDomain&$_customizeWindowColor&$_phoneNumber&$_deepLink$_bankCode';
    try {
      // if (await canLaunchUrl(Uri.parse(urlPayOnWeb))) {
      if (isFromSheetPick) {
        Navigator.of(context)
          ..pop()
          ..pop();
      } else {
        Navigator.pop(context);
      }
      await launchUrl(Uri.parse(urlPayOnWeb),
          mode: LaunchMode.externalApplication);
      // } else {
      //   ToastUtils.showFail('Xảy ra lỗi khi mở trang thanh toán');
      // }
    } catch (e) {
      ToastUtils.showFail('Xảy ra lỗi khi mở trang thanh toán: $e');
    }
    // example link web:
    // https://muasam-stag.vivas.vn/payment/payment-online/2689?merId=EPAY000001&currency=VND&amount=2000&invoiceNo=V002689&goodsNm=VD2&notiUrl=https://api-muasam-stag.vivas.vn/v1/payment&description=Thanh+toan+d%C6%A1n+hang+V002689&userLanguage=VN&timeStamp=*************&merTrxId=EPAY000001*************5058&merchantToken=b99f1576a376db1f290c58384b4d342312b0e26299361a4f0404c20362227823&payType=DC&callBackUrl=https://muasam-stag.vivas.vn/payment/payment-online/result/2689&reqDomain=https://muasam-stag.vivas.vn&windowColor=%23ef5459&bankCode=ABBM
  }

  Future _getPaymentInfor() async {
    if (ordId == null) {
      return;
    }
    isLoading.toggle();
    BaseResponse<PaymentInforResponse> response =
        await ShoppingRepository.instance.getPaymentInfor(ordId: ordId ?? 0);
    isLoading.toggle();
    if (response.code == CODE_SUCCESS) {
      paymentInforResponse = response.data;
    }
  }
}

class PaymentOnlineControllerUpdateKey {
  static const String updateData = 'payment_online_controller/update_data';
  static const String updatePaymentType =
      'payment_online_controller/update_payment_type';
  static const String updateAgreeToPolicy =
      'payment_online_controller/update_agree_to_policy';
  static const String updateStateEnabeButton =
      'payment_online_controller/update_state_enable_button';
}
