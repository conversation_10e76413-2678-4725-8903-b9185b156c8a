import 'dart:convert';

AddressData AddressDataFromJson(String str) => AddressData.fromJson(json.decode(str));

class AddressData {
  bool? success;
  int? statusCode;
  int? code;
  List<Address>? data;

  AddressData({
    this.success,
    this.statusCode,
    this.code,
    this.data,
  });

  factory AddressData.fromJson(Map<String, dynamic> json) => AddressData(
        success: json["success"],
        statusCode: json["statusCode"],
        code: json["code"],
        data: List<Address>.from(json["data"].map((x) => Address.fromJson(x))),
      );
}

class Address {
  int id;
  String? name;
  String? itId;

  Address({required this.id, required this.name, this.itId});

  factory Address.fromJson(Map<String, dynamic> json) => Address(id: json["id"], name: json["name"], itId: json["it_id"]);
}
