import 'package:flutter/material.dart';
import 'package:shopping/constants/app_colors.dart';

class AppRadioButtonWidget<T> extends StatelessWidget {
  final T item;
  final T groupValue;
  final Widget widget;
  final ValueChanged<T?> onChanged;
  final Color? borderColor;
  const AppRadioButtonWidget(
      {super.key,
      required this.item,
      required this.groupValue,
      required this.onChanged,
      required this.widget,
      this.borderColor});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(vertical: 8, horizontal: 11),
      decoration: BoxDecoration(
          border: Border.all(width: 1, color: borderColor?? AppColors.greyEAEAEA),
          borderRadius: BorderRadius.circular(8)),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          SizedBox(
            width: 20,
            height: 32,
            child: Radio<T>(
              toggleable: true,
              activeColor: AppColors.redCE3722,
              fillColor: WidgetStateProperty.resolveWith<Color>(
                  (Set<WidgetState> states) {
                if (states.contains(WidgetState.selected)) {
                  return AppColors.redCE3722;
                }
                return AppColors.greyDFE4EA;
              }),
              materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
              value: item,
              groupValue: groupValue,
              onChanged: onChanged,
            ),
          ),
          const SizedBox(width: 15),
          Flexible(child: widget)
        ],
      ),
    );
  }
}
