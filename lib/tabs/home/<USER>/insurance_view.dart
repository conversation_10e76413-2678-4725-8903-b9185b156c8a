import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:shopping/constants/app_assets_paths.dart';
import 'package:shopping/constants/app_colors.dart';
import 'package:shopping/constants/app_text_style.dart';
import 'package:shopping/models/home_model.dart';
import 'package:shopping/pages/insurance/insurance_screen.dart';
import 'package:shopping/pages/insurance/widgets/item_insurance_widget.dart';

class InsuranceView extends StatelessWidget {
  final HomeData homeData;
  const InsuranceView({super.key, required this.homeData});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Expanded(
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  Image.network(homeData.icon ?? '', width: 28, height: 28,
                      errorBuilder: (_, __, ___) {
                    return SvgPicture.asset(
                      SvgPath.svgIconInsurance,
                      width: 28,
                      height: 28,
                    );
                  }),
                  SizedBox(width: 5),
                  Expanded(
                    child: Text(
                      homeData.name ?? ''.toUpperCase(),
                      style: AppTextStyle.s16Bold
                          .copyWith(overflow: TextOverflow.ellipsis),
                    ),
                  ),
                ],
              ),
            ),
            GestureDetector(
              onTap: () {
                Navigator.push(
                    context,
                    MaterialPageRoute(
                        builder: (context) =>
                            InsuranceScreen(homeData: homeData)));
              },
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Text("Xem thêm",
                      style: AppTextStyle.s12Medium.copyWith(
                        color: AppColors.black,
                      )),
                  SizedBox(width: 5),
                  SvgPicture.asset(SvgPath.svgWatchMore)
                ],
              ),
            )
          ],
        ),
        const SizedBox(height: 16),
        SizedBox(
          width: MediaQuery.of(context).size.width,
          height: 174,
          child: OverflowBox(
            maxWidth: MediaQuery.of(context).size.width,
            child: ListView.builder(
                physics: ClampingScrollPhysics(),
                scrollDirection: Axis.horizontal,
                itemBuilder: (_, index) {
                  return Container(
                    width: 200,
                    height: 174,
                    margin:
                        EdgeInsets.only(left: index == 0 ? 15 : 0, right: 15),
                    child: ItemInsuranceWidget(
                      productInsurance: homeData.contents?[index].product,
                      itemWidth: 200,
                    ),
                  );
                },
                itemCount: homeData.contents?.length ?? 0),
          ),
        )
      ],
    );
  }
}
