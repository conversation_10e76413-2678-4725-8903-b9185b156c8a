// ignore_for_file: unnecessary_string_escapes

import 'dart:math';

import 'package:get/get.dart';

/// use this mixin for all form field
mixin ValidatorUlti {
  static bool isPhoneNumber(String phoneNo) {
    final regExp = RegExp(r'(^(?:\+84|0)(?:3[2-9]|5[25689]|7[06-9]|8[1-9]|9\d)\d{7}$)');
    return regExp.hasMatch(phoneNo);
  }

  static String normalizePhoneNumber(String PhoneNo) {
    String number = PhoneNo.replaceAll(' ', '').replaceAll('.', '').replaceAll('(', '').replaceAll(')', '').replaceAll('+84', '0');
    if (number.startsWith('84')) {
      number = number.replaceFirst('84', '0');
    }
    if (number.isNotEmpty) {
      if (number[0] != '0') number = '0$number';
    }

    return number;
  }

  static String getFileSizeString({required int bytes, int decimals = 0}) {
    const suffixes = [" B", " KB", " MB", " GB", " TB"];
    if (bytes == 0) return '0${suffixes[0]}';
    var i = (log(bytes) / log(1024)).floor();
    return ((bytes / pow(1024, i)).toStringAsFixed(decimals)) + suffixes[i];
  }

  static bool isValidUsername(userName) {
    if (GetUtils.isEmail(userName) || isPhoneNumber(userName)) return true;
    return false;
  }
}
