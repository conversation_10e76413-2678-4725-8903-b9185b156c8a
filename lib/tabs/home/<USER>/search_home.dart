import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:shopping/constants/enum_package_type.dart';
import 'package:shopping/loading_screen.dart';
import 'package:shopping/tabs/home/<USER>/item_tab_widget.dart';
import 'package:shopping/tabs/home/<USER>/search_home_controller.dart';

import '../../../constants/app_assets_paths.dart';
import '../../../constants/app_colors.dart';
import '../../../constants/app_text_style.dart';
import 'search_home_widget.dart';

class SearchHome extends GetView<SearchHomeController> {
  const SearchHome({super.key});

  @override
  Widget build(BuildContext context) {
    return GetBuilder<SearchHomeController>(
        init: SearchHomeController(context: context),
        builder: (controller) {
          return LoadingOverlay(
            child: Scaffold(
              backgroundColor: AppColors.white,
              appBar: AppBar(
                titleSpacing: 0,
                backgroundColor: AppColors.white,
                elevation: 5,
                shadowColor: AppColors.black.withOpacity(0.4),
                surfaceTintColor: AppColors.white,
                leading: InkWell(
                  child: SizedBox(
                    width: 20,
                    height: 20,
                    // color: AppColors.white,
                    child: SvgPicture.asset(
                      SvgPath.svgIconBack,
                      fit: BoxFit.scaleDown,
                    ),
                  ),
                  onTap: () {
                    Navigator.pop(context);
                  },
                ),
                centerTitle: false,
                title: Container(
                  height: 44,
                  padding: const EdgeInsets.only(right: 24),
                  child: TextField(
                    focusNode: controller.focusNode,
                    style: AppTextStyle.s14Regular
                        .copyWith(color: AppColors.black2E2E2E),
                    maxLength: 200,
                    controller: controller.searchController,
                    // onChanged: controller.onChangeSetValue,
                    cursorColor: AppColors.black2E2E2E,
                    // onChanged: (value) {
                    //   controller.onSearch(value);
                    // },
                    onEditingComplete: controller.onSearch,
                    keyboardType: TextInputType.text,
                    autofocus: true,
                    decoration: InputDecoration(
                      contentPadding:
                          EdgeInsets.symmetric(vertical: 10, horizontal: 15),
                      hintText: 'Nhập từ khoá tìm kiếm',
                      hintStyle: AppTextStyle.s14Regular
                          .copyWith(color: AppColors.black2E2E2E),
                      filled: true,
                      fillColor: AppColors.greyF5F6F9,
                      counterText: '',
                      border: const UnderlineInputBorder(),
                      enabledBorder: OutlineInputBorder(
                        borderSide: const BorderSide(
                            width: 1, color: AppColors.greyE5E5E5),
                        borderRadius: BorderRadius.circular(100),
                      ),
                      focusedBorder: OutlineInputBorder(
                        borderSide: const BorderSide(
                            width: 1, color: AppColors.greyE5E5E5),
                        borderRadius: BorderRadius.circular(100),
                      ),
                      suffixIcon: Row(
                        mainAxisSize: MainAxisSize.min,
                        mainAxisAlignment: MainAxisAlignment.end,
                        children: [
                          GestureDetector(
                            behavior: HitTestBehavior.opaque,
                            onTap: controller.onSearch,
                            child: Padding(
                              padding: const EdgeInsets.all(15),
                              child: SvgPicture.asset(
                                SvgPath.svgIconSearch,
                              ),
                            ),
                          )
                        ],
                      ),
                    ),
                  ),
                ),
              ),
              body: Padding(
                padding:
                    const EdgeInsets.symmetric(horizontal: 15, vertical: 10),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    SizedBox(
                      height: 30,
                      child: ListView.builder(
                        scrollDirection: Axis.horizontal,
                        itemCount: controller.listTab.length,
                        itemBuilder: (context, index) {
                          return Obx(() => GestureDetector(
                                onTap: () {
                                  controller
                                      .onChangeTab(controller.listTab[index]);
                                },
                                child: ItemTabWidget(
                                  name: controller.listTab[index]
                                      .getNameWithCount(controller.listCount?[
                                          controller
                                              .listTab[index].getIdCount]),
                                  isActive: controller.listTab[index] ==
                                      controller.orderTypeChoosed.value,
                                ),
                              ));
                        },
                      ),
                    ),
                    SizedBox(height: 20),
                    Expanded(
                      child: SearchHomeWidget(controller: controller),
                    )
                  ],
                ),
              ),
            ),
          );
        });
  }
}
