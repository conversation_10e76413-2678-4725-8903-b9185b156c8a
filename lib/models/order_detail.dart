import 'package:shopping/enum/enum_order_product_type.dart';
import 'package:shopping/models/order.dart';

class OrderDetailResponse {
  OrderDetail? data;

  OrderDetailResponse({
    this.data,
  });

  OrderDetailResponse.fromJson(Map<String, dynamic> json) {
    data = OrderDetail.fromJson(json["data"]);
  }
}

class OrderDetail {
  final List<ProductItem>? productItems;
  final int? id;
  final String? code;
  final int? createdAt;
  final String? timeAt;
  final int? totalPrice;
  final int? status;
  final int? type;
  final String? supplier;
  final String? thoiGian;
  final String? gcnBh;
  final String? linkTc;

  OrderDetail({
    this.productItems,
    this.id,
    this.code,
    this.createdAt,
    this.timeAt,
    this.totalPrice,
    this.status,
    this.type,
    this.supplier,
    this.thoiGian,
    this.gcnBh,
    this.linkTc,
  });

  factory OrderDetail.fromJson(Map<String, dynamic> json) => OrderDetail(
        productItems: json["product_items"] == null
            ? []
            : List<ProductItem>.from(
                json["product_items"]!.map((x) => ProductItem.fromJson(x))),
        id: json["id"],
        code: json["code"],
        createdAt: json["created_at"],
        timeAt: json["time_at"],
        totalPrice: json["total_price"],
        status: json["status"],
        type: json["type"],
        supplier: json["supplier"],
        thoiGian: json["thoi_gian"],
        gcnBh: json["gcn_bh"],
        linkTc: json["link_tc"],
      );

  Map<String, dynamic> toJson() => {
        "product_items": productItems == null
            ? []
            : List<dynamic>.from(productItems!.map((x) => x.toJson())),
        "id": id,
        "code": code,
        "created_at": createdAt,
        "time_at": timeAt,
        "total_price": totalPrice,
        "status": status,
      };
  String? get getItemProductInfor {
    String dataName = "";
    String simName = "";
    if (productItems == null || productItems?.length == 0) {
      return "";
    }
    for (int i = 0; i < productItems!.length; i++) {
      // bảo hiểm abic
      if (isInsABICOrder) {
        return productItems?[0].productNameStr ?? 'N/A';
      }
      if (productItems?[i].getOrderProductType == OrderProductType.goicuoc) {
        dataName = '${productItems?[i].productName ?? ""}';
        if (productItems?.length == 1) {
          return dataName;
        }
      }
      if (productItems?[i].getOrderProductType == OrderProductType.sim) {
        simName = '${productItems?[i].productNameStr ?? ""}';
      }
    }
    if (dataName.isNotEmpty) {
      return '$simName, Gói $dataName';
    } else {
      return '$simName';
    }
  }

  bool get isInsABICOrder {
    return type == 3;
  }
}
