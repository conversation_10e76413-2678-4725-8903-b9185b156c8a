import 'package:shopping/constants/app_assets_paths.dart';

enum LogoType {
  facebook,
  youtube,
  viber,
  whatsapp,
  instagram,
  tiktok,
  messenger,
  shopee,
  vieOn,
  nhaccuatui,
  lienquanmobile,
  fifaOnline4,
  sendo,
  fptplay,
  galaxyplay,
  vilearn,
  dinodihoc,
  unica,
  smartedup<PERSON>,
  kaspersky,
  suppernews,
  waka,
  freefire
}
extension LogoTypeExt on LogoType{
  int get getValue{
    switch(this){
      case LogoType.facebook:
      return 1;
      case LogoType.tiktok:
      return 2;
      case LogoType.youtube:
      return 3;
      case LogoType.vieOn:
      return 4;
      case LogoType.nhaccuatui:
      return 5;
      case LogoType.viber:
      return 6;
      case LogoType.whatsapp:
      return 7;
      case LogoType.lienquanmobile:
      return 8;
      case LogoType.freefire:
      return 9;
      case LogoType.fifaOnline4:
      return 10;
      case LogoType.messenger:
      return 11;
      case LogoType.instagram:
      return 12;
      case LogoType.shopee:
      return 13;
      case LogoType.sendo:
      return 14;
      case LogoType.fptplay:
      return 15;
      case LogoType.galaxyplay:
      return 16;
      case LogoType.vilearn:
      return 17;
      case LogoType.dinodihoc:
      return 18;
      case LogoType.unica:
      return 19;
      case LogoType.smartedupica:
      return 20;
      case LogoType.kaspersky:
      return 21;
      case LogoType.suppernews:
      return 22;
      case LogoType.waka:
      return 23;
    }
  }

  String get getLogoPath{
     switch(this){
      case LogoType.facebook:
      return SvgPath.svgLogoFacebook;
      case LogoType.tiktok:
      return SvgPath.svgLogoTiktok;
      case LogoType.youtube:
      return SvgPath.svgLogoYoutube;
      case LogoType.vieOn:
      return SvgPath.svgLogoVieon;
      case LogoType.nhaccuatui:
      return SvgPath.svgLogoNhaccuatui;
      case LogoType.viber:
      return SvgPath.svgLogoViber;
      case LogoType.whatsapp:
      return SvgPath.svgLogoWhatsapp;
      case LogoType.lienquanmobile:
      return SvgPath.svgLogoLienquanmobile;
      case LogoType.freefire:
      return SvgPath.svgLogoFreefire;
      case LogoType.fifaOnline4:
      return SvgPath.svgLogoFifaonline4;
      case LogoType.messenger:
      return SvgPath.svgLogoMessenger;
      case LogoType.instagram:
      return SvgPath.svgLogoInstagram;
      case LogoType.shopee:
      return SvgPath.svgLogoShopee;
      case LogoType.sendo:
      return SvgPath.svgLogoSendo;
      case LogoType.fptplay:
      return SvgPath.svgLogoFptplay;
      case LogoType.galaxyplay:
      return SvgPath.svgLogoGalaxyplay;
      case LogoType.vilearn:
      return SvgPath.svgLogoVilearn;
      case LogoType.dinodihoc:
      return SvgPath.svgLogodinodihoc;
      case LogoType.unica:
      return SvgPath.svgLogoUnica;
      case LogoType.smartedupica:
      return SvgPath.svgLogoSmartedupia;
      case LogoType.kaspersky:
      return SvgPath.svgLogoKaspersky;
      case LogoType.suppernews:
      return SvgPath.svgLogoSupernews;
      case LogoType.waka:
      return SvgPath.svgLogoWaka;
    }
  }
}
