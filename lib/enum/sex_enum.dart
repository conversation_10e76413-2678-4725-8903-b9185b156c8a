enum SexEnum {
  fale(1, '<PERSON>'),
  female(2, '<PERSON><PERSON>'),
  other(0, '<PERSON>há<PERSON>');

  final int id;
  final String name;
  const SexEnum(this.id, this.name);

  factory SexEnum.fromId(int? id) {
    switch (id) {
      case 1:
        return SexEnum.fale;
      case 2:
        return SexEnum.female;
      default:
        return SexEnum.other;
    }
  }

  factory SexEnum.fromName(String name) {
    if (name.contains('Nam')) {
      return SexEnum.fale;
    } else if (name.contains('Nữ')) {
      return SexEnum.female;
    } else {
      return SexEnum.other;
    }
  }
}
