import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:shopping/constants/app_colors.dart';
import 'package:shopping/constants/app_text_style.dart';
import 'package:shopping/constants/enum_step_ord_insurance.dart';
import 'package:shopping/models/insurance_create_order_request_form.dart';
import 'package:shopping/pages/insurance/pages/moto/moto_step_confirm_controller.dart';
import 'package:shopping/pages/insurance/widgets/common_buy_insurance_view.dart';
import 'package:shopping/pages/package/widgets/filter_endraw_widget.dart';
import 'package:shopping/pages/sim/widgets/sim_text_field_widget.dart';

class MotoStepConfirmScreen extends StatelessWidget {
  const MotoStepConfirmScreen(
      {super.key, required this.insuranceCreateOrdRequestForm});
  final InsuranceCreateOrdRequestForm insuranceCreateOrdRequestForm;
  @override
  Widget build(BuildContext context) {
    return GetBuilder<MotoStepConfirmController>(
      init: MotoStepConfirmController(
          insuranceCreateOrdRequestForm: insuranceCreateOrdRequestForm),
      id: MotoStepCofirmControlelrUpdateKey.updateCheckboxConfirm,
      builder: (controller) => Obx(() => CommonBuyInsuranceView(
            title: 'BẢO HIỂM TNDS XE MÁY',
            stepType: StepOrdInsuranceType.confirmInfor,
            bottonWidget: GetBuilder<MotoStepConfirmController>(
              id: MotoStepCofirmControlelrUpdateKey.updateCheckboxConfirm,
              builder: (controller) => InkWell(
                splashColor: Colors.transparent,
                onTap: () {
                  controller.onChangeCofirm(!controller.isConfirmed);
                },
                child: CheckBoxLineWidget(
                  title:
                      'Tôi xác nhận và cam đoan các thông tin trên là chính xác',
                  isSelected: controller.isConfirmed,
                  onChanged: (p0) {
                    controller.onChangeCofirm(p0);
                  },
                ),
              ),
            ),
            lableBtn: 'THANH TOÁN',
            enableBtn: controller.isConfirmed,
            isLoadingBtn: controller.isLoadingBtn.value,
            onClickBtn: () {
              controller.onClickBtn(context: context);
            },
            body: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Padding(
                  padding: EdgeInsets.only(top: 18),
                  child: Text(
                    'Thông tin chủ xe',
                    style: AppTextStyle.s18SemiBold
                        .copyWith(color: AppColors.black),
                  ),
                ),
                const SizedBox(height: 11),
                Obx(() => SIMTextFieldWidget(
                      title: 'Họ tên chủ xe',
                      isRequired: true,
                      maxLength: 200,
                      hintText: 'Nhập họ và tên',
                      text: controller.tenChuXeTxt,
                      errorText: controller.validateTenChuXe(),
                      onChanged: (p0) {
                        controller.isValidateTenChuXe.value = false;
                        controller.onChangeChuXe(p0);
                      },
                      onSubmitted: (p0) {
                        controller.isValidateTenChuXe.value = true;
                        controller.onChangeChuXe(p0);
                      },
                    )),
                const SizedBox(height: 11),
                GetBuilder<MotoStepConfirmController>(
                  id: MotoStepCofirmControlelrUpdateKey.updateBienSoXeMay,
                  builder: (controller) => SIMTextFieldWidget(
                    errorText: controller.validateBsx(),
                    title: 'Biển số xe',
                    isRequired: true,
                    hintText: 'Nhập biển số xe',
                    text: controller.bienSoXeTxt,
                    maxLength: 11,
                    onChanged: (p0) {
                      controller.updateStateBsx(false);
                      controller.onChangeBsx(p0);
                    },
                    onSubmitted: (p0) {
                      controller.updateStateBsx(true);
                      controller.onChangeBsx(p0);
                    },
                  ),
                ),
                Padding(
                  padding: EdgeInsets.only(top: 11),
                  child: Text(
                    'Thông tin chủ hợp đồng',
                    style: AppTextStyle.s18SemiBold
                        .copyWith(color: AppColors.black),
                  ),
                ),
                const SizedBox(height: 11),
                SIMTextFieldWidget(
                  title: 'Họ tên chủ hợp đồng',
                  isRequired: true,
                  maxLength: 200,
                  hintText: 'Nhập họ và tên',
                  text: controller.tenChuHDTxt,
                  errorText: controller.validateTenChuHD(),
                  onChanged: (p0) {
                    controller.isValidateTenChuHD.value = false;
                    controller.onChangeTenChuHD(p0);
                  },
                  onSubmitted: (p0) {
                    controller.isValidateTenChuHD.value = true;
                    controller.onChangeTenChuHD(p0);
                  },
                ),
                const SizedBox(height: 11),
                SIMTextFieldWidgetOnlyNumber(
                  title: 'Số điện thoại liên hệ',
                  isRequired: true,
                  maxLength: 12,
                  keyBoardType: TextInputType.number,
                  hintText: 'Nhập số điện thoại liên hệ',
                  text: controller.sdtTxt,
                  errorText: controller.validateSdt(),
                  onChanged: (p0) {
                    controller.isValidateSdt.value = false;
                    controller.onChangeSdt(p0);
                  },
                  onSubmitted: (p0) {
                    controller.isValidateSdt.value = true;
                    controller.onChangeSdt(p0);
                  },
                ),
                const SizedBox(height: 11),
                Obx(
                  () => SIMTextFieldWidget(
                    title: 'Email',
                    maxLength: 200,
                    hintText: 'Nhập email',
                    text: controller.emailTxt,
                    errorText: controller.validateEmail(),
                    onChanged: (p0) {
                      controller.isValidateEmail.value = false;
                      controller.onChangeEmail(p0);
                    },
                    onSubmitted: (p0) {
                      controller.isValidateEmail.value = true;
                      controller.onChangeEmail(p0);
                    },
                  ),
                ),
                const SizedBox(height: 11),
                SIMTextFieldWidget(
                  title: 'Địa chỉ',
                  maxLength: 200,
                  hintText: 'Nhập địa chỉ',
                  text: controller.diaChiTxt,
                  contentPadding:
                      EdgeInsets.symmetric(horizontal: 10, vertical: 13),
                  onChanged: (p0) {
                    controller.onChangeDiaChi(p0);
                  },
                  onSubmitted: (p0) {
                    controller.onChangeDiaChi(p0);
                  },
                ),
                const SizedBox(height: 5),
              ],
            ),
          )),
    );
  }
}
