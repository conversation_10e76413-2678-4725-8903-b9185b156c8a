enum HomeType { product, news, banner, hotDeal, sim, reportFlag, abic }

extension HomeTypeExt on HomeType {
  int get getValue {
    switch (this) {
      case HomeType.product:
        return 1;
      case HomeType.news:
        return 2;
      case HomeType.banner:
        return 3;
      case HomeType.hotDeal:
        return 4;
      case HomeType.sim:
        return 5;
      case HomeType.abic:
        return 6;
      case HomeType.reportFlag:
        return 99;
    }
  }
}
