import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:shopping/constants/app_assets_paths.dart';
import 'package:shopping/constants/app_colors.dart';
import 'package:shopping/constants/app_text_style.dart';
import 'package:shopping/enum/enum_payment_type.dart';
import 'package:shopping/models/product.dart';
import 'package:shopping/tabs/home/<USER>';
import 'package:shopping/widgets/app_radio_button_widget.dart';

class PickPaymentTypeWidget extends StatelessWidget {
  const PickPaymentTypeWidget(
      {super.key,
      required this.openOTP,
      required this.product,
      required this.phoneNumber,
      required this.referCode
      // required this.funcPayOnline, required this.funcPayTKC
      });
  final Product product;
  final Function(int?) openOTP;
  final String phoneNumber;
  final String referCode;
  // final Function() funcPayOnline;
  // final Function() funcPayTKC;
  @override
  Widget build(BuildContext context) {
    return GetBuilder<BuyPackageController>(
        init: BuyPackageController(),
        id: "updatePaymentType",
        tag: "PickPaymentBuyPackageWidget",
        builder: (controller) => Padding(
              padding: const EdgeInsets.only(
                  left: 15, right: 15, top: 6, bottom: 35),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    height: 22,
                    child: Stack(
                      children: [
                        Padding(
                          padding: const EdgeInsets.only(bottom: 3),
                          child: Align(
                            alignment: Alignment.center,
                            child: Container(
                              width: 45,
                              height: 5,
                              decoration: BoxDecoration(
                                  color: AppColors.greyE5E6EC,
                                  borderRadius: BorderRadius.circular(10)),
                            ),
                          ),
                        ),
                        Align(
                          alignment: Alignment.centerRight,
                          child: GestureDetector(
                            onTap: () {
                              // close
                              Navigator.of(context).pop();
                            },
                            child: SvgPicture.asset(SvgPath.svgIconClose),
                          ),
                        )
                      ],
                    ),
                  ),
                  Center(
                    child: Text("Xác nhận thanh toán",
                        textAlign: TextAlign.center,
                        style: AppTextStyle.s18Bold),
                  ),
                  const SizedBox(height: 16),
                  Text("Chọn phương thức thanh toán",
                      textAlign: TextAlign.center,
                      style: AppTextStyle.s13Regular),
                  const SizedBox(height: 11),
                  ListView.separated(
                      padding: EdgeInsets.zero,
                      shrinkWrap: true,
                      physics: NeverScrollableScrollPhysics(),
                      itemBuilder: (_, index) {
                        PaymentType item = PaymentType.values[index];
                        return InkWell(
                          splashColor: Colors.transparent,
                          onTap: () {
                            controller.onChangePayment(item);
                          },
                          child: AppRadioButtonWidget<PaymentType>(
                              item: item,
                              groupValue: controller.paymentTypeChoosed,
                              onChanged: (PaymentType? value) {
                                if (value != null)
                                  controller.onChangePayment(value);
                              },
                              borderColor: controller.isSelected(item)
                                  ? AppColors.redCE3722
                                  : null,
                              widget: Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  SvgPicture.asset(item.getIcon),
                                  const SizedBox(width: 18),
                                  Flexible(
                                    child: Text(
                                      item.getName,
                                      style: AppTextStyle.s14Medium.copyWith(
                                          color: AppColors.black262626,
                                          overflow: TextOverflow.ellipsis),
                                      maxLines: 2,
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                  ),
                                ],
                              )),
                        );
                      },
                      separatorBuilder: (_, __) => const SizedBox(height: 15),
                      itemCount: PaymentType.values.length),
                  const SizedBox(height: 14),
                  Row(
                    children: [
                      Expanded(
                        child: _ButtonWidget(
                          lable: 'HỦY',
                          bgColor: AppColors.greyCACACA,
                          lableColor: AppColors.black2E2E2E,
                          onClick: () {
                            Navigator.pop(context);
                          },
                        ),
                      ),
                      const SizedBox(width: 10),
                      Expanded(
                        child: Obx(() => _ButtonWidget(
                              child: controller.isSendServer.value
                                  ? CircularProgressIndicator(
                                      color: AppColors.whiteFFFFFF,
                                    )
                                  : null,
                              lable: "TIẾP TỤC",
                              bgColor: AppColors.green30AAB7,
                              lableColor: AppColors.white,
                              onClick: () {
                                if (controller.isSendServer.value) {
                                  return;
                                }
                                controller.onDonePickPaymentType(
                                    context: context,
                                    product: product,
                                    openOTP: openOTP,
                                    phoneNumber: phoneNumber,
                                    referCode: referCode);
                              },
                            )),
                      ),
                    ],
                  )
                ],
              ),
            ));
  }
}

class _ButtonWidget extends StatelessWidget {
  const _ButtonWidget(
      {this.child,
      required this.lable,
      required this.bgColor,
      required this.lableColor,
      required this.onClick});
  final String lable;
  final Color lableColor;
  final Color bgColor;
  final Function() onClick;
  final Widget? child;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () {
        onClick.call();
      },
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8),
          color: bgColor,
        ),
        padding: EdgeInsets.symmetric(
            vertical: child != null ? 4.5 : 12, horizontal: 12),
        child: Center(
          child: child != null
              ? child
              : Text(
                  lable,
                  style: AppTextStyle.s16Medium
                      .copyWith(color: lableColor, height: 21 / 16),
                ),
        ),
      ),
    );
  }
}

// class PickPaymentTypeController extends GetxController {
//   PaymentType paymentTypeChoosed = PaymentType.tkc;

//   void onChangePayment(PaymentType value) {
//     if (paymentTypeChoosed != value) {
//       paymentTypeChoosed = value;
//       update([PickPaymentTypeControllerUpdateKey.updatePaymentType]);
//     }
//   }

//   bool isSelected(PaymentType value) {
//     return paymentTypeChoosed == value;
//   }
// }

// class PickPaymentTypeControllerUpdateKey {
//   static const String updatePaymentType =
//       'pick_payment_type_controller/update_payment_type';
// }
