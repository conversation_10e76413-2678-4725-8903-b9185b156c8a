import 'package:get/get.dart';
import 'package:shopping/enum/enum_payment_type.dart';

class ListBankInforResponse {
  List<String>? payType;
  List<String>? payTypeSim;
  List<Bank>? bank;

  ListBankInforResponse({
    this.payType,
    this.bank,
    this.payTypeSim,
  });

  factory ListBankInforResponse.fromJson(Map<String, dynamic> json) =>
      ListBankInforResponse(
        payType: json["payType"] == null
            ? []
            : List<String>.from(json["payType"]!.map((x) => x)),
        payTypeSim: json["payTypeSim"] == null
            ? []
            : List<String>.from(json["payTypeSim"]!.map((x) => x)),
        bank: json["bank"] == null
            ? []
            : List<Bank>.from(json["bank"]!.map((x) => Bank.fromJson(x))),
      );
  List<PaymentOnlineType?> get getListPaymentOnineType {
    if (payType == null || payType?.length == 0) return [];
    return (payType ?? [])
        .map((e) =>
            PaymentOnlineType.values.firstWhereOrNull((p0) => p0.getValue == e))
        .toList();
  }

  List<PaymentOnlineType?> get getListPayTypeSim {
    if (payTypeSim == null || payTypeSim?.length == 0) return [];
    return (payTypeSim ?? [])
        .map((e) =>
            PaymentOnlineType.values.firstWhereOrNull((p0) => p0.getValue == e))
        .toList();
  }
}

class Bank {
  String? code;
  String? name;
  String? logo;

  Bank({
    this.code,
    this.name,
    this.logo,
  });

  factory Bank.fromJson(Map<String, dynamic> json) => Bank(
        code: json["code"],
        name: json["name"],
        logo: json["logo"],
      );
}
