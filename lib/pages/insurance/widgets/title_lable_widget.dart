import 'package:flutter/material.dart';
import 'package:shopping/constants/app_colors.dart';
import 'package:shopping/constants/app_text_style.dart';

class TitleLable extends StatelessWidget {
  const TitleLable(
      {super.key, required this.lable, this.isRequired, this.style});
  final String lable;
  final bool? isRequired;
  final TextStyle? style;
  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(top: 11),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(lable,
              style: style ??
                  AppTextStyle.s14Medium.copyWith(color: AppColors.black)),
          (isRequired ?? false)
              ? Text(
                  " *",
                  style: style?.copyWith(color: AppColors.redE60E00) ??
                      AppTextStyle.s14Medium
                          .copyWith(color: AppColors.redE60E00),
                )
              : SizedBox()
        ],
      ),
    );
  }
}
