import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:shopping/apis/ShoppingRepository.dart';
import 'package:shopping/app_services/api_service.dart';
import 'package:shopping/app_services/app_storage.dart';
import 'package:shopping/enum/enum_ins_land.dart';
import 'package:shopping/loading_screen.dart';
import 'package:shopping/models/base_response.dart';
import 'package:shopping/models/insurance_create_order_request_form.dart';
import 'package:shopping/models/insurance_create_order_response.dart';
import 'package:shopping/shopping_controller.dart';
import 'package:shopping/utils/extension/int_extension.dart';
import 'package:shopping/utils/extension/string_extension.dart';
import 'package:shopping/widgets/app_dialog.dart';
import 'package:url_launcher/url_launcher.dart';

class LandStepConfirmController extends GetxController {
  LandStepConfirmController({required this.insuranceCreateOrdRequestForm});

  // tên chủ nhà
  String? tenChuNhaTxt;
  RxBool isValidateTenChuNha = false.obs;

  // cccd
  String? cccdTxt;
  RxBool isValidateCccd = false.obs;

  // sdt
  String? sdtTxt;
  RxBool isValidateSdt = false.obs;

  // diachi
  String? diaChiNhaTxt;
  RxBool isValidateDiaChiNha = false.obs;

  // số tầng
  int? soTang;
  String? soTangTxt;
  RxBool isValidateSoTang = false.obs;

  // diện tích
  num? dienTich;
  String? dientichTxt;
  RxBool isValidateDienTich = false.obs;

  // quyền sở hữu
  LandStatus landStatusChoosed = LandStatus.chuSoHuu;

  // vay thế chấp
  bool checkVayTheChap = false;

  // tên chủ hợp đồng
  String? tenChuHDTxt;
  RxBool isValidateTenChuHD = false.obs;

  // sdt liên hệ chủ hợp đồng
  String? sdtChuHDTxt;
  RxBool isValidateSdtChuHD = false.obs;

  // enail chủ hợp đồng
  String? emailTxt;
  RxBool isValidateEmail = false.obs;

  // diachi
  String? diaChiChuHdTxt;

  bool isConfirmed = false;
  //
  InsuranceCreateOrdRequestForm insuranceCreateOrdRequestForm;
  String? urlPayInsurance;

  RxBool isLoadingBtn = false.obs;
  @override
  void onInit() {
    super.onInit();
    _initData();
  }

  void _initData() {
    if (Get.isRegistered<ShoppingController>()) {
      tenChuHDTxt = Get.find<ShoppingController>().user?.fullname;
      sdtChuHDTxt = Get.find<ShoppingController>().user?.phoneNumber;
      diaChiChuHdTxt = Get.find<ShoppingController>().user?.addressFull;
    }
  }

  void onChangeTenChuNha(String newValue) {
    tenChuNhaTxt = newValue;
  }

  String? validateTenChuNha() {
    if ((tenChuNhaTxt == null || (tenChuNhaTxt ?? '').isEmpty) &&
        isValidateTenChuNha.value == true) {
      return 'Vui lòng nhập họ tên chủ nhà';
    }
    return null;
  }

  void onChangeCccd(String newValue) {
    cccdTxt = newValue;
  }

  String? validateCccd() {
    if ((cccdTxt == null || (cccdTxt ?? '').isEmpty) &&
        isValidateCccd.value == true) {
      return 'Vui lòng nhập CCCD/Hộ chiếu';
    }
    return null;
  }

  void onChangeSdt(String? newValue) {
    if (newValue == null) {
      return;
    }
    sdtTxt = newValue;
  }

  String? validateSdt() {
    if (isValidateSdt.value == true) {
      if (sdtTxt == null || (sdtTxt ?? '').isEmpty) {
        return 'Vui lòng nhập số điện thoại';
      }
      // regex phone
      if ((sdtTxt ?? '').validatePhoneNumber() == false) {
        return 'Số điện thoại không hợp lệ ';
      }
    }
    return null;
  }

  void onChangeDiaChiNha(String? newValue) {
    if (newValue == null) {
      return;
    }
    diaChiNhaTxt = newValue;
  }

  String? validateDiaChiNha() {
    if (isValidateDiaChiNha.value == true) {
      if (diaChiNhaTxt == null || (diaChiNhaTxt ?? '').isEmpty) {
        return 'Vui lòng nhập địa chỉ';
      }
    }
    return null;
  }

  void onChangeSoTang(String newValue) {
    soTangTxt = newValue;
  }

  String? validateSoTang() {
    if (isValidateSoTang.value == true) {
      if (soTangTxt == null || (soTangTxt ?? '').isEmpty) {
        return 'Vui lòng nhập số tầng';
      }
    }
    return null;
  }

  void onChangeDienTich(String newValue) {
    dientichTxt = newValue.replaceAll(',', '.');
  }

  String? validateDienTich() {
    if (isValidateDienTich.value == true) {
      if (dientichTxt == null || (dientichTxt ?? '').isEmpty) {
        return 'Vui lòng nhập diện tích';
      }
    }
    return null;
  }

  void onChangeQuyenSH(LandStatus? newValue) {
    if (newValue == null || landStatusChoosed == newValue) {
      return;
    }
    landStatusChoosed = newValue;
    landStatusChoosed = newValue;
    update([LandStepCofirmControllerUpdateKey.updateLandStatus]);
  }

  void updateCheckboxVay() {
    checkVayTheChap = !checkVayTheChap;
    update([LandStepCofirmControllerUpdateKey.updateCheckboxVay]);
  }

  void onChangeTenChuHD(String newValue) {
    tenChuHDTxt = newValue;
  }

  String? validateTenChuHd() {
    if (isValidateTenChuHD.value == true) {
      if (tenChuHDTxt == null || (tenChuHDTxt ?? '').isEmpty) {
        return 'Vui lòng nhập tên chủ hợp đồng';
      }
    }
    return null;
  }

  void onChangeSdtLH(String newValue) {
    sdtChuHDTxt = newValue;
  }

  String? validateSdtLH() {
    if (isValidateSdtChuHD.value == true) {
      if (sdtChuHDTxt == null || (sdtChuHDTxt ?? '').isEmpty) {
        return 'Vui lòng nhập số điện thoại liên hệ';
      }
    }
    return null;
  }

  void onChangeEmail(String newValue) {
    emailTxt = newValue;
  }

  String? validateEmail() {
    if (isValidateEmail.value == true) {
      if (emailTxt == null || (emailTxt ?? '').isEmpty) {
        return null;
      }
      if ((emailTxt ?? '').validateEmail()) {
        return 'Email không hợp lệ';
      }
    }
    return null;
  }

  void onChangeDiaChiChuHD(String newValue) {
    diaChiChuHdTxt = newValue;
  }

  void onChangeCofirm(bool? newValue) {
    if (newValue == null) {
      return;
    }
    isConfirmed = newValue;
    update([LandStepCofirmControllerUpdateKey.updateCheckboxConfirm]);
  }

  void _convertData() {
    soTang = int.parse(soTangTxt ?? '1');
    dienTich = num.parse(dientichTxt ?? '0');
  }

  void _onUpdateFormRequest() {
    insuranceCreateOrdRequestForm = insuranceCreateOrdRequestForm.copyWith(
      chuXe: tenChuNhaTxt,
      cccd: cccdTxt,
      phoneNumber: sdtTxt,
      diaChiNha: diaChiNhaTxt,
      soTang: soTang,
      dienTich: dienTich,
      qSuHuu: landStatusChoosed.getValue,
      chuHd: tenChuHDTxt,
      phoneNumberHd: sdtChuHDTxt,
      email: emailTxt,
      diaChiHd: diaChiChuHdTxt,
    );
  }

  Future<void> _openWebPayInsurance(BuildContext context) async {
    try {
      if (urlPayInsurance == null || (urlPayInsurance ?? '').isEmpty) {
        dialogAsk(context, 'Lỗi không tìm thấy trang thanh toán!');
      } else {
        // back về home
        Navigator.of(context).popUntil(ModalRoute.withName("/shopping_home"));
        // update add deep_link
        int? _appId = AppStorage.instance.getInt2(SKeysPK.appId) ??
            Get.find<ShoppingController>().appId;
        urlPayInsurance = (urlPayInsurance ?? '') + '${_appId?.getDeepLink}';
        //
        await launchUrl(Uri.parse(urlPayInsurance ?? ''),
            mode: LaunchMode.externalApplication);
      }
    } catch (e) {
      dialogAsk(context, 'Lỗi không tìm thấy trang thanh toán!');
    }
  }

  Future<void> _onCreateOrder({required BuildContext context}) async {
    _onUpdateFormRequest();
    isLoadingBtn.toggle();
    BaseResponse<InsuranceCreateOrdResponse> res =
        await DelayLoadingCallApi.delayCallApi(ShoppingRepository.instance
            .postCreateOrder(formRequest: insuranceCreateOrdRequestForm));
    isLoadingBtn.toggle();
    if (res.code == CODE_SUCCESS) {
      urlPayInsurance = res.data?.returnUrl;
      await _openWebPayInsurance(context);
    } else {
      dialogAsk(context, res.message);
    }
  }

  void onClickBtn({required BuildContext context}) async {
    isValidateTenChuNha.value = true;
    isValidateCccd.value = true;
    isValidateSdt.value = true;
    isValidateDiaChiNha.value = true;
    isValidateSoTang.value = true;
    isValidateDienTich.value = true;
    isValidateTenChuHD.value = true;
    isValidateSdtChuHD.value = true;
    isValidateEmail.value = true;
    if (validateTenChuNha() != null ||
        validateCccd() != null ||
        validateSdt() != null ||
        validateDiaChiNha() != null ||
        validateSoTang() != null ||
        validateDienTich() != null ||
        validateTenChuHd() != null ||
        validateSdtLH() != null ||
        validateEmail() != null) {
      return;
    }
    _convertData();
    await _onCreateOrder(context: context);
  }
}

class LandStepCofirmControllerUpdateKey {
  static const String updateLandStatus = '/update_land_status';
  static const String updateCheckboxVay = '/update_check_box_vay';
  static const String updateCheckboxConfirm = '/update_check_box_cofirm';
}
