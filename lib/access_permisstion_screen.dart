import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:shopping/permission_app_screen.dart';

import 'constants/app_assets_paths.dart';
import 'constants/app_colors.dart';
import 'constants/app_text_style.dart';

class AccessPermissionScreen extends StatefulWidget {
  const AccessPermissionScreen({
    super.key,
    this.index,
  });
  final int? index;

  @override
  State<AccessPermissionScreen> createState() => _AccessPermissionScreenState();
}

class _AccessPermissionScreenState extends State<AccessPermissionScreen> {
  String? text;
  @override
  void initState() {
    super.initState();
    if (widget.index == 2) {
      text = 'TÀI KHOẢN';
    } else if (widget.index == 1) {
      text = 'ĐƠN HÀNG';
    } else {
      text = 'BẢO HIỂM';
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(text ?? '', style: AppTextStyle.s18SemiBold),
        centerTitle: true,
        backgroundColor: AppColors.white,
        surfaceTintColor: AppColors.white,
        elevation: 5,
        shadowColor: AppColors.black.withValues(alpha: 0.4),
        leading: IconButton(
          icon: SvgPicture.asset(SvgPath.svgIconBack),
          onPressed: () => Navigator.of(context).pop(false),
        ),
      ),
      body: Container(
        padding: EdgeInsets.only(left: 15, right: 15),
        child: Column(
          children: [
            Padding(
              padding: const EdgeInsets.only(top: 59, left: 118, right: 118),
              child: SvgPicture.asset(
                SvgPath.svgNotAccessOrderTab,
              ),
            ),
            SizedBox(height: 39),
            Text(
              'Vui lòng cấp quyền truy cập \n để xem thông tin ${text?.toLowerCase()}',
              textAlign: TextAlign.center,
              style:
                  AppTextStyle.s14Medium.copyWith(color: AppColors.black2E2E2E),
            ),
            Spacer(),
            Align(
              alignment: FractionalOffset.bottomCenter,
              child: InkWell(
                onTap: () {
                  Navigator.of(context)
                      .push(
                    MaterialPageRoute(
                      builder: (_) => PermissionAppScreen(
                        isPush: true,
                      ),
                    ),
                  )
                      .then((value) {
                    if (value == true) {
                      Navigator.of(context).pop(true);
                    }
                  });
                },
                child: Container(
                  padding: EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: AppColors.green30AAB7,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Center(
                    child: Text(
                      "CẤP QUYỀN TRUY CẬP",
                      style: AppTextStyle.s16Medium
                          .copyWith(color: AppColors.white, height: 17 / 12),
                    ),
                  ),
                ),
              ),
            ),
            SizedBox(
              height: 63,
            )
          ],
        ),
      ),
    );
  }
}
