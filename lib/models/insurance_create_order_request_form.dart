import 'package:shopping/models/insurance_get_price_response.dart';

class InsuranceCreateOrdRequestForm {
  int? productId;
  int? loaiBh;
  String? chuXe;
  String? bienSoXe;
  String? soKhung;
  String? mdSuDung;
  int? soChoNgoi;
  num? trongTai;
  String? cccd;
  String? phoneNumber;
  String? diaChiNha;
  int? soTang;
  num? dienTich;
  String? qSuHuu;
  String? gtriNgoiNha;
  String? chuHd;
  String? phoneNumberHd;
  String? email;
  String? diaChiHd;
  int? namDaSd;
  String? loaiXe;
  int? thoiHan;
  int? tgBatDau;
  String? maKm;
  List<DataDk>? ddDk;
  String? referCode;

  InsuranceCreateOrdRequestForm({
    this.productId,
    this.loaiBh,
    this.chuXe,
    this.bienSoXe,
    this.soKhung,
    this.mdSuDung,
    this.soChoNgoi,
    this.trongTai,
    this.cccd,
    this.phoneNumber,
    this.diaChiNha,
    this.soTang,
    this.dienTich,
    this.qSuHuu,
    this.gtriNgoiNha,
    this.chuHd,
    this.phoneNumberHd,
    this.email,
    this.diaChiHd,
    this.namDaSd,
    this.loaiXe,
    this.thoiHan,
    this.tgBatDau,
    this.maKm,
    this.ddDk,
    this.referCode,
  });

  InsuranceCreateOrdRequestForm copyWith({
    int? productId,
    int? loaiBh,
    String? chuXe,
    String? bienSoXe,
    String? soKhung,
    String? mdSuDung,
    int? soChoNgoi,
    int? trongTai,
    String? cccd,
    String? phoneNumber,
    String? diaChiNha,
    int? soTang,
    num? dienTich,
    String? qSuHuu,
    String? gtriNgoiNha,
    String? chuHd,
    String? phoneNumberHd,
    String? email,
    String? diaChiHd,
    int? namDaSd,
    String? loaiXe,
    int? thoiHan,
    int? tgBatDau,
    String? maKm,
    List<DataDk>? ddDk,
    String? referCode,
  }) =>
      InsuranceCreateOrdRequestForm(
        productId: productId ?? this.productId,
        loaiBh: loaiBh ?? this.loaiBh,
        chuXe: chuXe ?? this.chuXe,
        bienSoXe: bienSoXe ?? this.bienSoXe,
        soKhung: soKhung ?? this.soKhung,
        mdSuDung: mdSuDung ?? this.mdSuDung,
        soChoNgoi: soChoNgoi ?? this.soChoNgoi,
        trongTai: trongTai ?? this.trongTai,
        cccd: cccd ?? this.cccd,
        phoneNumber: phoneNumber ?? this.phoneNumber,
        diaChiNha: diaChiNha ?? this.diaChiNha,
        soTang: soTang ?? this.soTang,
        dienTich: dienTich ?? this.dienTich,
        qSuHuu: qSuHuu ?? this.qSuHuu,
        gtriNgoiNha: gtriNgoiNha ?? this.gtriNgoiNha,
        chuHd: chuHd ?? this.chuHd,
        phoneNumberHd: phoneNumberHd ?? this.phoneNumberHd,
        email: email ?? this.email,
        diaChiHd: diaChiHd ?? this.diaChiHd,
        namDaSd: namDaSd ?? this.namDaSd,
        loaiXe: loaiXe ?? this.loaiXe,
        thoiHan: thoiHan ?? this.thoiHan,
        tgBatDau: tgBatDau ?? this.tgBatDau,
        maKm: maKm ?? this.maKm,
        ddDk: ddDk ?? this.ddDk,
        referCode: referCode ?? this.referCode,
      );

  factory InsuranceCreateOrdRequestForm.fromJson(Map<String, dynamic> json) =>
      InsuranceCreateOrdRequestForm(
        productId: json["product_id"],
        loaiBh: json["loai_bh"],
        chuXe: json["chu_xe"],
        bienSoXe: json["bien_so_xe"],
        soKhung: json["so_khung"],
        mdSuDung: json["md_su_dung"],
        soChoNgoi: json["so_cho_ngoi"],
        trongTai: json["trong_tai"],
        cccd: json["cccd"],
        phoneNumber: json["phone_number"],
        diaChiNha: json["dia_chi_nha"],
        soTang: json["so_tang"],
        dienTich: json["dien_tich"],
        qSuHuu: json["q_su_huu"],
        gtriNgoiNha: json["gtri_ngoi_nha"],
        chuHd: json["chu_hd"],
        phoneNumberHd: json["phone_number_hd"],
        email: json["email"],
        diaChiHd: json["dia_chi_hd"],
        namDaSd: json["nam_da_sd"],
        loaiXe: json["loai_xe"],
        thoiHan: json["thoi_han"],
        tgBatDau: json["tg_bat_dau"],
        maKm: json["ma_km"],
        ddDk: json["dd_dk"] == null
            ? []
            : List<DataDk>.from(json["dd_dk"]!.map((x) => DataDk.fromJson(x))),
        referCode: json["refer_code"],
      );

  Map<String, dynamic> toJson() => {
        "product_id": productId,
        "loai_bh": loaiBh,
        "chu_xe": chuXe,
        "bien_so_xe": bienSoXe,
        "so_khung": soKhung,
        "md_su_dung": mdSuDung,
        "so_cho_ngoi": soChoNgoi,
        "trong_tai": trongTai,
        "cccd": cccd,
        "phone_number": phoneNumber,
        "dia_chi_nha": diaChiNha,
        "so_tang": soTang,
        "dien_tich": dienTich,
        "q_su_huu": qSuHuu,
        "gtri_ngoi_nha": gtriNgoiNha,
        "chu_hd": chuHd,
        "phone_number_hd": phoneNumberHd,
        "email": email,
        "dia_chi_hd": diaChiHd,
        "nam_da_sd": namDaSd,
        "loai_xe": loaiXe,
        "thoi_han": thoiHan,
        "tg_bat_dau": tgBatDau,
        "ma_km": maKm,
        "dd_dk": ddDk == null
            ? []
            : List<dynamic>.from(ddDk!.map((x) => x.toJson())),
        "refer_code": referCode,
      };
}
