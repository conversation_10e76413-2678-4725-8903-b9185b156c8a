import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:shopping/apis/ShoppingRepository.dart';
import 'package:shopping/app_services/api_service.dart';
import 'package:shopping/constants/enum_insurance_type.dart';
import 'package:shopping/enum/enum_ins_car_type.dart';
import 'package:shopping/loading_screen.dart';
import 'package:shopping/models/base_response.dart';
import 'package:shopping/models/insurance_create_order_request_form.dart';
import 'package:shopping/models/insurance_filter_response.dart';
import 'package:shopping/models/insurance_get_price_form_request.dart';
import 'package:shopping/models/insurance_get_price_response.dart';
import 'package:shopping/models/product.dart';
import 'package:shopping/pages/insurance/pages/car/car_step_confirm_screen.dart';
import 'package:shopping/widgets/app_dialog.dart';

class InsuranceCarStepProductController extends GetxController {
  InsuranceCarStepProductController(
      {required this.productIns, required this.context});

  // input
  final Product? productIns;
  final BuildContext context;

  // loading
  RxBool isLoading = false.obs;
  String? errMessage;

  // loai xe
  List<LoaiXe>? listLoaiXe;
  LoaiXe? loaiXeChoosed;

  // mục đích sử dụng
  CarPurposeUsedType carPurposeUsedTypeChoosed =
      CarPurposeUsedType.non_business;

  // số chỗ ngồi
  int? seat;
  String? seatTxt;
  RxBool isValidateSeat = false.obs;
  // trọng tải
  int? payload;
  String? payloadTxt;
  RxBool isValidatePayload = false.obs;

  // thời hạn bảo hiểm
  CarTimeType carTimeTypeChoosed = CarTimeType.one;

  // ngày hiệu lực/ ngày hết hạn
  DateTime timeStart = DateTime.now();
  String startTimeTxt = '';
  String endTimeTxt = '';

  // gói bảo hiểm mua thêm
  List<SpMuaThem>? listSpMuaThem;
  List<SpMuaThem> listSpMuaThemChoosed = [];
  List<SpMuaThem> listSpMuaThemBatBuoc = [];

  // mã khuyến mại
  List<MaGiamGia>? listMaKM;
  MaGiamGia? maKMChoosed;
  String? errMaKm;

  // tiền
  InsuranceGetPriceFormResquest requestGetPriceForm =
      InsuranceGetPriceFormResquest();
  InsuranceGetPriceResponse? insuranceGetPriceResponse;
  RxBool isLoadingMoney = false.obs;
  num total = 0;
  num disscount = 0;
  num totalPay = 0;

  // loading btn
  RxBool isLoadingBtn = false.obs;

  @override
  void onInit() async {
    super.onInit();
    _clearData();
    await _initData();
    updateTimeStart(timeStart, isInit: true);
  }

  void _clearData() {
    listLoaiXe?.clear();
    listMaKM?.clear();
    listSpMuaThem?.clear();
  }

  Future<void> _initData() async {
    if (isLoading.value) {
      return;
    }
    isLoading.value = true;
    BaseResponse<InsuranceFilterResponse> res =
        await DelayLoadingCallApi.delayCallApi(ShoppingRepository.instance
            .getInsFilter(insId: productIns?.id ?? 0));
    if (res.code == CODE_SUCCESS) {
      listLoaiXe = res.data?.data?.loaiXe ?? [];
      listMaKM = res.data?.data?.maGiamGia ?? [];
      listSpMuaThem = res.data?.data?.spMuaThem ?? [];
      errMessage = null;
    } else {
      errMessage = res.message;
      dialogAsk(context, res.message);
    }
    _setup();
    update([
      InsuranceCarStepProductControllerUpdateKey.updateCarType,
      InsuranceCarStepProductControllerUpdateKey.updateCouponChoosed,
      InsuranceCarStepProductControllerUpdateKey.updateBonusChoosed,
    ]);
    isLoading.value = false;
  }

  void _setup() {
    if ((listLoaiXe ?? []).isEmpty) {
      listLoaiXe?.add(LoaiXe(name: 'Không có dữ liệu'));
    }
    if ((listMaKM ?? []).isEmpty) {
      listMaKM?.add(MaGiamGia(name: 'Không có dữ liệu'));
    }
    loaiXeChoosed = listLoaiXe?.firstOrNull;
    maKMChoosed = listMaKM?.firstOrNull;
    // setup auto check cho gói bảo hiểm bắt buộc
    if ((listSpMuaThem ?? []).isNotEmpty) {
      if (productIns?.getInsType == InsuranceType.moto ||
          productIns?.getInsType == InsuranceType.car) {
        for (SpMuaThem i in listSpMuaThem ?? []) {
          if (i.code == 'BB') {
            listSpMuaThemBatBuoc.add(i);
          }
        }
      } else if (productIns?.getInsType == InsuranceType.land) {
        for (SpMuaThem i in listSpMuaThem ?? []) {
          if (i.code == 'COBAN') {
            listSpMuaThemBatBuoc.add(i);
          }
        }
      }
    }
    listSpMuaThemChoosed.addAll(listSpMuaThemBatBuoc);
  }

  void updateCarType(LoaiXe newValue) async {
    loaiXeChoosed = newValue;
    update([InsuranceCarStepProductControllerUpdateKey.updateCarType]);
    await _calculateMoney();
  }

  void updateCarPurposedUsed(CarPurposeUsedType newValue) async {
    carPurposeUsedTypeChoosed = newValue;
    update([InsuranceCarStepProductControllerUpdateKey.updateCarPurposedUsed]);
    await _calculateMoney();
  }

  void updateCarTimeType(CarTimeType newValue) {
    carTimeTypeChoosed = newValue;
    update([InsuranceCarStepProductControllerUpdateKey.updateCarTimeType]);
    _updateTimeEnd();
  }

  void updateTimeStart(DateTime? newValue, {bool? isInit}) {
    if (newValue == null) {
      return;
    }
    timeStart = newValue;
    startTimeTxt = DateFormat("dd/MM/yyyy").format(timeStart);
    _updateTimeEnd(isInit: isInit);
  }

  Future<void> _updateTimeEnd({bool? isInit}) async {
    endTimeTxt = DateFormat("dd/MM/yyyy").format(timeStart
        .add(Duration(days: (365 * carTimeTypeChoosed.getDurationTime))));
    if (isInit != true) {
      await _calculateMoney();
    }
    update([InsuranceCarStepProductControllerUpdateKey.updateTime]);
  }

  Future<void> _calculateMoney() async {
    // check data (đủ thông tin request chưa)
    isValidateSeat.value = true;
    isValidatePayload.value = true;
    if (validateSoChoNgoi() != null || validateTrongTai() != null) {
      return;
    }
    // convert dữ liệu số chỗ ngồi và trọng tải
    convertData();

    // request API get price
    requestGetPriceForm = requestGetPriceForm.copyWith(
      loaiBh: productIns?.group,
      loaiXe: loaiXeChoosed?.id,
      thoiHan: carTimeTypeChoosed.getDurationTime,
      tgBatDau: timeStart.millisecondsSinceEpoch ~/ 1000,
      maKm: maKMChoosed?.id,
      spMuaKem: listSpMuaThemChoosed.map((e) => e.code ?? '').toList(),
      mdSuDung: carPurposeUsedTypeChoosed.getValue,
      soChoNgoi: seat,
      trongTai: payload,
    );
    isLoadingMoney.value = true;
    BaseResponse<InsuranceGetPriceResponse> res =
        await DelayLoadingCallApi.delayCallApi(ShoppingRepository.instance
            .postGetPrice(formRequest: requestGetPriceForm));
    if (res.code == CODE_SUCCESS) {
      insuranceGetPriceResponse = res.data;
      total = insuranceGetPriceResponse?.data?.tongTien ?? 0;
      disscount = insuranceGetPriceResponse?.data?.giam ?? 0;
      totalPay = insuranceGetPriceResponse?.data?.thanhToan ?? 0;
      errMessage = null;
      _validateMaKM();
    } else {
      errMessage = res.message;
      dialogAsk(context, res.message);
    }
    update([InsuranceCarStepProductControllerUpdateKey.updateMoney]);
    isLoadingMoney.value = false;
  }

  void _validateMaKM() {
    // update tbao lỗi mã gg
    if (disscount == 0 && maKMChoosed != null) {
      errMaKm = 'Sản phẩm không áp dụng mã giảm giá này';
    } else {
      errMaKm = null;
    }
    update([InsuranceCarStepProductControllerUpdateKey.updateCouponChoosed]);
  }

  void onChangeSeat(String strSeat) async {
    seatTxt = strSeat;
    await _calculateMoney();
  }

  String? validateSoChoNgoi() {
    if (isValidateSeat.value == true) {
      if (seatTxt == null || (seatTxt ?? '').isEmpty) {
        return 'Vui lòng nhập số chỗ ngồi';
      }
      seat = int.parse(seatTxt ?? '0');
      // xe chờ người, pickup => nhỏ hơn 100
      // xe chở hàng => nhỏ hơn 6
      if (loaiXeChoosed?.isXeChoHang == true) {
        if ((seat ?? 0) >= 6) return 'Số chỗ ngồi không hợp lệ';
      } else {
        if ((seat ?? 0) >= 100) return 'Số chỗ ngồi không hợp lệ';
      }
    }
    return null;
  }

  void onChangePayload(String strPayload) async {
    payloadTxt = strPayload;
    await _calculateMoney();
  }

  String? validateTrongTai() {
    if (isValidatePayload.value == true && loaiXeChoosed?.isXeChoHang == true) {
      if (payloadTxt == null || (payloadTxt ?? '').isEmpty) {
        return 'Vui lòng nhập trọng tải';
      }
      payload = int.parse(payloadTxt ?? '0');
      // số nguyên < 100
      if ((payload ?? 0) >= 100) return 'Trọng tải xe không hợp lệ';
    }
    return null;
  }

  void updateCouponChoosed(MaGiamGia newValue) async {
    maKMChoosed = newValue;
    update([InsuranceCarStepProductControllerUpdateKey.updateCouponChoosed]);
    await _calculateMoney();
  }

  void onSelectInsBonus(SpMuaThem newValue) async {
    // với gói bắt buộc ko được bỏ chọn
    if (listSpMuaThemBatBuoc.contains(newValue)) {
      return;
    }
    // với các gói còn lại
    if (listSpMuaThemChoosed.contains(newValue)) {
      listSpMuaThemChoosed.remove(newValue);
    } else {
      listSpMuaThemChoosed.add(newValue);
    }
    update([InsuranceCarStepProductControllerUpdateKey.updateBonusChoosed]);
    await _calculateMoney();
  }

  Future<void> onClickBtn(BuildContext context) async {
    /// case đang loading
    if (isLoadingBtn.value == true ||
        isLoadingMoney.value == true ||
        errMessage != null) {
      return;
    }

    /// validate các trường
    isValidateSeat.value = true;
    isValidatePayload.value = true;
    if (validateSoChoNgoi() != null || validateTrongTai() != null) {
      // case thông tin ko hợp lệ
      return;
    }

    /// tính tiền lại
    await _calculateMoney();

    /// loading  btn
    isLoadingBtn.value = true;

    /// chuyển màn thanh toán
    if (errMessage == null) {
      // convert
      InsuranceCreateOrdRequestForm _insCreateOrdForm =
          InsuranceCreateOrdRequestForm(
        productId: productIns?.id,
        loaiBh: requestGetPriceForm.loaiBh,
        loaiXe: requestGetPriceForm.loaiXe,
        thoiHan: requestGetPriceForm.thoiHan,
        tgBatDau: requestGetPriceForm.tgBatDau,
        mdSuDung: requestGetPriceForm.mdSuDung,
        soChoNgoi: requestGetPriceForm.soChoNgoi,
        trongTai: requestGetPriceForm.trongTai,
        namDaSd: requestGetPriceForm.namDaSd,
        gtriNgoiNha: requestGetPriceForm.gtriNgoiNha,
        maKm: requestGetPriceForm.maKm,
        ddDk: insuranceGetPriceResponse?.data?.dataDk,
      );
      Navigator.push(
          context,
          MaterialPageRoute(
              builder: (_) => CarStepConfirmScreen(
                    insuranceCreateOrdRequestForm: _insCreateOrdForm,
                  )));
    }
    isLoadingBtn.value = false;
  }

  void convertData() {
    seat = int.parse(seatTxt ?? '0');
    payload = int.parse(payloadTxt ?? '0');
  }
}

class InsuranceCarStepProductControllerUpdateKey {
  static const String updateCarType = '/update_car_type';
  static const String updateCarPurposedUsed = '/update_car_purpose_used';
  static const String updateCarTimeType = '/update_car_time_type';
  static const String updateCouponChoosed = '/update_coupon_choosed';
  static const String updateBonusChoosed = '/update_bonus_choosed';
  static const String updateMoney = '/update_money';
  static const String updateTime = '/update_time';
}
