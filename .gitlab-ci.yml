stages:
  - build
  - package
  # - deploy


build-image:
  stage: build
  tags:
    - flutter-web # Tags for runner
  only:
    - tags
  except:
    - master
  before_script:
    - export T_CI_TAG=$CI_COMMIT_TAG
    - export T_CI_DEV="$DEV_API_URL"
    - export T_CI_STAG="$STAG_API_URL"
    - export T_CI_PRO="$PRO_API_URL"
    - export PATH="$PATH:/Users/<USER>/development/flutter_3_32_7/bin"
    - sh .run.sh
  script:
    - flutter --version
    - cd example
    - flutter clean
    - flutter build web --release --wasm --build-number $CI_PIPELINE_IID --build-name $CI_APP_VERSION --base-href '/' --source-maps
    - cd ../
  artifacts:
    paths:
      - example/build/web


docker-build:
  stage: package
  tags:
    - web-flutter # Tags for runner
  only:
    - tags
  except:
    - master
  variables:
    TAG_COMMIT: registry.vivas.vn/mini-app-web/muadi:$CI_COMMIT_REF_SLUG
  script:
    - echo "package the code..."
    - echo "T4Us2VfyYvZ742oOJkWjNQ4JxMT02Ht3" | docker login registry.vivas.vn --username 'robot$mini-app-web+build' --password-stdin 
    - echo "ENV RUN_ENV=$CI_COMMIT_TAG" >> ./Dockerfile
    - docker build -t $TAG_COMMIT .
    - docker push $TAG_COMMIT

# deploy:
#   stage: deploy
#   image: curlimages/curl
#   when: manual
#   variables:
#     TAG_COMMIT: registry.vivas.vn/mini-app-web/muadi:$CI_COMMIT_SHA
#   tags:
#     - web-flutter # Tags for runner
#   only:
#     - tags
#   except:
#     - master
#   script:
#     - export T_CI_TAG=$CI_COMMIT_TAG
#     - export T_TAG_COMMIT=CURRENT_IMAGE=$TAG_COMMIT
#     - sh .deploy.sh
