library shopping;

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:loading_animation_widget/loading_animation_widget.dart';

import 'package:shopping/apis/ShoppingRepository.dart';
import 'package:shopping/app_services/api_service.dart';
import 'package:shopping/app_services/app_storage.dart';
import 'package:shopping/constants/enum_package_type.dart';
import 'package:shopping/models/home_model.dart';
import 'package:shopping/pages/package/package_screen.dart';
import 'package:shopping/shopping_controller.dart';

import 'app_services/log.dart';

import 'shopping_screen.dart';

export 'tabs/home/<USER>';

enum RunMode { dev, stag, pro }

class Shopping {
  late String ApiKey;
  late String VdcpToken;
  late int profileId;
  late int appId;
  RunMode runMode = RunMode.dev;
  OverlayEntry? _loadingOverlayEntry;
  Shopping(RunMode _RunMode, String _ApiKey, String _VdcpToken, int _profileId,
      int _appId) {
    this.runMode = _RunMode;
    this.VdcpToken = _VdcpToken;
    this.ApiKey = _ApiKey;
    this.profileId = _profileId;
    this.appId = _appId;
    AppStorage.instance.init(appId: _appId);
    ApiService.instance.init(this.runMode, this.ApiKey, this.profileId);
    ShoppingRepository.instance.init(this.VdcpToken);
    if (Get.isRegistered<ShoppingController>() == true) {
      Get.put<ShoppingController>(ShoppingController(0, appId: _appId)).onReload(_VdcpToken);
      Get.put<ShoppingController>(ShoppingController(0, appId: _appId)).currentIndex.value = 0;
    }
    UDPLog.instance.init_udp();
    // initServices(_RunMode, _ApiKey, _VdcpToken);
  }

  // initServices(RunMode runMode, String _ApiKey, String _VdcpToken) {
  //   print('starting services ...');

  //   UDPLog.instance.init_udp();
  //   // if (Get.isRegistered<ShoppingController>() == true) {
  //   //   Get.put<ShoppingController>(ShoppingController()).onReload(_VdcpToken);
  //   //   Get.put<ShoppingController>(ShoppingController()).currentIndex.value = 0;
  //   // } else {
  //   //   Get.put(() => ShoppingController().init(_VdcpToken));
  //   // }
  //   print('All services started...');
  // }

  Widget page(bool isShowNavigatorBar, int product_id) {
    return ShoppingScreen(isShowNavigatorBar, product_id);
  }

  showShoppingScreen(
    context,
    bool isShowNavigatorBar,
    int product_id,
  ) {
    Navigator.push(
      context,
      MaterialPageRoute(
          settings: RouteSettings(name: "/shopping_home"),
          builder: (context) => this.page(isShowNavigatorBar, product_id)),
    ).then((value) {
      Get.find<ShoppingController>().setAcceptPermission(false);
    });
  }

  showDetailCate(context, int cate_id, String _name, String ishot) {
    // ishot = 1
    //PackageType? packageTypes = PackageType.values.firstWhereOrNull((e) => e.getValue == packageTye);
    Navigator.push(
      context,
      MaterialPageRoute(
          builder: (context) => PackageScreen(
                homeData: HomeData(id: cate_id, name: _name),
                packageType:
                    ishot == "1" ? PackageType.hotDeal : PackageType.unknown,
              )),
    );
  }

  showDetailProduct(context, int productId) async {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => this.page(false, productId)),
    ).then((value) {
      Get.find<ShoppingController>().setAcceptPermission(false);
    });
    // ShoppingRepository.instance.getProduct2(productId).then((res) {
    // if (res.code == 0) {
    //   var product = res.data ?? null;
    //   if (product != null) {
    //     //ItemPackageAction.openDetail(context, product);
    //   } else {
    //     print("Get product null");
    //   }
    // } else {
    //   print("Get product error");
    // }
    // }).onError((error, stackTrace) {});
  }

  showLoading(BuildContext context) {
    // ignore: prefer_conditional_assignment
    if (_loadingOverlayEntry == null) {
      _loadingOverlayEntry = OverlayEntry(
        builder: (context) {
          return Container(
            width: MediaQuery.of(context).size.width,
            height: MediaQuery.of(context).size.height,
            color: Colors.black.withAlpha(50),
            child: Center(
              child: LoadingAnimationWidget.staggeredDotsWave(
                color: const Color(0xFF3D4FF4),
                size: 40,
              ),
            ),
          );
        },
      );
    }
    Overlay.of(context).insert(_loadingOverlayEntry!);
  }

  closeLoading(BuildContext context) {
    if (_loadingOverlayEntry != null) {
      _loadingOverlayEntry!.remove();
    }
  }
}
