import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:shopping/constants/app_assets_paths.dart';
import 'package:shopping/constants/app_colors.dart';
import 'package:shopping/constants/app_text_style.dart';
import 'package:shopping/loading_screen.dart';
import 'package:shopping/tabs/order/order_controller.dart';
import 'package:shopping/utils/extension/int_extension.dart';
import 'package:shopping/widgets/app_dialog.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../enum/enum_order_type.dart';

class OrderDetailScreen extends StatefulWidget {
  const OrderDetailScreen({
    super.key,
    this.orderId,
  });
  final int? orderId;

  @override
  State<OrderDetailScreen> createState() => _OrderDetailScreenState();
}

class _OrderDetailScreenState extends State<OrderDetailScreen> {
  Future<void> call() async {
    await Get.find<OrderController>().getOrderDetail(widget.orderId);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.white,
      appBar: AppBar(
        backgroundColor: AppColors.white,
        centerTitle: true,
        title: Text(
          "THÔNG TIN ĐƠN HÀNG",
          style: AppTextStyle.s16Bold.copyWith(color: AppColors.black1E1E1E),
        ),
        elevation: 5,
        shadowColor: AppColors.black.withOpacity(0.4),
        surfaceTintColor: Colors.white,
        leading: IconButton(
          icon: SvgPicture.asset(SvgPath.svgIconBack),
          onPressed: () => Navigator.of(context).pop(),
        ),
      ),
      body: FutureBuilder(
          future: call(),
          builder: (context, snpshot) {
            final order = Get.find<OrderController>().orderDetail.value;
            print(order?.id);
            return Obx(() => Get.find<OrderController>().isLoading.value
                ? Center(child: LoadingWidget())
                : Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Card(
                          elevation: 0,
                          color: Colors.white,
                          margin: EdgeInsets.only(left: 15, right: 15),
                          child: Padding(
                            padding: const EdgeInsets.only(top: 39),
                            child: Column(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                infoOrderDetailItem(
                                    'Mã đơn hàng', '${order?.code}'),
                                SizedBox(
                                  height: 10,
                                ),
                                infoOrderDetailItem('Sản phẩm',
                                    '${order?.getItemProductInfor}'),
                                SizedBox(
                                  height: 10,
                                ),
                                order?.isInsABICOrder == true
                                    ? Column(
                                        mainAxisSize: MainAxisSize.min,
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          infoOrderDetailItem('Nhà bảo hiểm',
                                              order?.supplier ?? 'N/A'),
                                          SizedBox(height: 10),
                                          infoOrderDetailItem(
                                              'Thời hạn hiệu lực',
                                              order?.thoiGian ?? 'N/A'),
                                          SizedBox(height: 10),
                                          OrderType.fromStatus(
                                                      order?.status ?? 0) ==
                                                  OrderType.success
                                              ? Padding(
                                                  padding:
                                                      const EdgeInsets.only(
                                                          bottom: 10),
                                                  child: infoOrderDetailItem(
                                                      'Số GCNBH',
                                                      order?.gcnBh ?? 'N/A'),
                                                )
                                              : const SizedBox(),
                                          OrderType.fromStatus(
                                                      order?.status ?? 0) ==
                                                  OrderType.success
                                              ? Padding(
                                                  padding:
                                                      const EdgeInsets.only(
                                                          bottom: 10),
                                                  child: infoOrderDetailItem(
                                                      'Link tra cứu',
                                                      order?.linkTc ?? 'N/A',
                                                      isLink: true),
                                                )
                                              : const SizedBox(),
                                        ],
                                      )
                                    : Padding(
                                        padding:
                                            const EdgeInsets.only(bottom: 10),
                                        child: infoOrderDetailItem('Số lượng',
                                            '${order?.productItems?[0].quantity}'),
                                      ),
                                infoOrderDetailItem('Tổng giá trị đơn hàng',
                                    '${order?.totalPrice.convertToVietnamesMoney()}đ',
                                    isMoney: true),
                                SizedBox(
                                  height: 10,
                                ),
                                order?.isInsABICOrder == true
                                    ? const SizedBox()
                                    : Padding(
                                        padding:
                                            const EdgeInsets.only(bottom: 10),
                                        child: infoOrderDetailItem(
                                            'Thời gian thực hiện',
                                            '${order?.timeAt}'),
                                      ),
                                stateOrderDetailItem(order?.status ?? 0),
                              ],
                            ),
                          ))
                    ],
                  ));
          }),
    );
  }

  Widget infoOrderDetailItem(String label, String value,
      {bool? isLink, bool? isMoney}) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: AppTextStyle.s13Regular
              .copyWith(color: AppColors.black, height: 17 / 12),
        ),
        SizedBox(
          width: 10,
        ),
        Expanded(
            child: Align(
          alignment: FractionalOffset.bottomRight,
          child: InkWell(
            splashColor: Colors.transparent,
            onTap: () async {
              if (isLink != true || value == 'N/A') {
                return;
              }
              try {
                await launchUrl(Uri.parse(value),
                    mode: LaunchMode.externalApplication);
              } catch (e) {
                dialogAsk(context, 'Lỗi không tìm thấy trang thanh toán!');
              }
            },
            child: Text(
              value,
              style: AppTextStyle.s13SemiBold.copyWith(
                color: isLink == true ? AppColors.blue005BF9 : AppColors.black,
                fontWeight: isMoney == true ? FontWeight.w700 : FontWeight.w600,
                height: 19 / 12,
                fontStyle: isLink == true ? FontStyle.italic : null,
                decoration: isLink == true ? TextDecoration.underline : null,
                decorationColor: isLink == true ? AppColors.blue005BF9 : null,
              ),
              textAlign: TextAlign.right,
            ),
          ),
        ))
      ],
    );
  }

  Widget stateOrderDetailItem(int status) {
    return Row(
      children: [
        Text(
          "Trạng thái",
          style: AppTextStyle.s13Regular
              .copyWith(color: AppColors.black, height: 17 / 12),
        ),
        Expanded(
          child: Align(
            alignment: FractionalOffset.bottomRight,
            child: Container(
              width: 88,
              height: 18,
              decoration: BoxDecoration(
                  color: OrderType.fromStatus(status).statusColor,
                  borderRadius: BorderRadius.circular(5)),
              child: Center(
                child: Text(OrderType.fromStatus(status).statusName,
                    style: AppTextStyle.s10Regular.copyWith(
                        color: AppColors.whiteFFFFFF, height: 17 / 12)),
              ),
            ),
          ),
        ),
      ],
    );
  }
}
