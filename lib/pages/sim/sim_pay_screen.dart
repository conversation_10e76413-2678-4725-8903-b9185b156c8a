import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:shopping/apis/ShoppingRepository.dart';
import 'package:shopping/constants/app_assets_paths.dart';
import 'package:shopping/constants/app_colors.dart';
import 'package:shopping/constants/app_text_style.dart';
import 'package:shopping/constants/enum_step_ord_sim.dart';
import 'package:shopping/loading_screen.dart';
import 'package:shopping/models/sim_model.dart';
import 'package:shopping/pages/sim/item_sim_action.dart';
import 'package:shopping/pages/sim/policy_detail_screen.dart';
import 'package:shopping/pages/sim/sim_pay_controller.dart';
import 'package:shopping/pages/sim/step_pick_product_controller.dart';
import 'package:shopping/pages/sim/widgets/count_time_order_sim_widget.dart';
import 'package:shopping/pages/sim/widgets/method_pay_widget.dart';
import 'package:shopping/pages/sim/widgets/sim_text_field_widget.dart';
import 'package:shopping/pages/sim/widgets/stepper_widget.dart';
import 'package:shopping/pages/sim/widgets/ticket_widget.dart';
import 'package:shopping/utils/extension/int_extension.dart';
import 'package:shopping/widgets/app_dialog.dart';

class SIMPayScreen extends StatelessWidget {
  SIMPayScreen({super.key, required this.customer});
  final SIMOrder customer;

  @override
  Widget build(BuildContext context) {
    return GetBuilder<SimPayController>(
      id: SIMPayControllerUpdateKey.updateData,
      init: SimPayController(customer, context),
      builder: (controller) => LoadingOverlay(
        child: Scaffold(
          backgroundColor: AppColors.white,
          appBar: AppBar(
            centerTitle: true,
            backgroundColor: AppColors.white,
            scrolledUnderElevation: 0,
            title: const Text(
              "SIM SỐ",
            ),
            surfaceTintColor: AppColors.white,
            elevation: 5,
            shadowColor: AppColors.black.withValues(alpha: 0.4),
            titleTextStyle: AppTextStyle.s16Bold.copyWith(color: Colors.black),
            leading: IconButton(
                icon: SvgPicture.asset(
                  SvgPath.svgIconBack,
                ),
                onPressed: () {
                  ShoppingRepository.instance
                      .postUnBookSim(ordId: customer.orderId);
                  ActionBack.onBack(context: context);
                  // Navigator.of(context).popUntil(ModalRoute.withName("/page_list_sim"));
                }),
          ),
          body: SingleChildScrollView(
            physics: ClampingScrollPhysics(),
            padding: EdgeInsets.symmetric(horizontal: 15, vertical: 15),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      SizedBox(
                        height: 53,
                        child: LineStepStatusWidget(
                          currentStepIndex: controller.listStepOrdName
                              .indexOf(StepOrdSimStatus.pay),
                          lisStepStatus: controller.listStepOrdName
                              .map((e) => e.getName)
                              .toList(),
                        ),
                      ),
                      SizedBox(height: 17),
                      Obx(() => CountTimeOrderSimWidget(
                          time: controller
                              .stepPickProductController.countTime.value)),
                      const SizedBox(height: 18),
                      TicketPayInforWidget(simOrder: controller.simOrder),
                      const SizedBox(height: 21),
                      Text("Hình thức thanh toán",
                          style: AppTextStyle.s18SemiBold),
                      const SizedBox(height: 10),
                      Flexible(
                        child: GetBuilder<SimPayController>(
                            id: SIMPayControllerUpdateKey.updatePaymentType,
                            builder: (controller) =>
                                MethodPayWidget(controller: controller)),
                      ),
                      const SizedBox(height: 10),
                      SIMTextFieldWidget(
                        title: 'Mã giảm giá',
                        text: controller.couponTxt,
                        onChanged: (p0) {
                          controller.updateCoupon(p0);
                        },
                        onSubmitted: (p0) {
                          controller.updateCoupon(p0);
                        },
                      ),
                      const SizedBox(height: 10),
                      SIMTextFieldWidget(
                        title: 'Mã giới thiệu',
                        text: controller.inviteCodeTxt,
                        onChanged: (p0) {
                          controller.updateInviteCode(p0);
                        },
                        onSubmitted: (p0) {
                          controller.updateInviteCode(p0);
                        },
                      ),
                      const SizedBox(height: 19),
                      _NoteWidget(phiGiaoHang: controller.simOrder.phiGiaoHang),
                      const SizedBox(height: 17),
                      Obx(() => policyWidget(
                            context,
                            controller,
                          )),
                      SizedBox(height: 30),
                      GetBuilder<SimPayController>(
                          id: SIMPayControllerUpdateKey.updateAgreePolicy,
                          builder: (controller) => BlockWidget(
                                simOrder: controller.simOrder,
                                simPayController: controller,
                              ))
                    ])
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget policyWidget(
    BuildContext context,
    SimPayController controller,
  ) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        GestureDetector(
          onTap: () {
            controller.onChangedRemeberMe(!controller.isRememberMe.value);
          },
          child: SizedBox(
            width: 20,
            height: 32,
            child: Checkbox(
              side: const BorderSide(
                width: 1,
                color: AppColors.grey6A6A6A,
              ),
              shape: const RoundedRectangleBorder(
                borderRadius: BorderRadius.all(
                  Radius.circular(5.0),
                ),
              ),
              materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
              activeColor: AppColors.redCE3722,
              value: controller.isRememberMe.value,
              onChanged: (value) => controller.onChangedRemeberMe(value),
            ),
          ),
        ),
        SizedBox(
          width: 9,
        ),
        GestureDetector(
          onTap: () {
            Navigator.push(
              context,
              MaterialPageRoute(
                  builder: (context) => PolicyDetailScreen(
                        webLink: "https://muadimuadi.vn/guide",
                      )),
            );
          },
          child: Padding(
            padding: const EdgeInsets.only(top: 6.0),
            child: RichText(
              text: TextSpan(
                children: [
                  TextSpan(
                      text: 'Tôi đồng ý với ',
                      style: AppTextStyle.s14Medium
                          .copyWith(color: AppColors.black)
                      // style: AppTextStyle.s14(
                      //     fontSize: 13,
                      //     fontWeight: FontWeight.w400,
                      //     color: AppColors.black)
                      ),
                  TextSpan(
                      text: 'Điều khoản sử dụng',
                      style: AppTextStyle.s14Medium.copyWith(
                          color: AppColors.blue1877F2,
                          decoration: TextDecoration.underline)),
                  TextSpan(
                      text: ' và ',
                      style: AppTextStyle.s14Medium
                          .copyWith(color: AppColors.black)),
                  TextSpan(
                      text: 'Chính \nsách riêng tư',
                      style: AppTextStyle.s14Medium.copyWith(
                          color: AppColors.blue1877F2,
                          decoration: TextDecoration.underline)),
                  TextSpan(
                      text: ' của Muadi',
                      style: AppTextStyle.s14Medium
                          .copyWith(color: AppColors.black)),
                ],
              ),
            ),
          ),
        ),
        SizedBox(height: 36),
      ],
    );
  }

  Widget infoCustomer() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [],
    );
  }
}

class _NoteWidget extends StatelessWidget {
  const _NoteWidget({required this.phiGiaoHang});
  final num? phiGiaoHang;
  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        (phiGiaoHang ?? 0) > 0
            ? Container(
                //margin: EdgeInsets.only(bottom: 10),
                padding: EdgeInsets.all(8),
                decoration: BoxDecoration(
                    color: AppColors.blueE4F4FF,
                    borderRadius: BorderRadius.circular(10),
                    border: Border.all(width: 1, color: AppColors.blueA2D9FF)),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    SvgPicture.asset(SvgPath.svgIconNote),
                    const SizedBox(width: 7),
                    Flexible(
                      child: RichText(
                          text: TextSpan(children: [
                        TextSpan(
                            text: 'Phí giao hàng là ',
                            style: AppTextStyle.s12Medium
                                .copyWith(color: AppColors.black)),
                        TextSpan(
                            text:
                                ConvertMoney.convertVNDMoney(phiGiaoHang ?? 0),
                            style: AppTextStyle.s12Medium
                                .copyWith(color: AppColors.redCE3723)),
                        WidgetSpan(
                            alignment: PlaceholderAlignment.top,
                            child: Text(' đ',
                                style: AppTextStyle.s8.copyWith(
                                    color: AppColors.redCE3723,
                                    fontWeight: FontWeight.w500))),
                        TextSpan(
                            text: '. Vui lòng thanh toán khi nhận SIM.',
                            style: AppTextStyle.s12Medium
                                .copyWith(color: AppColors.black)),
                      ])),
                    ),
                  ],
                ),
              )
            : const SizedBox(),
            //bỏ lưu ý
        // Container(
        //   padding: EdgeInsets.all(8),
        //   decoration: BoxDecoration(
        //       color: AppColors.blueE4F4FF,
        //       borderRadius: BorderRadius.circular(10),
        //       border: Border.all(width: 1, color: AppColors.blueA2D9FF)),
        //   child: Row(
        //     mainAxisSize: MainAxisSize.min,
        //     crossAxisAlignment: CrossAxisAlignment.start,
        //     children: [
        //       SvgPicture.asset(SvgPath.svgIconNote),
        //       const SizedBox(width: 7),
        //       Expanded(
        //         child: RichText(
        //             text: TextSpan(children: <TextSpan>[
        //           TextSpan(
        //               text:
        //                   'Lưu ý: Sau khi thanh toán online thành công, đơn hàng của bạn sẽ được xử lý ngay lập tức và ',
        //               style: AppTextStyle.s12Medium
        //                   .copyWith(color: AppColors.black)),
        //           TextSpan(
        //               text: 'không thể hoàn tiền',
        //               style: AppTextStyle.s12Medium
        //                   .copyWith(color: AppColors.redCE3723)),
        //         ])),
        //       ),
        //     ],
        //   ),
        // )
      ],
    );
  }
}

class TotalMoneyWidget extends StatelessWidget {
  const TotalMoneyWidget({
    super.key,
    required this.label,
    required this.value,
  });

  final String label;
  final String value;

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Text(
          label,
          style: AppTextStyle.s14Medium
              .copyWith(color: AppColors.black, height: 17 / 12),
        ),
        SizedBox(
          width: 10,
        ),
        Expanded(
            child: Align(
          alignment: FractionalOffset.bottomRight,
          child: Row(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              Flexible(
                  child: Text(
                value,
                style:
                    AppTextStyle.s18SemiBold.copyWith(color: AppColors.black),
                overflow: TextOverflow.clip,
                maxLines: 1,
                softWrap: false,
              )),
              Text(
                ' đ',
                style: AppTextStyle.s12SemiBold.copyWith(
                  color: AppColors.black,
                ),
                overflow: TextOverflow.ellipsis,
              )
            ],
          ),
        ))
      ],
    );
  }
}

class IntroduceCodeWidget extends StatelessWidget {
  const IntroduceCodeWidget({
    super.key,
    required this.label,
    required this.value,
  });

  final String label;
  final String value;

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Text(
          label,
          style: AppTextStyle.s14Medium
              .copyWith(color: AppColors.black, height: 17 / 12),
        ),
        SizedBox(width: 10),
        Expanded(
          child: Align(
              alignment: FractionalOffset.bottomRight,
              child: SizedBox(
                width: 172,
                child: TextField(
                  //focusNode: controller.focusNode,
                  style: AppTextStyle.s14Regular
                      .copyWith(color: AppColors.black2E2E2E),
                  maxLength: 200,
                  //  controller: controller.searchController,
                  cursorColor: AppColors.black2E2E2E,
                  keyboardType: TextInputType.text,
                  autofocus: false,
                  onEditingComplete: () {
                    // controller.onTapSearch();
                  },
                  decoration: InputDecoration(
                    fillColor: AppColors.white,
                    counterText: '',
                    hintText: 'Nhập mã giảm giá',
                    hintStyle: AppTextStyle.s14Regular.copyWith(
                        color: AppColors.black, fontStyle: FontStyle.italic),
                    filled: true,
                  ),
                ),
              )),
        ),
      ],
    );
  }
}

class BlockWidget extends StatelessWidget {
  BlockWidget(
      {super.key, required this.simOrder, required this.simPayController});
  final SIMOrder simOrder;
  final SimPayController simPayController;

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // IntroduceCodeWidget(label: "Mã giảm giá ", value: "117.500"),
        // SizedBox(height: 13),
        // Divider(color: AppColors.greyD9D9D9),
        // TotalMoneyWidget(
        //     label: "Tổng tiền ",
        //     value: ConvertMoney.convertVNDMoney(simOrder.totalMoney(simOrder))),
        // SizedBox(height: 24),
        GestureDetector(
            onTap: () async {
              if (simPayController.isRememberMe.value == false) return;
              var _controller = Get.find<StepPickProductController>();
              if (_controller.countTime.value <= 0) {
                dialogAsk(
                  context,
                  'Đã hết thời gian giữ số. Vui lòng thực hiện lại.',
                  textButton: 'ĐÓNG',
                  isBackAction: true,
                  callOk: () {
                    Navigator.of(context).pop();
                    ActionBack.onBack(context: context);
                    // Navigator.of(context).popUntil(ModalRoute.withName("/page_list_sim"));
                  },
                );
              } else {
                await simPayController.onClickButtonConfirm();
              }
            },
            child: Container(
                width: double.infinity,
                padding: EdgeInsets.all(12),
                decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(8),
                    color: simPayController.enableBtn.value == true
                        ? AppColors.redCE3723
                        : AppColors.redCE3723.withValues(alpha: 0.7)),
                child: Center(
                    child: Obx(
                  () => simPayController.isLoading.value
                      ? CircularProgressIndicator(color: AppColors.whiteFFFFFF)
                      : Text(
                          "XÁC NHẬN",
                          style: AppTextStyle.s16Medium
                              .copyWith(color: AppColors.white),
                        ),
                )))),
        SizedBox(height: 12),
      ],
    );
  }
}
