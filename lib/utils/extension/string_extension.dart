import 'package:intl/intl.dart';

extension StringExt on String {
  bool validateDate() {
    RegExp regex =
        RegExp(r"^(0[1-9]|[12][0-9]|3[01])/(0[1-9]|1[012])/(19|20)\d\d$");
    if (this.isNotEmpty) {
      if (regex.hasMatch(this)) {
        if (DateFormat('dd/MM/yyyy')
                .parse(this)
                .toLocal()
                .millisecondsSinceEpoch >
            DateTime.now().millisecondsSinceEpoch) {
          return false;
        } else {
          return true;
        }
      } else {
        return false;
      }
    } else {
      return true;
    }
  }

  String normalizeVietnamese() {
    String ans = this.toLowerCase();
    // Replace Vietnamese characters
    ans = ans.replaceAll(RegExp(r'[àáạảãâầấậẩẫăằắặẳẵ]'), 'a');
    ans = ans.replaceAll(RegExp(r'[èéẹẻẽêềếệểễ]'), 'e');
    ans = ans.replaceAll(RegExp(r'[ìíịỉĩ]'), 'i');
    ans = ans.replaceAll(RegExp(r'[òóọỏõôồốộổỗơờớợởỡ]'), 'o');
    ans = ans.replaceAll(RegExp(r'[ùúụủũưừứựửữ]'), 'u');
    ans = ans.replaceAll(RegExp(r'[ỳýỵỷỹ]'), 'y');
    ans = ans.replaceAll(RegExp(r'đ'), 'd');
    return ans;
  }

  String getNameAvatar() {
    List<String> words =
        this.split(' ').where((w) => w.trim().isNotEmpty).toList();
    if (words.length >= 2) {
      return (words[0][0] + words[1][0]).toUpperCase();
    } else if (words.isNotEmpty) {
      return words[0][0].toUpperCase();
    } else {
      return '';
    }
  }

  bool validateEmail() {
    RegExp emailRegex = RegExp(
        r'^[\w-]+(\.[\w-]+)*@[a-zA-Z0-9-]+(\.[a-zA-Z0-9-]+)*\.[a-zA-Z]{2,}$');
    if (emailRegex.hasMatch(this)) {
      return false;
    } else {
      return true;
    }
  }

  bool validateName() {
    RegExp nameRegex =
        RegExp(r'[!@#$%^&*(),.?":{}|<>]'); // Matches letters and spaces only

    if (nameRegex.hasMatch(this) || RegExp(r'[0-9]').hasMatch(this)) {
      return true; // Name is valid
    } else {
      return false;
    }
  }

  bool validatePhone() {
    RegExp phoneRegex1 = RegExp(r'^84\d+');
    RegExp phoneRegex2 = RegExp(r'^0\d+');
    if ((phoneRegex1.hasMatch(this) && this.length == 11) ||
        (phoneRegex2.hasMatch(this) && this.length == 10)) {
      return false;
    } else {
      return true;
    }
  }

  bool validatePhoneNumber() {
    final phoneRegExp = RegExp(r'^0\d{9,11}$');
    if (this.isEmpty) {
      return false;
    } else if (!phoneRegExp.hasMatch(this) ||
        this.length < 10 ||
        this.length > 12) {
      return false;
    }
    return true;
  }

  bool validateBienXeMay() {
    final regex = RegExp(r'^\d{2}[A-Za-z][0-9A-Za-z]-\d{3}\.\d{2}$');
    if (this.isEmpty) {
      return false;
    } else if (regex.hasMatch(this) == false) {
      return false;
    }
    return true;
  }

  bool validateBienXeOto() {
    final regex = RegExp(r'^(\d{2}[A-Za-z]-\d{3}\.\d{2}|\d{2}[A-Za-z]\d{5})$');
    if (this.isEmpty) {
      return false;
    } else if (regex.hasMatch(this) == false) {
      return false;
    }
    return true;
  }

  String get convert84To0 {
    return this.replaceFirst(RegExp(r'^84'), '0');
  }

  /// setup biển số xe máy
  /// format: XXYZ-XXX.XX
  ///
  /// X: is number,
  /// Y: is letter,
  /// Z: maybe a letter or a number
  String setupBienSoXeMay() {
    String ans = this;
    // check
    if (ans.contains('-') && ans.contains('.')) {
      return ans;
    }
    // step1: add '-'
    if (ans.length >= 4 && ans.contains('-') == false) {
      String l = ans.substring(0, 4);
      String r = ans.substring(4);
      ans = l + '-' + r;
    }
    // step2: add '.'
    if (ans.length >= 8 && ans.contains('.') == false) {
      String l = ans.substring(0, 8);
      String r = ans.substring(8);
      ans = l + '.' + r;
    }
    return ans.toUpperCase();
  }

  /// setup biển số oto
  /// format: XXY-XXX.XX
  ///
  /// X: is number,
  /// Y: is letter,
  String setupBienSoOto() {
    String ans = this;
    // check
    if (ans.contains('-') && ans.contains('.')) {
      return ans;
    }
    // step1: add '-'
    if (ans.length >= 3 && ans.contains('-') == false) {
      String l = ans.substring(0, 3);
      String r = ans.substring(3);
      ans = l + '-' + r;
    }
    // step2: add '.'
    if (ans.length >= 7 && ans.contains('.') == false) {
      String l = ans.substring(0, 7);
      String r = ans.substring(7);
      ans = l + '.' + r;
    }
    return ans.toUpperCase();
  }
}
