import 'package:flutter/material.dart';
import 'package:shopping/constants/app_colors.dart';
import 'package:shopping/constants/app_text_style.dart';

class MoneyVnWidget extends StatelessWidget {
  const MoneyVnWidget(
      {super.key, required this.money, this.moneyStyle, this.symboyStyle});
  final String money;
  final TextStyle? moneyStyle;
  final TextStyle? symboyStyle;
  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          money,
          style: moneyStyle ??
              AppTextStyle.s18SemiBold.copyWith(color: AppColors.black),
        ),
        Text(
          " đ",
          style: symboyStyle ??
              AppTextStyle.s10SemiBold.copyWith(color: AppColors.black),
        ),
      ],
    );
  }
}
