class ReportFlagResponse {
  String? title;
  String? description;
  String? date;
  List<ListFlag>? list;

  ReportFlagResponse({
    this.title,
    this.description,
    this.date,
    this.list,
  });

  factory ReportFlagResponse.fromJson(Map<String, dynamic> json) =>
      ReportFlagResponse(
        title: json["title"],
        description: json["description"],
        date: json["date"],
        list: json["list"] == null
            ? []
            : List<ListFlag>.from(
                json["list"]!.map((x) => ListFlag.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "title": title,
        "description": description,
        "date": date,
        "list": list == null
            ? []
            : List<dynamic>.from(list!.map((x) => x.toJson())),
      };
}

class ListFlag {
  String? top;
  String? tinh;
  int? soLaCo;

  ListFlag({
    this.top,
    this.tinh,
    this.soLaCo,
  });

  factory ListFlag.fromJson(Map<String, dynamic> json) => ListFlag(
        top: json["top"],
        tinh: json["tinh"],
        soLaCo: json["so_la_co"],
      );

  Map<String, dynamic> toJson() => {
        "top": top,
        "tinh": tinh,
        "so_la_co": soLaCo,
      };
}
