import 'package:flutter/material.dart';
import 'package:shopping/constants/app_assets_paths.dart';
import 'package:shopping/constants/app_colors.dart';
import 'package:shopping/constants/app_text_style.dart';
import 'package:shopping/loading_screen.dart';
import 'package:shopping/models/home_model.dart';
import 'package:shopping/models/report_flag_response.dart';

class ReportFlagWidget extends StatelessWidget {
  const ReportFlagWidget(
      {super.key,
      required this.homeData,
      required this.reportFlag,
      required this.isLoading});
  final HomeData homeData;
  final ReportFlagResponse? reportFlag;
  final bool isLoading;
  @override
  Widget build(BuildContext context) {
    return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              Image.network(
                homeData.icon ?? '',
                width: 28,
                height: 28,
                errorBuilder: (context, error, stackTrace) => SizedBox(),
              ),
              SizedBox(width: 5),
              Expanded(
                child: Text(
                  homeData.name ?? '',
                  style: AppTextStyle.s16Bold
                      .copyWith(overflow: TextOverflow.ellipsis),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          // table report
          Flexible(
            child: isLoading
                ? ShimmerList()
                : Container(
                    width: double.infinity,
                    decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(10),
                        image: DecorationImage(
                            image: AssetImage(ImagePath.imgBgReportFlag),
                            fit: BoxFit.fill)),
                    child: Padding(
                      padding: EdgeInsets.only(
                          top: 23, right: 11, left: 11, bottom: 40),
                      child: Column(
                        children: [
                          Text(
                            reportFlag?.title ?? '',
                            style: AppTextStyle.s20Bold
                                .copyWith(color: AppColors.white),
                            textAlign: TextAlign.center,
                          ),
                          const SizedBox(height: 5),
                          Text(
                            reportFlag?.description ?? '',
                            style: AppTextStyle.s11Medium
                                .copyWith(color: AppColors.white),
                            textAlign: TextAlign.center,
                          ),
                          const SizedBox(height: 10),
                          _LableTableReport(),
                          const SizedBox(height: 3),
                          ListView.separated(
                            padding: EdgeInsets.zero,
                            shrinkWrap: true,
                            physics: NeverScrollableScrollPhysics(),
                            itemCount: reportFlag?.list?.length ?? 0,
                            itemBuilder: (context, index) {
                              return _ItemFlagWidget(
                                  itemFlag: reportFlag?.list?[index]);
                            },
                            separatorBuilder: (context, index) => SizedBox(
                                height:
                                    index + 1 >= (reportFlag?.list?.length ?? 0)
                                        ? null
                                        : 3),
                          ),
                          const SizedBox(height: 10),
                          Align(
                            alignment: Alignment.centerRight,
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Text('* ',
                                    style: AppTextStyle.s10.copyWith(
                                        fontWeight: FontWeight.w400,
                                        color: AppColors.white)),
                                Text(reportFlag?.date ?? '',
                                    style: AppTextStyle.s10Regular.copyWith(
                                        color: AppColors.white,
                                        fontStyle: FontStyle.italic)),
                              ],
                            ),
                          )
                        ],
                      ),
                    ),
                  ),
          )
        ]);
  }
}

class _LableTableReport extends StatelessWidget {
  const _LableTableReport();

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: AppColors.black.withOpacity(0.15),
        borderRadius: BorderRadius.only(
            topLeft: Radius.circular(4), topRight: Radius.circular(4)),
      ),
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: 12, vertical: 3),
        child: Row(
          children: [
            Expanded(
                flex: 52,
                child: Text(
                  'TOP',
                  style: AppTextStyle.s13Medium
                      .copyWith(color: AppColors.white, height: 16 / 13),
                )),
            Expanded(
                flex: 178,
                child: Text(
                  'Tỉnh/TP',
                  style: AppTextStyle.s13Medium
                      .copyWith(color: AppColors.white, height: 16 / 13),
                )),
            Expanded(
                flex: 90,
                child: Text(
                  'Số lá cờ',
                  style: AppTextStyle.s13Medium
                      .copyWith(color: AppColors.white, height: 16 / 13),
                )),
          ],
        ),
      ),
    );
  }
}

class _ItemFlagWidget extends StatelessWidget {
  const _ItemFlagWidget({required this.itemFlag});
  final ListFlag? itemFlag;
  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(color: AppColors.black.withOpacity(0.15)),
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: 12, vertical: 3),
        child: Row(
          children: [
            Expanded(
                flex: 52,
                child: Text(
                  itemFlag?.top ?? 'N/A',
                  style: AppTextStyle.s13SemiBold
                      .copyWith(color: AppColors.white, height: 16 / 13),
                )),
            Expanded(
                flex: 178,
                child: Text(
                  itemFlag?.tinh ?? 'N/A',
                  style: AppTextStyle.s13SemiBold
                      .copyWith(color: AppColors.white, height: 16 / 13),
                )),
            Expanded(
                flex: 90,
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Image.asset(ImagePath.imgIconFlag, width: 15, height: 10),
                    const SizedBox(width: 5),
                    Text(
                      '${itemFlag?.soLaCo}',
                      style: AppTextStyle.s13SemiBold
                          .copyWith(color: AppColors.white, height: 16 / 13),
                    ),
                  ],
                )),
          ],
        ),
      ),
    );
  }
}
