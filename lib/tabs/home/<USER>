import 'package:flutter/widgets.dart';
import 'package:get/get.dart';
import 'package:shopping/apis/ShoppingRepository.dart';
import 'package:shopping/app_services/api_service.dart';
import 'package:shopping/app_services/app_storage.dart';
import 'package:shopping/constants/enum_package_type.dart';
import 'package:shopping/enum/home_type.dart';
import 'package:shopping/loading_screen.dart';
import 'package:shopping/models/home_model.dart';
import 'package:shopping/models/list_sim_response.dart';
import 'package:shopping/models/report_flag_response.dart';
import 'package:shopping/pages/sim/item_sim_action.dart';
import 'package:shopping/tabs/home/<USER>/item_package_action.dart';
import 'package:shopping/widgets/app_dialog.dart';

class ShoppingHomeController extends GetxController {
  final int show_product_id;
  final List<PackageType> listTopAction = [
    PackageType.sim,
    PackageType.hotDeal,
    PackageType.data,
    PackageType.SMS,
    PackageType.combo,
    PackageType.insuranceABIC
  ];
  List<String>? banner = [
    "https://images.wallpapersden.com/image/download/purple-sunrise-4k-vaporwave_bGplZmiUmZqaraWkpJRmbmdlU.jpg"
  ];
  Future<void> loadContents(int pageType) async {}

  ScrollController? scrollController;
  var isShowAppBar = false.obs;
  // list data màn home (hiển thị theo cấu hình)
  List<HomeData> listHomeData = [];
  // loading
  Rx<bool> isLoading = false.obs;
  // loading
  Rx<bool> isLoadingSIM = false.obs;
  // loading
  Rx<bool> isLoadingRandomSIM = false.obs;
  Rx<bool> isShowBtnRdSim = false.obs;
  // loading report
  Rx<bool> isLoadingReport = true.obs;
  // list SIM
  Map<int, List<ItemSim>> mapListSim = {};
  // list báo cáo lá cờ
  ReportFlagResponse? reportFlag;
  // random SIM
  Rx<Offset> fabPosition = Offset(20, 20).obs;
  final double maxW = Get.size.width;
  final double maxH = Get.size.height;
  // banner
  List<String?>? listLinkBanner;
  //
  ShoppingHomeController(this.show_product_id);

  @override
  void onInit() async {
    super.onInit();
    scrollController = ScrollController();
    scrollController?.addListener(() {
      if (scrollController!.position.extentBefore > 0) {
        isShowAppBar.value = true;
      } else {
        isShowAppBar.value = false;
      }
    });
    fabPosition.value = Offset(maxW - 170, maxH - 130);
    await _initData();
  }

  Future _initData() async {
    listHomeData.clear();
    await _getHomeData();
  }

  Future _getHomeData() async {
    isLoading.toggle();
    // get data home
    var res = await DelayLoadingCallApi.delayCallApi(
        ShoppingRepository.instance.getHome2());
    listHomeData = res?.data ?? [];
    // get banner
    var resBanner = await DelayLoadingCallApi.delayCallApi(
        ShoppingRepository.instance.getHomeBanner());
    listLinkBanner = resBanner ?? [];
    isLoading.toggle();
    // check trong config home có list SIM không => gọi API get list SIM
    for (var e in listHomeData) {
      if (e.getHomeType == HomeType.sim) {
        isShowBtnRdSim.value = true;
        isLoadingSIM.toggle();
        var list = await _getListSim();
        mapListSim[e.id!] = list ?? [];
        update([HomeControllerUpdateKey.updateListSIM]);
      } else if (e.getHomeType == HomeType.reportFlag) {
        await _getReportFlag();
      }
    }
    update([HomeControllerUpdateKey.updateHomePackage]);

    if (show_product_id != 0) {
      print("Product id ${show_product_id}");
      isLoading.toggle();
      ShoppingRepository.instance.getProduct2(show_product_id).then((res) {
        isLoading.toggle();
        if (res.code == 0) {
          var product = res.data ?? null;
          if (product != null) {
            print("Open Product");
            ItemPackageAction.openDetail(Get.context!, product);
          } else {
            print("Get product null");
          }
        } else {
          print("Get product error");
        }
      }).onError((error, stackTrace) {
        isLoading.toggle();
      });
    } else {}
  }

  Future<List<ItemSim>?> _getListSim() async {
    var res =
        await ShoppingRepository.instance.getListSim(perPage: 5, keyword: "");
    isLoadingSIM.toggle();
    if (res?.code == 0) {
      return res?.data?.items ?? [];
    }
    return null;
  }

  Future<void> _getReportFlag() async {
    var res = await ShoppingRepository.instance.getReportFlag();
    isLoadingReport.toggle();
    if (res.code == CODE_SUCCESS) {
      reportFlag = res.data;
    } else {
      reportFlag = null;
    }
    update([
      HomeControllerUpdateKey.updateReportFlag,
      HomeControllerUpdateKey.updateHomePackage,
    ]);
  }

  void onDragBtnRandom(Offset offset) {
    double tmpdx = offset.dx;
    double tmpdy = offset.dy;
    if (tmpdx < 0) {
      tmpdx = 0;
    } else if (tmpdx >= maxW - 170) {
      tmpdx = maxW - 170;
    }
    if (tmpdy < 0) {
      tmpdy = 0;
    } else if (tmpdy >= maxH - 130) {
      tmpdy = maxH - 130;
    }
    fabPosition.value = Offset(tmpdx, tmpdy);
  }

  Future onClickRandonSim({required BuildContext context}) async {
    if (isLoadingRandomSIM.value) {
      return;
    }
    isLoadingRandomSIM.toggle();
    var response = await ShoppingRepository.instance.getRandomSim();
    String messErr = response?.message ?? '';
    isLoadingRandomSIM.toggle();
    if (response?.code == 0) {
      ItemSim? simRandom = response?.data?.item;
      // set new value storage
      AppStorage.instance.setBool2(SKeysPK.isBookSimFromHome, true);

      ItemSimAction.openSheetBookSim(context: context, itemSim: simRandom);
    } else {
      dialogAsk(context, messErr);
    }
  }
}

class HomeControllerUpdateKey {
  static const String updateHomePackage = 'updateHomePackage';
  static const String updateListSIM = 'updateListSIM';
  static const String updateReportFlag = 'update_report_flag';
}
