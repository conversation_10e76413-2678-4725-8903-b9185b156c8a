import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:shopping/constants/app_assets_paths.dart';
import 'package:shopping/constants/app_colors.dart';
import 'package:shopping/constants/app_text_style.dart';
import 'package:shopping/models/product.dart';
import 'package:shopping/pages/insurance/item_insurance_controller.dart';

class ItemInsuranceWidget extends StatelessWidget {
  const ItemInsuranceWidget(
      {super.key, required this.productInsurance, this.itemWidth});
  final Product? productInsurance;
  final double? itemWidth;
  @override
  Widget build(BuildContext context) {
    return GetBuilder<ItemInsuranceController>(
        init: ItemInsuranceController(),
        builder: (controller) => InkWell(
            splashColor: Colors.transparent,
            onTap: () {
              controller.openSheetDetail(
                  context: context, productIns: productInsurance);
            },
            child: Stack(
              children: [
                Container(
                  height: 174,
                  width: itemWidth,
                  decoration: BoxDecoration(
                      color: AppColors.blueD6EEF1,
                      borderRadius: BorderRadius.circular(14)),
                ),
                Positioned(
                  right: 0,
                  bottom: 44,
                  child: Opacity(
                    opacity: 0.5,
                    child: SvgPicture.asset(
                      productInsurance?.getLogoIconInsType ?? '',
                      width: 80,
                      height: 80,
                      placeholderBuilder: (context) => SvgPicture.asset(
                          SvgPath.svgInsMoto,
                          width: 80,
                          height: 80),
                    ),
                  ),
                ),
                Positioned.fill(
                  child: Container(
                    width: itemWidth,
                    height: 174,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(14),
                    ),
                    padding: EdgeInsets.symmetric(vertical: 10, horizontal: 10),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          productInsurance?.getContent(1) ?? '',
                          style: AppTextStyle.s15SemiBold
                              .copyWith(color: AppColors.black),
                          maxLines: 3,
                          overflow: TextOverflow.ellipsis,
                        ),
                        Expanded(
                            child: Align(
                          alignment: Alignment.bottomCenter,
                          child: Column(
                            mainAxisSize: MainAxisSize.min,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              // logo nhà cung cấp
                              (productInsurance?.getListLogo ?? []).isEmpty
                                  ? SvgPicture.asset(
                                      SvgPath.svgLogoABIC,
                                      width: 45,
                                      height: 13,
                                    )
                                  : Image.network(
                                      productInsurance?.getListLogo?[0] ?? '',
                                      width: 45,
                                      height: 13,
                                      errorBuilder:
                                          (context, error, stackTrace) =>
                                              SvgPicture.asset(
                                                SvgPath.svgLogoABIC,
                                                width: 45,
                                                height: 13,
                                              )),
                              const SizedBox(height: 5),
                              Text(
                                productInsurance?.getContent(3) ?? '',
                                style: AppTextStyle.s13Regular
                                    .copyWith(color: AppColors.black),
                                maxLines: 3,
                                overflow: TextOverflow.ellipsis,
                              ),
                              const SizedBox(height: 5),

                              SizedBox(
                                  height: 35,
                                  child: InkWell(
                                      onTap: () {
                                        controller.openOrdScreen(
                                            context: context,
                                            productIns: productInsurance);
                                      },
                                      splashColor: Colors.transparent,
                                      child: Container(
                                        padding:
                                            EdgeInsets.symmetric(vertical: 9),
                                        decoration: BoxDecoration(
                                          borderRadius:
                                              BorderRadius.circular(30),
                                          border: Border.all(
                                              width: 1,
                                              color: AppColors.blue30AAB7),
                                        ),
                                        child: Center(
                                          child: Text(
                                            'Đăng ký'.toUpperCase(),
                                            style: AppTextStyle.s13SemiBold
                                                .copyWith(
                                                    height: 17 / 13,
                                                    color:
                                                        AppColors.blue30AAB7),
                                          ),
                                        ),
                                      ))),
                            ],
                          ),
                        ))
                      ],
                    ),
                  ),
                )
              ],
            )
            // Stack(
            //   children: [
            //     Container(
            //       height: 174,
            //       decoration: BoxDecoration(
            //           color: AppColors.blueD6EEF1,
            //           borderRadius: BorderRadius.circular(14)),
            //       padding: EdgeInsets.symmetric(horizontal: 10),
            //       child: Column(
            //         crossAxisAlignment: CrossAxisAlignment.start,
            //         mainAxisSize: MainAxisSize.min,
            //         children: [
            //           Container(
            //             margin: EdgeInsets.only(top: 10),
            //             height: 56,
            //             child: Row(
            //               mainAxisSize: MainAxisSize.min,
            //               crossAxisAlignment: CrossAxisAlignment.start,
            //               children: [
            //                 Visibility(
            //                     visible:
            //                         productInsurance?.isPriceContent(1) ??
            //                             false,
            //                     child: Text('Chỉ từ: ',
            //                         style: AppTextStyle.s13Regular
            //                             .copyWith(color: AppColors.black))),
            //                 Expanded(
            //                   child: Text(
            //                     productInsurance?.getContent(1) ?? '',
            //                     style: AppTextStyle.s15SemiBold.copyWith(
            //                         color:
            //                             productInsurance?.isPriceContent(1) ==
            //                                     true
            //                                 ? AppColors.redCE3722
            //                                 : AppColors.black),
            //                     maxLines: 3,
            //                   ),
            //                 ),
            //                 Visibility(
            //                     visible:
            //                         productInsurance?.isPriceContent(1) ??
            //                             false,
            //                     child: Text('đ',
            //                         style: AppTextStyle.s10Bold
            //                             .copyWith(color: AppColors.redCE3722),
            //                         overflow: TextOverflow.ellipsis))
            //               ],
            //             ),
            //           ),
            //           // logo nhà cung cấp
            //           Container(
            //             margin: EdgeInsets.only(top: 26),
            //             height: 13,
            //             child: (productInsurance?.getListLogo ?? []).isEmpty
            //                 ? Image.asset(
            //                     ImagePath.logoABIC,
            //                     width: 45,
            //                     height: 13,
            //                   )
            //                 : Image.network(
            //                     productInsurance?.getListLogo?[0] ?? '',
            //                     // width: 45,
            //                     height: 13,
            //                     errorBuilder: (context, error, stackTrace) =>
            //                         Image.asset(
            //                       ImagePath.logoABIC,
            //                       width: 45,
            //                       height: 13,
            //                     ),
            //                   ),
            //           ),
            //           Container(
            //             margin: EdgeInsets.only(top: 5),
            //             height: 22,
            //             child: Row(
            //               mainAxisSize: MainAxisSize.min,
            //               crossAxisAlignment: CrossAxisAlignment.start,
            //               children: [
            //                 Visibility(
            //                   visible: productInsurance?.isPriceContent(3) ??
            //                       false,
            //                   child: Text(
            //                     'Chỉ từ: ',
            //                     style: AppTextStyle.s13Regular.copyWith(
            //                         color: AppColors.black, height: 20 / 14),
            //                   ),
            //                 ),
            //                 Expanded(
            //                   child: Text(
            //                     productInsurance?.getContent(3) ?? '',
            //                     style: AppTextStyle.s13Regular.copyWith(
            //                         color:
            //                             productInsurance?.isPriceContent(1) ==
            //                                     true
            //                                 ? AppColors.redCE3722
            //                                 : AppColors.black),
            //                     maxLines: 3,
            //                   ),
            //                 ),
            //                 Visibility(
            //                     visible:
            //                         productInsurance?.isPriceContent(3) ??
            //                             false,
            //                     child: Text('đ',
            //                         style: AppTextStyle.s10SemiBold
            //                             .copyWith(color: AppColors.redCE3722),
            //                         overflow: TextOverflow.ellipsis))
            //               ],
            //             ),
            //           ),
            //           Container(
            //             margin: EdgeInsets.only(top: 2, bottom: 5),
            //             height: 35,
            //             child: InkWell(
            //                 onTap: () {
            //                   controller.openOrdScreen(
            //                       context: context,
            //                       productIns: productInsurance);
            //                 },
            //                 splashColor: Colors.transparent,
            //                 child: Container(
            //                   padding: EdgeInsets.symmetric(vertical: 9),
            //                   decoration: BoxDecoration(
            //                     borderRadius: BorderRadius.circular(30),
            //                     border: Border.all(
            //                         width: 1, color: AppColors.blue30AAB7),
            //                   ),
            //                   child: Center(
            //                     child: Text(
            //                       'Đăng ký'.toUpperCase(),
            //                       style: AppTextStyle.s13SemiBold.copyWith(
            //                           height: 17 / 13,
            //                           color: AppColors.blue30AAB7),
            //                     ),
            //                   ),
            //                 )),
            //           ),
            //         ],
            //       ),
            //     ),
            //     Positioned(
            //         right: 0,
            //         bottom: 60,
            //         child: SvgPicture.asset(
            //           productInsurance?.getLogoIconInsType ?? '',
            //           width: 80,
            //           height: 80,
            //           placeholderBuilder: (context) => SvgPicture.asset(
            //               SvgPath.svgInsMoto,
            //               width: 80,
            //               height: 80),
            //         ))
            //   ],
            // ),
            ));
  }
}
