import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:shopping/access_permisstion_screen.dart';
import 'package:shopping/constants/app_colors.dart';
import 'package:shopping/constants/app_text_style.dart';
import 'package:shopping/constants/enum_insurance_type.dart';
import 'package:shopping/models/product.dart';
import 'package:shopping/pages/insurance/pages/car/insurance_car_step_product_screen.dart';
import 'package:shopping/pages/insurance/pages/land/insurance_land_step_product_screen.dart';
import 'package:shopping/pages/insurance/pages/moto/insurance_moto_step_product_screen.dart';
import 'package:shopping/pages/insurance/widgets/insurance_detail_widget.dart';
import 'package:shopping/shopping_controller.dart';

class ItemInsuranceController extends GetxController {
  void openOrdScreen(
      {required BuildContext context, required Product? productIns}) {
    // chặn case chưa cấp quyền
    if (Get.isRegistered<ShoppingController>()) {
      var _controller = Get.find<ShoppingController>();
      if (_controller.shopState.value == ShopState.USER_NOT_ACCEPT_ACCESS) {
        Navigator.of(context)
            .push(
          MaterialPageRoute(
            builder: (_) => AccessPermissionScreen(index: 4),
          ),
        )
            .then((value) {
          if (value == true) {
            //đồng ý vào cấp quyền truy cập
            // controller.setAcceptPermission(true);
            _controller.permissionAccept();
          } else {
            _controller.setAcceptPermission(true);
          }
        });
      }
    }
    var _controller = Get.find<ShoppingController>();
    if (_controller.shopState.value == ShopState.LOGIN_OK) {
      // case đã cấp quyền
      switch (productIns?.getInsType) {
        case InsuranceType.moto: //  xe may
          Navigator.push(
              context,
              MaterialPageRoute(
                  builder: (_) =>
                      InsuranceMotoStepProductScreen(productIns: productIns)));
          break;
        case InsuranceType.car: // oto
          Navigator.push(
              context,
              MaterialPageRoute(
                  builder: (_) =>
                      InsuranceCarStepProductScreen(productIns: productIns)));
          break;
        case InsuranceType.land: // nha dat
          Navigator.push(
              context,
              MaterialPageRoute(
                  builder: (_) =>
                      InsuranceLandStepProductScreen(productIns: productIns)));
          break;
        default: //  xe may
          Navigator.push(
              context,
              MaterialPageRoute(
                  builder: (_) =>
                      InsuranceMotoStepProductScreen(productIns: productIns)));
      }
    }
  }

  void openSheetDetail(
      {required BuildContext context, required Product? productIns}) {
    showModalBottomSheet(
        backgroundColor: AppColors.white,
        context: context,
        enableDrag: true,
        shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.vertical(top: Radius.circular(30))),
        isScrollControlled: true,
        builder: (context) => Container(
            padding: EdgeInsets.symmetric(horizontal: 8),
            decoration: BoxDecoration(
                color: AppColors.white,
                borderRadius: BorderRadius.circular(30)),
            width: double.infinity,
            child: Stack(
              children: [
                Container(
                  margin: EdgeInsets.only(bottom: 100),
                  child: DraggableScrollableSheet(
                    expand: false,
                    initialChildSize: 0.7,
                    minChildSize: 0.68,
                    maxChildSize: 0.9,
                    builder: (context, scrollController) =>
                        SingleChildScrollView(
                      physics: NeverScrollableScrollPhysics(),
                      child: InsuranceDetailWidget(
                          scrollController: scrollController,
                          productIns: productIns),
                    ),
                  ),
                ),
                Positioned(
                    bottom: 0,
                    left: 0,
                    right: 0,
                    child: GestureDetector(
                      onTap: () {
                        // Navigator.pop(context);
                        openOrdScreen(context: context, productIns: productIns);
                      },
                      child: Container(
                        width: MediaQuery.of(context).size.width - 60,
                        height: 100,
                        color: AppColors.white,
                        margin: EdgeInsets.symmetric(horizontal: 15),
                        child: Center(
                          child: Container(
                            width: double.infinity,
                            height: 45,
                            decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(8),
                                color: AppColors.blue30AAB7),
                            child: Center(
                                child: Text(
                              'ĐĂNG KÝ NGAY',
                              style: AppTextStyle.s16Medium
                                  .copyWith(color: AppColors.white),
                            )),
                          ),
                        ),
                      ),
                    ))
              ],
            )));
  }
}
