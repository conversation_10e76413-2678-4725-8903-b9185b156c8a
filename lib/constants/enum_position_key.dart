import 'package:shopping/constants/enum_filter_product.dart';
import 'package:shopping/models/product.dart';
import 'package:shopping/pages/package/widgets/filter_endraw_widget.dart';

enum PositionKey { name, code, cycle, price, other }

extension PositionKeyExt on PositionKey {
  String getName() {
    switch (this) {
      case (PositionKey.name):
        return 'name';
      case (PositionKey.code):
        return 'code';
      case (PositionKey.cycle):
        return 'cycle';
      case (PositionKey.price):
        return 'price';
    
      case (PositionKey.other):
        return 'other';
    }
  }

  String getContent(Product product) {
    switch (this) {
      case (PositionKey.name):
        return (product.name ?? '').toString();
      case (PositionKey.code):
        return (product.code ?? '').toString();
      case (PositionKey.price):
        return product.price == null ? '' : '${product.price}đ';
      case (PositionKey.cycle):
        {
          String tenCK = FilterCondition.listCycleType
              .where((element) => element == product.cycleType)
              .first
              .nameCycleType;
          return '${product.cycle} ${tenCK}';
        }

      default:
        return '';
    }
  }
}
