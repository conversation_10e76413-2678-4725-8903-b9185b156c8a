import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:shopping/app_services/app_storage.dart';
import 'package:shopping/constants/app_assets_paths.dart';
import 'package:shopping/constants/app_colors.dart';
import 'package:shopping/constants/app_text_style.dart';
import 'package:shopping/pages/sim/item_sim_action_controller.dart';
import 'package:shopping/tabs/home/<USER>/timer_count.dart';
import 'package:shopping/widgets/app_text_field_number.dart';

class BookSimOTPWidget extends StatelessWidget {
  const BookSimOTPWidget(
      {super.key, required this.openPickProduct, required this.ordId});
  final Function() openPickProduct;
  final int? ordId;
  @override
  Widget build(BuildContext context) {
    return GetBuilder<BookSimOTPController>(
        init: BookSimOTPController(ordId: ordId),
        builder: (controller) => Padding(
              padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 7),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Container(
                    height: 22,
                    child: Stack(
                      children: [
                        Padding(
                          padding: const EdgeInsets.only(bottom: 3),
                          child: Align(
                            alignment: Alignment.center,
                            child: Container(
                              width: 45,
                              height: 5,
                              decoration: BoxDecoration(
                                  color: AppColors.greyE5E6EC,
                                  borderRadius: BorderRadius.circular(10)),
                            ),
                          ),
                        ),
                        Align(
                          alignment: Alignment.centerRight,
                          child: GestureDetector(
                            onTap: () {
                              // close
                              Navigator.of(context).pop();
                              // clear value storage
                              AppStorage.instance
                                  .removeString2(SKeysPK.isBookSimFromHome);
                            },
                            child: SvgPicture.asset(SvgPath.svgIconClose),
                          ),
                        )
                      ],
                    ),
                  ),

                  // Center(
                  //   child: Container(
                  //     width: 45,
                  //     height: 5,
                  //     decoration: BoxDecoration(color: AppColors.greyE5E6EC, borderRadius: BorderRadius.circular(10)),
                  //   ),
                  // ),
                  // GestureDetector(
                  //   onTap: () {
                  //     // close
                  //     Navigator.of(context).pop();
                  //   },
                  //   child: Align(alignment: Alignment.centerRight, child: SvgPicture.asset(SvgPath.svgIconClose)),
                  // ),

                  Text(
                    "Nhập mã xác thực",
                    style: AppTextStyle.s18Bold,
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 22),
                  Text(
                    "Vui lòng nhập mã xác thực đã được gửi đến số điện thoại liên hệ",
                    style: AppTextStyle.s13Regular.copyWith(height: 20 / 14),
                  ),
                  const SizedBox(height: 24),
                  CustomAppTextField(
                    hintText: "Nhập mã xác nhận",
                    maxLength: 20,
                    controller: controller.otpController,
                  ),
                  const SizedBox(height: 10),
                  // count down time
                  Obx(() => TimerCount(
                        isBuySim: true,
                        resentOtp: () {
                          controller.setResend(true);
                          controller.resendOTP(context: context);
                        },
                        rs: controller.resend.value,
                        isCountDown: controller.isCountDown.value,
                        isCallApiSuccess: controller.isCallAPISuccess.value,
                      )),
                  const SizedBox(height: 21),
                  GestureDetector(
                    onTap: () async {
                      await controller.onClickBookSim(
                          context: context,
                          openPickProduct: () {
                            openPickProduct.call();
                          });
                    },
                    child: Container(
                      width: double.infinity,
                      padding: EdgeInsets.all(12),
                      decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(8),
                          color: AppColors.green30AAB7),
                      child: Center(
                          child: Obx(() => controller.isLoading.value
                              ? CircularProgressIndicator(
                                  color: AppColors.whiteFFFFFF)
                              : Text(
                                  "TIẾP TỤC",
                                  style: AppTextStyle.s16Medium
                                      .copyWith(color: AppColors.white),
                                ))),
                    ),
                  ),
                  const SizedBox(height: 31),
                ],
              ),
            ));
  }
}
