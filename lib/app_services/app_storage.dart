import 'package:get/get.dart';
import 'package:shared_preferences/shared_preferences.dart';

class AppStorage extends GetxService {
  SharedPreferences? _sharedPreferences;
  init({int? appId}) async {
    this._sharedPreferences = await SharedPreferences.getInstance();
    await setInt2(SKeysPK.appId, appId ?? 1);
  }

  AppStorage._privateConstructor();

  static final AppStorage instance = AppStorage._privateConstructor();

  setString2(String key, String value) {
    _sharedPreferences?.setString(key, value);
  }

  String? getString2(String key) {
    return _sharedPreferences?.getString(key);
  }

  setInt2(String key, int value) async {
    await _sharedPreferences?.setInt(key, value);
  }

  int? getInt2(String key) {
    return _sharedPreferences?.getInt(key);
  }

  setListString2(String key, List<String> value) {
    _sharedPreferences?.setStringList(key, value);
  }

  List<String>? getListString2(String key) {
    return _sharedPreferences?.getStringList(key);
  }

  removeString2(String key) {
    _sharedPreferences?.remove(key);
  }

  Future<bool> setBool2(String key, bool value) async {
    return _sharedPreferences!.setBool(key, value);
  }

  bool? getBool2(String key) {
    return _sharedPreferences?.getBool(key);
  }
}

class SKeysPK {
  static const isLogin = 'app_login-pk';
  static const token = 'token-pk';
  static const sessionId = 'sessionId-pk';
  static const refreshToken = 'refreshToken-pk';
  static const expiresInToken = 'expiresInToken-pk';
  static const password = 'password-pk';
  static const userName = 'userName-pk';
  static const appId = 'appId_for_muadi';

  // check when user first login
  static const firstLogin = 'firstLogin-pk';

  //otp
  static const countOtp = 'countOtp-pk';
  static const timeStart = 'time-pk';

  //first for Permission;
  static const firstPermissionPhoto = 'first-permisison-photo';
  // locate book SIM
  static const isBookSimFromHome = 'muadi_is_book_sim_from_home';
}
