import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:shopping/constants/app_colors.dart';
import 'package:shopping/constants/app_text_style.dart';
import 'package:shopping/enum/enum_sim_type.dart';
import 'package:shopping/models/product.dart';
import 'package:shopping/models/sim_model.dart';
import 'package:shopping/pages/sim/widgets/dash_line_widget.dart';
import 'package:shopping/utils/check_overflow_text.dart';
import 'package:shopping/utils/extension/int_extension.dart';

class TicketOrderInforWidget extends StatelessWidget {
  const TicketOrderInforWidget(
      {super.key, this.phoneNumber, this.priceSim, this.product, this.simType});
  final String? phoneNumber;
  final Product? product;
  final num? priceSim;
  final SimType? simType;
  @override
  Widget build(BuildContext context) {
    return Column(mainAxisSize: MainAxisSize.min, children: [
      TicketLableWidget(),
      LineBarrierWidget(),
      SimAndProductInforWidget(
        isLastContent: true,
        priceSim: priceSim,
        product: product,
        phoneNumber: phoneNumber,
        simType: simType,
      )
    ]);
  }
}

class TicketPayInforWidget extends StatelessWidget {
  TicketPayInforWidget({super.key, required this.simOrder});
  final SIMOrder simOrder;

  @override
  Widget build(BuildContext context) {
    final SimType? simType = simOrder.getSimType;
    return Column(mainAxisSize: MainAxisSize.min, children: [
      TicketLableWidget(),
      LineBarrierWidget(),
      OrderInforWidget(simOrder: simOrder, simType: simType),
      LineBarrierWidget(),
      ProductInforPayWidget(
        phoneNumber: simOrder.phoneNumber,
        product: new Product(
            id: simOrder.productId,
            name: simOrder.productName,
            price: simOrder.giaGoi ?? 0),
        priceSim: simOrder.getGiaSim(simOrder),
        simType: simType,
      ),
      LineBarrierWidget(),
      PriceInfor(
        product: new Product(
            id: simOrder.productId,
            name: simOrder.productName,
            price: simOrder.giaGoi ?? 0),
        priceSim: simOrder.getGiaSim(simOrder),
      ),
      LineBarrierWidget(),
      TicketTotalWidget(
        simOrder: simOrder,
      )
    ]);
  }
}

class TicketLableWidget extends StatelessWidget {
  const TicketLableWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.only(top: 11, bottom: 3),
      decoration: BoxDecoration(
          color: AppColors.blue30AAB7,
          borderRadius: BorderRadius.only(
              topLeft: Radius.circular(14), topRight: Radius.circular(14))),
      child: Center(
        child: Text(
          'THÔNG TIN ĐƠN HÀNG',
          style: AppTextStyle.s14Bold.copyWith(color: AppColors.white),
        ),
      ),
    );
  }
}

class TicketTotalWidget extends StatelessWidget {
  TicketTotalWidget({super.key, required this.simOrder});
  final SIMOrder simOrder;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(vertical: 13, horizontal: 12),
      decoration: BoxDecoration(
          color: AppColors.blue30AAB7,
          borderRadius: BorderRadius.only(
              bottomLeft: Radius.circular(14),
              bottomRight: Radius.circular(14))),
      child: LineContentWidget(
          lable: 'Tổng tiền',
          content: ConvertMoney.convertVNDMoney(simOrder.totalMoney(simOrder)),
          isMoney: true),
    );
  }
}

class LineBarrierWidget extends StatelessWidget {
  const LineBarrierWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      color: AppColors.blue30AAB7,
      height: 14,
      width: MediaQuery.of(context).size.width - 16,
      child: OverflowBox(
        maxWidth: MediaQuery.of(context).size.width - 16,
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 14,
              height: 14,
              decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(7),
                  color: AppColors.white),
            ),
            Expanded(child: DashLineWidget()),
            Container(
              width: 14,
              height: 14,
              decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(7),
                  color: AppColors.white),
            ),
          ],
        ),
      ),
    );
  }
}

class SimAndProductInforWidget extends StatelessWidget {
  const SimAndProductInforWidget({
    super.key,
    this.isLastContent,
    this.product,
    this.phoneNumber,
    this.priceSim,
    this.simType,
    this.email,
  });
  final bool? isLastContent;
  final String? phoneNumber;
  final Product? product;
  final num? priceSim;
  final SimType? simType;
  final String? email;
  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(vertical: 7, horizontal: 12),
      decoration: BoxDecoration(
          color: AppColors.blue30AAB7,
          borderRadius: (isLastContent ?? false)
              ? BorderRadius.only(
                  bottomLeft: Radius.circular(14),
                  bottomRight: Radius.circular(14))
              : null),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          LineContentWidget(lable: 'Dịch vụ', content: 'Mua sim điện thoại'),
          const SizedBox(height: 9),
          LineContentWidget(lable: 'Số SIM', content: phoneNumber ?? ''),
          const SizedBox(height: 9),
          LineContentWidget(lable: 'Nhà mạng', content: 'Vinaphone'),
          const SizedBox(height: 9),
          LineContentWidget(lable: 'Loại thuê bao', content: 'Trả trước'),
          const SizedBox(height: 9),
          LineContentWidget(
              lable: 'Loại SIM', content: simType?.getName ?? 'SIM vật lý'),
          const SizedBox(height: 9),
          LineContentWidget(
              lable: 'Giá SIM',
              content: ConvertMoney.convertVNDMoney(priceSim),
              isMoney: true),
          const SizedBox(height: 9),
          LineContentWidget(
            lable: 'Gói cước',
            content: product?.name ?? '',
          ),
          const SizedBox(height: 9),
          LineContentWidget(
              lable: 'Giá gói cước',
              content: ConvertMoney.convertVNDMoney(product?.price ?? 0),
              // content: (product?.price ?? 0).convertToVietnamesMoney(),
              isMoney: true),
        ],
      ),
    );
  }
}

class ProductInforPayWidget extends StatelessWidget {
  const ProductInforPayWidget({
    super.key,
    this.isLastContent,
    this.product,
    this.phoneNumber,
    this.priceSim,
    this.simType,
    this.email,
  });
  final bool? isLastContent;
  final String? phoneNumber;
  final Product? product;
  final num? priceSim;
  final SimType? simType;
  final String? email;
  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(vertical: 7, horizontal: 12),
      decoration: BoxDecoration(
          color: AppColors.blue30AAB7,
          borderRadius: (isLastContent ?? false)
              ? BorderRadius.only(
                  bottomLeft: Radius.circular(14),
                  bottomRight: Radius.circular(14))
              : null),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          LineContentWidget(lable: 'Dịch vụ', content: 'Mua sim điện thoại'),
          const SizedBox(height: 9),
          LineContentWidget(lable: 'Số SIM', content: phoneNumber ?? ''),
          const SizedBox(height: 9),
          LineContentWidget(lable: 'Nhà mạng', content: 'Vinaphone'),
          const SizedBox(height: 9),
          LineContentWidget(lable: 'Loại thuê bao', content: 'Trả trước'),
          const SizedBox(height: 9),
          LineContentWidget(
              lable: 'Loại SIM', content: simType?.getName ?? 'SIM vật lý'),
          const SizedBox(height: 9),
          LineContentWidget(
            lable: 'Gói cước',
            content: product?.name ?? '',
          ),
        ],
      ),
    );
  }
}

class PriceInfor extends StatelessWidget {
  const PriceInfor({
    super.key,
    this.isLastContent,
    this.product,
    this.priceSim,
  });
  final bool? isLastContent;

  final Product? product;
  final num? priceSim;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(vertical: 7, horizontal: 12),
      decoration: BoxDecoration(
          color: AppColors.blue30AAB7,
          borderRadius: (isLastContent ?? false)
              ? BorderRadius.only(
                  bottomLeft: Radius.circular(14),
                  bottomRight: Radius.circular(14))
              : null),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          LineContentWidget(
              lable: 'Giá SIM',
              content: ConvertMoney.convertVNDMoney(priceSim),
              isMoney: true),
          const SizedBox(height: 9),
          LineContentWidget(
              lable: 'Giá gói cước',
              content: ConvertMoney.convertVNDMoney(product?.price ?? 0),
              // content: (product?.price ?? 0).convertToVietnamesMoney(),
              isMoney: true),
          const SizedBox(height: 9),
          LineContentWidget(
              lable: 'Giảm giá',
              content: 0.convertToVietnamesMoney(),
              isMoney: true),
        ],
      ),
    );
  }
}

class OrderInforWidget extends StatelessWidget {
  OrderInforWidget({super.key, required this.simOrder, this.simType});
  final SIMOrder simOrder;
  final SimType? simType;
  @override
  Widget build(BuildContext context) {
    double maxWidth = MediaQuery.of(context).size.width - 150;
    return Container(
      padding: EdgeInsets.symmetric(vertical: 7, horizontal: 12),
      color: AppColors.blue30AAB7,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // simOrder.fullName ?? ""
          CheckOverFlow.doesTextOverflow(
                  text: simOrder.fullName ?? "",
                  style: AppTextStyle.s14Medium
                      .copyWith(color: AppColors.white, height: 17 / 14),
                  maxWidth: maxWidth)
              ? Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      'Người nhận',
                      style: AppTextStyle.s14Regular
                          .copyWith(color: AppColors.white, height: 17 / 14),
                    ),
                    const SizedBox(height: 5),
                    Flexible(
                      child: Text(
                        simOrder.fullName ?? "",
                        style: AppTextStyle.s14Medium
                            .copyWith(color: AppColors.white, height: 17 / 14),
                      ),
                    )
                  ],
                )
              : LineContentWidget(
                  lable: 'Người nhận', content: simOrder.fullName ?? ""),
          const SizedBox(height: 9),
          LineContentWidget(
              lable: 'Số điện thoại', content: simOrder.userPhoneNumber ?? ''),
          const SizedBox(height: 9),
          Text(
            'Địa chỉ người nhận',
            style: AppTextStyle.s14Regular
                .copyWith(color: AppColors.white, height: 17 / 14),
          ),
          const SizedBox(height: 7),
          Flexible(
              child: Text(
            simOrder.diachi ?? "",
            style: AppTextStyle.s14Medium
                .copyWith(color: AppColors.white, height: 15 / 14),
          )),
          Visibility(
              visible: simType == SimType.eSim,
              child: const SizedBox(height: 9)),
          simType == SimType.eSim
              ? CheckOverFlow.doesTextOverflow(
                      text: simOrder.email ?? 'N/A',
                      style: AppTextStyle.s14Medium
                          .copyWith(color: AppColors.white, height: 17 / 14),
                      maxWidth: maxWidth)
                  ? Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          'Email ',
                          style: AppTextStyle.s14Regular.copyWith(
                              color: AppColors.white, height: 17 / 14),
                        ),
                        const SizedBox(height: 5),
                        Flexible(
                          child: Text(
                            simOrder.email ?? 'N/A',
                            style: AppTextStyle.s14Medium.copyWith(
                                color: AppColors.white, height: 17 / 14),
                          ),
                        )
                      ],
                    )
                  : LineContentWidget(
                      lable: 'Email', content: simOrder.email ?? 'N/A')
              : const SizedBox(),
          // ẩn phí giao hàng
          // LineContentWidget(
          //     lable: 'Phí giao hàng',
          //     content:
          //         ConvertMoney.convertVNDMoney(simOrder.phiGiaoHang ?? 0),
          //     isMoney: true),
        ],
      ),
    );
  }
}

class LineContentWidget extends StatelessWidget {
  const LineContentWidget(
      {super.key, required this.content, required this.lable, this.isMoney});
  final String lable;
  final String content;
  final bool? isMoney;
  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Text(
          lable,
          style: AppTextStyle.s14Regular
              .copyWith(color: AppColors.white, height: 17 / 14),
        ),
        Spacer(),
        Align(
          alignment: Alignment.centerRight,
          child: Row(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                content,
                style: AppTextStyle.s14Medium
                    .copyWith(color: AppColors.white, height: 17 / 14),
              ),
              (isMoney ?? false)
                  ? Text(
                      ' đ',
                      style: AppTextStyle.s10Medium
                          .copyWith(color: AppColors.white),
                    )
                  : SizedBox()
            ],
          ),
        )
      ],
    );
  }
}
