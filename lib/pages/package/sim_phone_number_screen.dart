import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:shopping/constants/app_assets_paths.dart';
import 'package:shopping/constants/app_colors.dart';
import 'package:shopping/constants/app_text_style.dart';
import 'package:shopping/loading_screen.dart';
import 'package:shopping/models/home_model.dart';
import 'package:shopping/models/list_sim_response.dart';
import 'package:shopping/pages/package/sim_phone_number_controller.dart';
import 'package:shopping/pages/package/widgets/filter_sim_endraw_widget.dart';
import 'package:shopping/pages/package/widgets/item_sim_widget.dart';

class SimPhoneNumberScreen extends StatelessWidget {
  const SimPhoneNumberScreen({super.key, this.homeData});
  final HomeData? homeData;
  @override
  Widget build(BuildContext context) {
    return GetBuilder<SimPhoneNumberController>(
        init: SimPhoneNumberController(homeData: homeData),
        builder: (controller) => Scaffold(
            backgroundColor: AppColors.white,
            appBar: AppBar(
              leading: GestureDetector(
                child: Container(
                  width: 20,
                  height: 20,
                  color: Colors.transparent,
                  child: SvgPicture.asset(SvgPath.svgArrowBack,
                      colorFilter: ColorFilter.mode(AppColors.black, BlendMode.srcIn), fit: BoxFit.scaleDown),
                ),
                onTap: () async {
                  Navigator.pop(context);
                },
              ),
              titleSpacing: 0,
              backgroundColor: AppColors.white,
              elevation: 5,
              shadowColor: AppColors.black.withValues(alpha: 0.4),
              surfaceTintColor: AppColors.white,
              centerTitle: true,
              title: Text(
                homeData?.name ?? 'Sim Bundle',
                style: AppTextStyle.s16Bold
                    .copyWith(height: 20 / 16, color: AppColors.black),
              ),
              actions: [SizedBox()],
            ),
            endDrawer: FilterSimEndrawWidget(controller: controller),
            endDrawerEnableOpenDragGesture: false,
            onEndDrawerChanged: (isOpened) {
              controller.onInitDataToOpenFilter();
            },
            body: Obx(() => (controller.messErr.value.isNotEmpty &&
                    controller.isLoading.value == false)
                ? Center(child: Text(controller.messErr.value))
                : Stack(
                    children: [
                      Container(
                        color: Colors.transparent,
                        width: double.infinity,
                        height: double.infinity,
                        child: Padding(
                          padding:
                              EdgeInsets.only(left: 16, right: 16, top: 14),
                          child: Column(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Padding(
                                padding: const EdgeInsets.only(bottom: 13),
                                child: Row(
                                  children: [
                                    Flexible(
                                        child: _SearchSimWidget(
                                            controller: controller)),
                                    const SizedBox(width: 19),
                                    Builder(builder: (context) {
                                      return GestureDetector(
                                        onTap: () {
                                          // open endraw
                                          Scaffold.of(context).openEndDrawer();
                                        },
                                        child: Container(
                                          color: Colors.transparent,
                                          child: Padding(
                                            padding: const EdgeInsets.only(
                                                bottom: 4),
                                            child: Row(
                                              mainAxisSize: MainAxisSize.min,
                                              children: [
                                                SvgPicture.asset(
                                                    SvgPath.svgIconFilter),
                                              ],
                                            ),
                                          ),
                                        ),
                                      );
                                    }),
                                  ],
                                ),
                              ),
                              Obx(() => controller.isLoading.value
                                  ? Expanded(
                                      child: Center(child: LoadingWidget()))
                                  : Flexible(
                                      child: GetBuilder<
                                              SimPhoneNumberController>(
                                          id: SimPhoneNumberControllerUpdateKey
                                              .updateListSim,
                                          builder: (controller) => controller
                                                  .listSim.isEmpty
                                              ? Center(
                                                  child:
                                                      Text('Không có kết quả'))
                                              : ListSIMWidget(
                                                  listSim: controller.listSim,
                                                  isShimmer: false,
                                                ))))
                            ],
                          ),
                        ),
                      ),
                      Obx(
                        () => Positioned(
                            left: controller.fabPosition.value.dx,
                            top: controller.fabPosition.value.dy,
                            child: GestureDetector(
                              onTap: () async {
                                await controller.onClickRandonSim(
                                    context: context);
                              },
                              child: Draggable(
                                onDragEnd: (details) {
                                  controller.onDragBtnRandom(details.offset);
                                },
                                feedback: SvgPicture.asset(SvgPath.svgRandom),
                                child: SvgPicture.asset(SvgPath.svgRandom),
                              ),
                            )),
                      )
                    ],
                  ))));
  }
}

class ListSIMWidget extends StatelessWidget {
  const ListSIMWidget(
      {super.key, required this.listSim, this.homeData, this.isShimmer});
  final List<ItemSim> listSim;
  final HomeData? homeData;
  final bool? isShimmer;
  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        LableListSIMWidget(),
        Container(height: 1, color: AppColors.white),
        isShimmer ?? true
            ? ShimmerList()
            : Flexible(
                child: listSim.isEmpty
                    ? Center(
                        child: Padding(
                        padding: const EdgeInsets.only(top: 10),
                        child: Text('Không có dữ liệu để hiển thị'),
                      ))
                    : ListView.separated(
                        physics: ClampingScrollPhysics(),
                        padding: EdgeInsets.zero,
                        itemCount: listSim.length,
                        shrinkWrap: true,
                        itemBuilder: (context, index) => ItemSimWidget(
                          itemSim: listSim[index],
                          homeData: homeData,
                        ),
                        separatorBuilder: (context, index) =>
                            Divider(height: 1, color: AppColors.white),
                      ))
      ],
    );
  }
}

class ListSimView extends StatelessWidget {
  const ListSimView(
      {super.key,
      required this.homeData,
      required this.listSim,
      this.isShimmer});
  final HomeData? homeData;
  final List<ItemSim>? listSim;
  final bool? isShimmer;
  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Expanded(
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  SvgPicture.asset(SvgPath.svgIconSim, width: 28, height: 28),
                  SizedBox(width: 5),
                  Text(
                    homeData?.name ?? '',
                    style: AppTextStyle.s16Bold
                        .copyWith(overflow: TextOverflow.ellipsis),
                  ),
                ],
              ),
            ),
            GestureDetector(
              onTap: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    settings: RouteSettings(name: "/page_list_sim"),
                    builder: (context) =>
                        SimPhoneNumberScreen(homeData: homeData),
                  ),
                );
              },
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Text("Tìm/Chọn số",
                      style: AppTextStyle.s12Medium.copyWith(
                        color: AppColors.black,
                      )),
                  SizedBox(width: 5),
                  SvgPicture.asset(SvgPath.svgWatchMore)
                ],
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        // LableListSIMWidget(),
        ListSIMWidget(
            listSim: listSim ?? [], homeData: homeData, isShimmer: isShimmer),
      ],
    );
  }
}

class _SearchSimWidget extends StatelessWidget {
  const _SearchSimWidget({required this.controller});
  final SimPhoneNumberController controller;
  @override
  Widget build(BuildContext context) {
    return Container(
      height: 36,
      margin: EdgeInsets.only(bottom: 4),
      child: TextField(
          focusNode: controller.focusNode,
          style: AppTextStyle.s14Regular.copyWith(color: AppColors.black2E2E2E),
          maxLength: 200,
          controller: controller.searchController,
          cursorColor: AppColors.black2E2E2E,
          keyboardType: TextInputType.text,
          autofocus: false,
          onEditingComplete: () {
            FocusScope.of(context).unfocus();
            controller.onTapSearch();
          },
          decoration: InputDecoration(
            contentPadding: EdgeInsets.symmetric(vertical: 10, horizontal: 12),
            hintText: 'Nhập số SIM cần tìm',
            hintStyle: AppTextStyle.s13Regular.copyWith(color: AppColors.black),
            filled: true,
            fillColor: AppColors.greyF5F6F9,
            counterText: '',
            border: const UnderlineInputBorder(),
            enabledBorder: OutlineInputBorder(
              borderSide:
                  const BorderSide(width: 1, color: AppColors.greyE5E5E5),
              borderRadius: BorderRadius.circular(30),
            ),
            focusedBorder: OutlineInputBorder(
              borderSide:
                  const BorderSide(width: 1, color: AppColors.greyE5E5E5),
              borderRadius: BorderRadius.circular(30),
            ),
            suffixIcon: Row(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                GestureDetector(
                  behavior: HitTestBehavior.opaque,
                  onTap: () {
                    controller.onTapSearch.call();
                  },
                  child: Padding(
                      padding: const EdgeInsets.all(10),
                      child: SvgPicture.asset(
                        SvgPath.svgIconSearch,
                        colorFilter: ColorFilter.mode(AppColors.black, BlendMode.srcIn)
                      )),
                )
              ],
            ),
          )),
    );
  }
}
