import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:shopping/app_services/app_storage.dart';
import 'package:shopping/constants/app_colors.dart';
import 'package:shopping/models/home_model.dart';
import 'package:shopping/models/list_sim_response.dart';
import 'package:shopping/pages/sim/item_sim_action_controller.dart';

import 'package:shopping/pages/sim/step_pick_product_screen.dart';
import 'package:shopping/pages/sim/widgets/book_sim_otp_widget.dart';
import 'package:shopping/pages/sim/widgets/book_sim_widget.dart';

class ItemSimAction {
  static void openSheetBookSim(
      {required BuildContext context,
      required ItemSim? itemSim,
      HomeData? homeData}) {
    showModalBottomSheet(
        backgroundColor: AppColors.white,
        context: context,
        enableDrag: true,
        shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.vertical(top: Radius.circular(30))),
        isScrollControlled: true,
        builder: (context) => Container(
            padding: MediaQuery.of(context).viewInsets,
            width: double.infinity,
            child: SingleChildScrollView(
                child: BookSimWidget(
              itemSim: itemSim,
              openOTP: (
                  {required int? ordId,
                  required String? phoneNumber,
                  required String? userPhoneNumber}) async {
                Navigator.pop(context);
               await openSheetOTPBookSim(
                    context: context,
                    itemSim: itemSim,
                    homeData: homeData,
                    ordId: ordId,
                    phoneNumber: phoneNumber,
                    userPhoneNumber: userPhoneNumber);
              },
            ))));
  }

  static Future<void> openSheetOTPBookSim(
      {required BuildContext context,
      required ItemSim? itemSim,
      int? ordId,
      String? phoneNumber,
      String? userPhoneNumber,
      HomeData? homeData}) async {
    await showModalBottomSheet(
        backgroundColor: AppColors.white,
        context: context,
        enableDrag: true,
        shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.vertical(top: Radius.circular(30))),
        isScrollControlled: true,
        builder: (context) => Container(
            padding: MediaQuery.of(context).viewInsets,
            width: double.infinity,
            child: SingleChildScrollView(
                child: BookSimOTPWidget(
              ordId: ordId,
              openPickProduct: () {
                Navigator.of(context).pop();
                Navigator.push(
                    context,
                    MaterialPageRoute(
                        builder: (context) => StepPickProductScreen(
                            homeData: homeData,
                            ordId: ordId,
                            phoneNumber: phoneNumber,
                            userPhoneNumber: userPhoneNumber)));
              },
            )))).whenComplete(() {
      if (Get.isRegistered<BookSimOTPController>()) {
        Get.find<BookSimOTPController>().timer?.cancel();
        Get.delete<BookSimOTPController>();
      }
    });
  }
}

class ActionBack {
  static void onBack({required BuildContext context}) {
    bool? _isOpenFromHome;

    _isOpenFromHome = AppStorage.instance.getBool2(SKeysPK.isBookSimFromHome);

    ///////////////
    // if (Get.isRegistered<StepPickProductController>()) {
    //   StepPickProductController _controller =
    //       Get.find<StepPickProductController>();
    //   if (_controller.homeData != null) {
    //     _isOpenFromHome = true;
    //   }
    // }
    //////////////

    // clear value storage
    AppStorage.instance.removeString2(SKeysPK.isBookSimFromHome);

    if (_isOpenFromHome ?? true) {
      Navigator.of(context).popUntil(ModalRoute.withName("/shopping_home"));
    } else {
      Navigator.of(context).popUntil(ModalRoute.withName("/page_list_sim"));
    }
  }
}
