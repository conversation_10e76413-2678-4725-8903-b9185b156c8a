class PhongGiaoDichResponse {
  bool? success;
  int? statusCode;
  int? code;
  PhongGiaoDichData? data;

  PhongGiaoDichResponse({
    this.success,
    this.statusCode,
    this.code,
    this.data,
  });

  factory PhongGiaoDichResponse.fromJson(Map<String, dynamic> json) => PhongGiaoDichResponse(
        success: json["success"],
        statusCode: json["statusCode"],
        code: json["code"],
        data: PhongGiaoDichData.fromJson(json["data"]),
      );
}

class PhongGiaoDichData {
  ListPhongGiaoDich data;

  PhongGiaoDichData({
    required this.data,
  });

  factory PhongGiaoDichData.fromJson(Map<String, dynamic> json) => PhongGiaoDichData(
        data: ListPhongGiaoDich.fromJson(json["data"]),
      );
}

class ListPhongGiaoDich {
  List<PhongGiaoDich> items;

  ListPhongGiaoDich({
    required this.items,
  });

  factory ListPhongGiaoDich.fromJson(Map<String, dynamic> json) => ListPhongGiaoDich(
        items: json["items"] == null ? [] : List<PhongGiaoDich>.from(json["items"].map((x) => PhongGiaoDich.fromJson(x))),
      );
}

class PhongGiaoDich {
  int idPgd;
  String? name;
  String address;

  PhongGiaoDich({
    required this.idPgd,
    required this.name,
    required this.address,
  });

  factory PhongGiaoDich.fromJson(Map<String, dynamic> json) => PhongGiaoDich(
        idPgd: json["id_pgd"],
        name: json["name"],
        address: json["address"],
      );
}
