import 'dart:io';
import 'package:dio/dio.dart';
import 'package:dio/io.dart';
import 'package:dio/src/response.dart' as prefix_Res;
import 'package:flutter/foundation.dart';
import 'package:shopping/app_services/logger.dart';
import 'package:shopping/shopping.dart';



var SERVER_URL_DEV = "https://api-muasam-app.vivas.vn/v1/";
var SERVER_URL_STG = "https://api-muasam-stag-app.vivas.vn/v1/";
var SERVER_URL_PRO = "https://api-givenet-app.vivas.vn/v1/";

String RESPONSE_CODE = "code";
String RESPONSE_MSG = "message";
String RESPONSE_DATA = "data";

int CODE_SUCCESS = 0;
int CODE_NO_INTERNET = 100;
int CODE_ERROR = 102;
int CODE_RESPONSE_NULL = 103;

class ApiService {
  RunMode runMode = RunMode.dev;
  late Dio _dio;
  late String ApiKey2;
  late int profileIdTCCT;
  String jwt_token2 = "";
  init(RunMode _RunMode, String _ApiKey, int _profileId) async {
    this.runMode = _RunMode;
    this.ApiKey2 = _ApiKey;
    this.profileIdTCCT = _profileId;
    BaseOptions options = new BaseOptions(
        baseUrl: (runMode == RunMode.dev ? SERVER_URL_DEV : (runMode == RunMode.stag ? SERVER_URL_STG : SERVER_URL_PRO)),
        headers: getHeader2(),
        receiveDataWhenStatusError: true,
        connectTimeout: Duration(seconds: 300), // 30 seconds
        receiveTimeout: Duration(seconds: 300) // 30 seconds

        );
    _dio = Dio(options);

     /// ADD Custom LOG
    _dio.interceptors.add(CustomLogInterceptor());
    // customization
    _dio.interceptors.add(PrettyDioLogger(
        requestHeader: true,
        requestBody: true,
        responseBody: true,
        responseHeader: false,
        error: true,
        compact: true,
        maxWidth: 90));

    /// Don't trust any certificate just because their root cert is trusted.
    if (!kIsWeb) {
      _dio.httpClientAdapter = IOHttpClientAdapter(
        createHttpClient: () {
          final HttpClient client = HttpClient(context: SecurityContext(withTrustedRoots: false));
          client.badCertificateCallback = ((X509Certificate cert, String host, int port) => true);
          return client;
        },
      );
    }
  }

  ApiService._privateConstructor();

  static final ApiService instance = ApiService._privateConstructor();

  void setToken(String token) {
    jwt_token2 = token;
  }

  Map<String, dynamic> getHeader2() {
    if (jwt_token2.length != 0) {
      return {
        'Content-Type': 'application/json; charset=UTF-8',
        "X-Api-Key": this.ApiKey2,
        "Authorization": "Bearer ${jwt_token2}",
        'PROFILE_ID': this.profileIdTCCT,
      };
    } else {
      return {
        'Content-Type': 'application/json; charset=UTF-8',
        "X-Api-Key": this.ApiKey2,
        'PROFILE_ID': this.profileIdTCCT,
      };
    }
  }

  Future<prefix_Res.Response> get2({
    required String url,
    Map<String, dynamic>? queryParameters,
    Options? options,
  }) async {
    var result = await _dio.get(
      url,
      queryParameters: queryParameters,
      options: options,
    );

    return result;
  }

  Future<prefix_Res.Response> post2({
    required String url,
    data,
    Map<String, dynamic>? queryParameters,
    Options? options,
  }) async {
    var result = await _dio.post(
      url,
      data: data,
      queryParameters: queryParameters,
      options: options,
    );
    return result;
  }

  Future<prefix_Res.Response> put2({
    required String url,
    data,
    Map<String, dynamic>? queryParameters,
    Options? options,
  }) async {
    var result = await _dio.put(
      url,
      data: data,
      queryParameters: queryParameters,
      options: options,
    );
    return result;
  }

  String handleError2(dynamic error) {
    //update message error
    String errorDescription = "Lỗi hệ thống. Vui lòng thử lại.";
    return errorDescription;
  }

  getFormattedError() {
    return {'error': 'Error'};
  }

  getNetworkError() {
    return "No Internet Connection.";
  }
}

class CustomLogInterceptor extends Interceptor {
  CustomLogInterceptor();
  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) {
    options.extra["start"] = DateTime.now().millisecondsSinceEpoch;
    return super.onRequest(options, handler);
  }

  @override
  void onResponse(prefix_Res.Response response, ResponseInterceptorHandler handler) {
    var start = response.requestOptions.extra["start"] as int;
    var end = DateTime.now().millisecondsSinceEpoch;
    response.extra["duration"] = end - start;
    super.onResponse(response, handler);
  }

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) {
    return super.onError(err, handler);
  }
}

class BaseRepo<T> {
  // GET API
  Future<T> get2({
    required String apiURL,
    Map<String, dynamic>? queryParameters,
    Options? options,
  }) async {
    try {
      final response = await ApiService.instance.get2(
        url: apiURL,
        queryParameters: queryParameters,
        options: options,
      );
      return response.data;
    } catch (e) {
      return {
        RESPONSE_CODE: CODE_ERROR,
        RESPONSE_MSG: ApiService.instance.handleError2(e),
      } as T;
    }
  }

  Future<T> post2({
    required String apiURL,
    data,
    Map<String, dynamic>? queryParameters,
    Options? options,
  }) async {
    try {
      final response = await ApiService.instance.post2(
        url: apiURL,
        data: data,
        queryParameters: queryParameters,
        options: options,
      );
      return response.data;
    } catch (e) {
      return {
        RESPONSE_CODE: CODE_ERROR,
        RESPONSE_MSG: ApiService.instance.handleError2(e),
      } as T;
    }
  }

  Future<T> put2({
    required String apiURL,
    data,
    Map<String, dynamic>? queryParameters,
    Options? options,
  }) async {
    try {
      final response = await ApiService.instance.put2(
        url: apiURL,
        data: data,
        queryParameters: queryParameters,
        options: options,
      );
      return response.data;
    } catch (e) {
      return {
        RESPONSE_CODE: CODE_ERROR,
        RESPONSE_MSG: ApiService.instance.handleError2(e),
      } as T;
    }
  }
}
