import 'package:flutter/cupertino.dart';
import 'package:shopping/constants/app_colors.dart';
import 'package:shopping/constants/app_text_style.dart';

class MultiButton extends StatelessWidget {
  const MultiButton({
    super.key,
    this.onTapLeft,
    this.onTapRight,
    this.isDisableRightBtn,
    required this.textLeft,
    required this.textRight,
  });
  final VoidCallback? onTapLeft;
  final VoidCallback? onTapRight;

  final String textLeft;
  final String textRight;
  final bool? isDisableRightBtn;
  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        _buildButton(
          onTap: () {
            onTapLeft?.call();
          },
          title: textLeft,
          isContinues: false,
        ),
        SizedBox(
          width: 10,
        ),
        _buildButton(
          onTap: () {
            onTapRight?.call();
          },
          title: textRight,
          isDisableRightBtn: isDisableRightBtn ?? false,
        ),
      ],
    );
  }

  Widget _buildButton({
    required VoidCallback onTap,
    required String title,
    bool isContinues = true,
    bool isDisableRightBtn = false,
  }) {
    return Expanded(
      child: GestureDetector(
        onTap: () {
          onTap();
        },
        child: Container(
          // width: 150,
          height: 45,
          decoration: BoxDecoration(
            color: isContinues
                ? (isDisableRightBtn
                    ? AppColors.blue30AAB7.withValues(alpha: 0.5)
                    : AppColors.blue30AAB7)
                : AppColors.greyCACACA,
            borderRadius: BorderRadius.circular(8),
          ),
          child: Center(
            child: Text(
              title,
              style: AppTextStyle.s16Medium.copyWith(
                color: isContinues ? AppColors.white : AppColors.black,
                height: 17 / 12,
              ),
            ),
          ),
        ),
      ),
    );
  }
}
