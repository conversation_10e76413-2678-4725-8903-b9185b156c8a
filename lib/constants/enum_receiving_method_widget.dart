enum ReceivingMethodByType { specificLocation, tradingLocation }

extension ReceivingMethodByExt on ReceivingMethodByType {
  String get nameLocation {
    switch (this) {
      case ReceivingMethodByType.specificLocation:
        return 'Nhận tại địa chỉ yêu cầu';
      case ReceivingMethodByType.tradingLocation:
        return 'Nhận tại địa điểm giao dịch';
    }
  }

  int get getValue {
    switch (this) {
      case ReceivingMethodByType.specificLocation:
        return 1;
      case ReceivingMethodByType.tradingLocation:
        return 2;
    }
  }
}
