import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:shopping/constants/app_assets_paths.dart';
import 'package:shopping/constants/app_colors.dart';
import 'package:shopping/constants/app_text_style.dart';
import 'package:shopping/constants/enum_package_type.dart';
import 'package:shopping/models/home_model.dart';
import 'package:shopping/pages/package/package_screen.dart';
import 'package:shopping/tabs/home/<USER>/item_package_action.dart';
import 'package:shopping/utils/check_overflow_text.dart';

import '../../../models/product.dart';

class HotDealWidget extends StatelessWidget {
  const HotDealWidget({super.key, required this.homeData});
  final HomeData homeData;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Expanded(
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  Image.network(
                    homeData.icon ?? '',
                    width: 28,
                    height: 28,
                  ),
                  // SvgPicture.asset(width: 20, height: 28, SvgPath.svgIconHotDeal),
                  SizedBox(width: 5),
                  Text(
                    homeData.name ?? '',
                    style: AppTextStyle.s16Bold.copyWith(overflow: TextOverflow.ellipsis),
                  )
                ],
              ),
            ),
            GestureDetector(
              onTap: () {
                Navigator.push(
                    context,
                    MaterialPageRoute(
                        builder: (context) => PackageScreen(
                              packageType: PackageType.unknown,
                              homeData: homeData,
                            )));
              },
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Text("Xem thêm",
                      style: AppTextStyle.s12Medium.copyWith(
                        color: AppColors.black,
                      )),
                  SizedBox(
                    width: 5,
                  ),
                  SvgPicture.asset(SvgPath.svgWatchMore)
                ],
              ),
            )
          ],
        ),
        const SizedBox(height: 16),
        SizedBox(
          width: MediaQuery.of(context).size.width,
          height: 140,
          child: OverflowBox(
            maxWidth: MediaQuery.of(context).size.width,
            child: SizedBox(
              height: 140,
              child: ListView.separated(
                physics: const ClampingScrollPhysics(),
                scrollDirection: Axis.horizontal,
                shrinkWrap: true,
                itemCount: homeData.contents?.length ?? 0,
                itemBuilder: (_, index) {
                  return GestureDetector(
                      onTap: () {
                        ItemPackageAction.openDetail(context, homeData.contents![index].product!);
                      },
                      child: Container(
                          padding: EdgeInsets.only(left: 15, right: index == (homeData.contents?.length ?? 0) - 1 ? 15 : 0),
                          child: ItemHotDealWidget(
                            openBuy: () {
                              ItemPackageAction.openSheetBuy(context, homeData.contents![index].product!);
                            },
                            product: homeData.contents?[index].product,
                          )));
                },
                separatorBuilder: (c, i) => Container(width: 0),
              ),
            ),
          ),
        ),
      ],
    );
  }
}

class ItemHotDealWidget extends StatelessWidget {
  const ItemHotDealWidget({
    super.key,
    required this.openBuy,
    this.product,
  });
  final Function() openBuy;
  final Product? product;
  @override
  Widget build(BuildContext context) {
    return ClipRRect(
      borderRadius: BorderRadius.circular(14),
      child: Stack(
        children: [
          SvgPicture.asset(SvgPath.svgHotDealBackGround),
          Container(
            width: 270,
            height: 140,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(14),
              border: Border.all(color: AppColors.pinkFFE2DB, width: 1),
            ),
            child: _ItemPackageInfor(onBuy: openBuy, product: product),
          )
        ],
      ),
    );
  }
}

class _ItemPackageInfor extends StatelessWidget {
  const _ItemPackageInfor({
    required this.onBuy,
    this.product,
  });
  final Function() onBuy;
  final Product? product;
  @override
  Widget build(BuildContext context) {
    return Container(
      height: 140,
      padding: EdgeInsets.symmetric(vertical: 10, horizontal: 10),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            height: 18,
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Flexible(
                  child: Text(
                    maxLines: 1,
                    softWrap: false,
                    overflow: TextOverflow.clip,
                    product?.getContent(1) ?? '',
                    style: AppTextStyle.s15Medium.copyWith(color: AppColors.black, overflow: TextOverflow.clip),
                  ),
                ),
                Visibility(
                    visible: product?.isPriceContent(1) ?? false,
                    // visible: true,
                    child: Text(
                      'đ',
                      style: AppTextStyle.s10Bold.copyWith(
                        color: AppColors.black,
                      ),
                      overflow: TextOverflow.ellipsis,
                    ))
              ],
            ),
          ),
          SizedBox(height: 3),
          SizedBox(
            height: 18,
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Flexible(
                  child: Text(
                    maxLines: 1,
                    softWrap: false,
                    overflow: TextOverflow.clip,
                    product?.getContent(2) ?? '',
                    style: AppTextStyle.s15SemiBold.copyWith(color: AppColors.black),
                  ),
                ),
                Visibility(
                    visible: product?.isPriceContent(2) ?? false,
                    // visible: true,
                    child: Text(
                      'đ',
                      style: AppTextStyle.s10Bold.copyWith(
                        color: AppColors.black,
                      ),
                      overflow: TextOverflow.ellipsis,
                    ))
              ],
            ),
          ),
          SizedBox(height: 5),
          Container(
            height: 43,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Expanded(
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Flexible(
                        child: _ItemPackageLineDetail(
                            // check có hiển thị icon hay không?
                            iconPath: (product?.isShowIconWithPosition(3) ?? false) ? SvgPath.svgIconCheck : '',
                            infor: product?.getContent(3) ?? ''),
                      ),
                      Visibility(
                          visible: product?.isPriceContent(3) ?? false,
                          // visible: true,
                          child: Text(
                            'đ',
                            style: AppTextStyle.s8.copyWith(
                              color: AppColors.black,
                            ),
                            overflow: TextOverflow.ellipsis,
                          ))
                    ],
                  ),
                ),
                Expanded(
                  child: Row(
                    children: [
                      // check có hiển thị icon hay không?
                      (product?.isShowIconWithPosition(4) ?? false) ? SvgPicture.asset(SvgPath.svgIconCheck) : SizedBox(),
                      const SizedBox(width: 9),
                      Flexible(
                        child: LayoutBuilder(builder: (context, constraints) {
                          double maxWidth = constraints.maxWidth;
                          String content4 = product?.getContent(4) ?? '';
                          if (content4.isNotEmpty && !(product?.isPriceContent(4) ?? false)) {
                            content4 = '$content4 ';
                          }
                          return Container(
                            width: maxWidth,
                            child: CheckOverFlow.doesTextOverflow(text: "$content4 đ", style: AppTextStyle.s13Regular, maxWidth: maxWidth)
                                ? Text(
                                    content4,
                                    maxLines: 1,
                                    softWrap: false,
                                    overflow: TextOverflow.clip,
                                    style: AppTextStyle.s13Regular.copyWith(color: AppColors.black, height: 20 / 11, overflow: TextOverflow.clip),
                                  )
                                : Row(
                                    children: [
                                      Row(
                                        mainAxisSize: MainAxisSize.min,
                                        crossAxisAlignment: CrossAxisAlignment.start,
                                        children: [
                                          Text(
                                            content4,
                                            maxLines: 1,
                                            softWrap: false,
                                            overflow: TextOverflow.clip,
                                            style: AppTextStyle.s13Regular.copyWith(color: AppColors.black, height: 20 / 11, overflow: TextOverflow.clip),
                                          ),
                                          Visibility(
                                              visible: product?.isPriceContent(4) ?? false,
                                              // visible: true,
                                              child: Text(
                                                'đ ',
                                                style: AppTextStyle.s8.copyWith(color: AppColors.black),
                                                overflow: TextOverflow.ellipsis,
                                              )),
                                        ],
                                      ),
                                    ],
                                  ),
                          );
                        }),
                      )
                    ],
                  ),
                ),
              ],
            ),
          ),
          Container(height: 4),
          Container(
            height: 27,
            width: Get.width,
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Visibility(
                    visible: product?.getListLogo!.isNotEmpty ?? false,
                    child: Flexible(child: LayoutBuilder(
                      builder: (context, constraints) {
                        double maxWidth = constraints.maxWidth;
                        String content4 = "Miễn phí";

                        double textWidth =
                            CheckOverFlow.textWidth(text: "$content4", style: AppTextStyle.s13Regular.copyWith(color: AppColors.black, height: 20 / 11, overflow: TextOverflow.ellipsis));
                        return Container(
                          height: 27,
                          width: Get.width,
                          child: Row(
                            children: [
                              Text("Miễn phí",
                                  style: TextStyle(
                                    fontSize: 10,
                                    fontWeight: FontWeight.w500,
                                    color: AppColors.black,
                                  )),
                              SizedBox(width: 5),
                              product?.getListLogo == null
                                  ? SizedBox()
                                  : ListView.builder(
                                      shrinkWrap: true,
                                      physics: NeverScrollableScrollPhysics(),
                                      scrollDirection: Axis.horizontal,
                                      itemCount: CheckOverFlow.calculateNumIcon(maxWidth: maxWidth, textWidth: textWidth, listIconLength: product?.getListLogo?.length ?? 0, iconSize: 23),
                                      itemBuilder: (context, index) => Padding(
                                        padding: EdgeInsets.only(left: index == 0 ? 0 : 3),
                                        child: Image.network(
                                          product?.getListLogo?[index] ?? '',
                                          width: 20,
                                          height: 20,
                                        ),
                                      ),
                                    ),
                            ],
                          ),
                        );
                      },
                    ))),
                SizedBox(width: 5),
                GestureDetector(
                  onTap: onBuy,
                  child: Container(
                    decoration: BoxDecoration(color: AppColors.redCE3722, borderRadius: BorderRadius.circular(30)),
                    padding: EdgeInsets.symmetric(vertical: 5, horizontal: 19),
                    child: Center(
                      child: Text('ĐĂNG KÝ', style: AppTextStyle.s13SemiBold.copyWith(color: AppColors.whiteFFFFFF, height: 17 / 12)),
                    ),
                  ),
                )
              ],
            ),
          ),
          // Expanded(child: SizedBox.shrink(), flex: 10),
          // Expanded(
          //   flex: 20,
          //   child: Row(
          //     crossAxisAlignment: CrossAxisAlignment.start,
          //     children: [
          //       Flexible(
          //         child: Text(
          //           product?.getContent(1) ?? '',
          //           style: AppTextStyle.s16Bold.copyWith(
          //               color: AppColors.whiteFFFFFF,
          //               overflow: TextOverflow.ellipsis),
          //         ),
          //       ),
          //       Visibility(
          //           visible: product?.isPriceContent(1) ?? false,
          //           // visible: true,
          //           child: Text(
          //             'đ',
          //             style: AppTextStyle.s10Bold.copyWith(
          //                 color: AppColors.white,
          //                 decorationColor: AppColors.white,
          //                 decoration: TextDecoration.underline),
          //             overflow: TextOverflow.ellipsis,
          //           ))
          //     ],
          //   ),
          // ),
          // Expanded(child: SizedBox.shrink(), flex: 5),
          // Expanded(
          //   flex: 25,
          //   child: Row(
          //     crossAxisAlignment: CrossAxisAlignment.start,
          //     children: [
          //       Flexible(
          //         child: Text(
          //           product?.getContent(2) ?? '',
          //           style: AppTextStyle.s20Bold
          //               .copyWith(color: AppColors.whiteFFFFFF),
          //           overflow: TextOverflow.ellipsis,
          //         ),
          //       ),
          //       Visibility(
          //           visible: product?.isPriceContent(2) ?? false,
          //           // visible: true,
          //           child: Text(
          //             'đ',
          //             style: AppTextStyle.s10Bold.copyWith(
          //                 color: AppColors.white,
          //                 decorationColor: AppColors.white,
          //                 decoration: TextDecoration.underline),
          //             overflow: TextOverflow.ellipsis,
          //           ))
          //     ],
          //   ),
          // ),
          // Expanded(child: SizedBox.shrink(), flex: 5),
          // Expanded(
          //   flex: 20,
          //   child: Row(
          //     crossAxisAlignment: CrossAxisAlignment.start,
          //     children: [
          //       Flexible(
          //         child: _ItemPackageLineDetail(
          //             iconPath: SvgPath.svgIconTick,
          //             infor: product?.getContent(3) ?? ''),
          //       ),
          //       Visibility(
          //           visible: product?.isPriceContent(3) ?? false,
          //           // visible: true,
          //           child: Text(
          //             'đ',
          //             style: AppTextStyle.s8.copyWith(
          //                 color: AppColors.white,
          //                 decorationColor: AppColors.white,
          //                 decoration: TextDecoration.underline),
          //             overflow: TextOverflow.ellipsis,
          //           ))
          //     ],
          //   ),
          // ),
          // Expanded(child: SizedBox.shrink(), flex: 10),
          // Expanded(
          //   flex: 31,
          //   child: Row(
          //     children: [
          //       Expanded(
          //         flex: 166,
          //         child: LayoutBuilder(
          //           builder: (context, constraints) {
          //             double maxWidth = constraints.maxWidth;
          //             String content4 = 'text dài dài';
          //             double textWidth = CheckOverFlow.textWidth(
          //                 text: content4,
          //                 style: AppTextStyle.s13Medium.copyWith(
          //                     color: AppColors.whiteFFFFFF,
          //                     height: 20 / 11,
          //                     overflow: TextOverflow.ellipsis));
          //             return Container(
          //                 width: maxWidth,
          //                 child: CheckOverFlow.doesTextOverflow(
          //                         text: content4,
          //                         style: AppTextStyle.s13Medium,
          //                         maxWidth: maxWidth)
          //                     ? Text(
          //                         content4,
          //                         style: AppTextStyle.s13Medium.copyWith(
          //                             color: AppColors.whiteFFFFFF,
          //                             height: 20 / 11,
          //                             overflow: TextOverflow.ellipsis),
          //                       )
          //                     : Row(
          //                         children: [
          //                           Text(
          //                             content4,
          //                             style: AppTextStyle.s13Medium.copyWith(
          //                                 color: AppColors.whiteFFFFFF,
          //                                 height: 20 / 11,
          //                                 overflow: TextOverflow.ellipsis),
          //                           ),
          //                           SizedBox(width: 2),
          //                           product?.getListLogo == null
          //                               ? SizedBox()
          //                               : ListView.builder(
          //                                   shrinkWrap: true,
          //                                   physics:
          //                                       NeverScrollableScrollPhysics(),
          //                                   scrollDirection: Axis.horizontal,
          //                                   itemCount:
          //                                       CheckOverFlow.calculateNumIcon(
          //                                           maxWidth: maxWidth,
          //                                           textWidth: textWidth,
          //                                           listIconLength: product
          //                                                   ?.getListLogo
          //                                                   ?.length ??
          //                                               0),
          //                                   itemBuilder: (context, index) =>
          //                                       Padding(
          //                                     padding: EdgeInsets.only(
          //                                         left: index == 0 ? 0 : 4),
          //                                     child: Image.network(
          //                                       product?.getListLogo?[index] ??
          //                                           '',
          //                                       width: 24,
          //                                       height: 24,
          //                                     ),
          //                                   ),
          //                                 ),
          //                         ],
          //                       ));
          //           },
          //         ),
          //       ),
          //       Expanded(child: SizedBox.shrink(), flex: 5),
          //       Expanded(
          //         flex: 91,
          //         child: GestureDetector(
          //           onTap: onBuy,
          //           child: Container(
          //               width: 91,
          //               height: 26,
          //               decoration: BoxDecoration(
          //                   color: AppColors.greyF5F6F9,
          //                   borderRadius: BorderRadius.circular(15)),
          //               child: Center(
          //                 child: Text("ĐĂNG KÝ",
          //                     style: AppTextStyle.s12SemiBold.copyWith(
          //                         color: AppColors.orangeFF6540,
          //                         height: 17 / 12)),
          //               )),
          //         ),
          //       ),
          //     ],
          //   ),
          // ),
          // Expanded(child: SizedBox.shrink(), flex: 5),
        ],
      ),
    );
  }
}

class _ItemPackageLineDetail extends StatelessWidget {
  const _ItemPackageLineDetail({required this.iconPath, required this.infor});
  final String iconPath;
  final String infor;
  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        iconPath.trim().isEmpty ? SizedBox() : SvgPicture.asset(iconPath),
        const SizedBox(width: 9),
        Flexible(
          child: Text(infor, maxLines: 1, softWrap: false, overflow: TextOverflow.clip, style: AppTextStyle.s13Regular.copyWith(color: AppColors.black, height: 20 / 11, overflow: TextOverflow.clip)),
        )
      ],
    );
  }
}
