import 'package:flutter/material.dart';

import '../constants/app_colors.dart';

enum OrderType {
  progress(2),
  success(1),
  fail(10),
  all(-1),
  waitingRefund(3),
  refunded(4),
  complaint(5);

  final int status;
  const OrderType(this.status);

  factory OrderType.fromStatus(int status) {
    switch (status) {
      case 1:
        return OrderType.success;
      case 2:
        return OrderType.progress;
      case 10:
        return OrderType.fail;
      case 3:
        return OrderType.waitingRefund;
      case 4:
        return OrderType.refunded;
      case 5:
        return OrderType.complaint;
      default:
        return OrderType.all;
    }
  }
}

extension OrderTypeExt on OrderType {
  String get nameOrderType {
    switch (this) {
      case OrderType.progress:
        return 'Đang thực hiện';
      case OrderType.success:
        return 'Thành công';
      case OrderType.fail:
        return 'Thất bại';
      case OrderType.waitingRefund:
        return 'Chờ hoàn tiền';
      case OrderType.refunded:
        return 'Đã hoàn tiền';
      case OrderType.complaint:
        return '<PERSON>hiếu nại';
      case OrderType.all:
        return 'Tất cả';
    }
  }

  String get statusName {
    switch (this) {
      case OrderType.progress:
        return '<PERSON>ang thực hiện';
      case OrderType.success:
        return 'Thành công';
      case OrderType.fail:
        return 'Thất bại';
      case OrderType.waitingRefund:
        return 'Chờ hoàn tiền';
      case OrderType.refunded:
        return 'Đã hoàn tiền';
      case OrderType.complaint:
        return 'Khiếu nại';
      case OrderType.all:
        return 'Chưa xác định';
    }
  }

  Color get textColor {
    return Colors.white;
    // switch (this) {
    //   case OrderType.progress:
    //     return AppColors.blue005BF9;
    //   case OrderType.success:
    //     return AppColors.blue005BF9;
    //   case OrderType.fail:
    //     return AppColors.redE60E00;
    //   default:
    //     return Colors.black;
    // }
  }

  Color get statusColor {
    switch (this) {
      case OrderType.progress:
        return AppColors.orangeFF6540;
      case OrderType.success:
        return AppColors.green50CB89;
      case OrderType.fail:
        return AppColors.redE60E00;
      case OrderType.waitingRefund:
        return AppColors.yellowFBBE12;
      case OrderType.refunded:
        return AppColors.blue1877F2;
      case OrderType.complaint:
        return AppColors.red910D11;
      case OrderType.all:
        return AppColors.black;
    }
  }
}
