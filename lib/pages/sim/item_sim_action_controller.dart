import 'dart:async';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:shopping/apis/ShoppingRepository.dart';
import 'package:shopping/loading_screen.dart';
import 'package:shopping/models/list_sim_response.dart';
import 'package:shopping/utils/extension/string_extension.dart';
import 'package:shopping/widgets/app_dialog.dart';

//book SIM sheet
class BookSimController extends GetxController {
  BookSimController({this.itemSim});
  final TextEditingController phoneController = TextEditingController();
  RxBool isLoading = false.obs;
  int? ordId;
  final ItemSim? itemSim;
  Rx<String> messErr = ''.obs;
  @override
  void onInit() {
    super.onInit();
  }

  Future getOTP({required BuildContext context}) async {
    var res = await DelayLoadingCallApi.delayCallApi(ShoppingRepository.instance.postOTPSim(phoneNumber: phoneController.text.trim(), idKhoSim: itemSim?.idKhoSim, numberBook: itemSim?.msisdn));
    messErr.value = res?.message ?? '';
    if (res?.code == 0) {
      messErr.value = '';
      ordId = res?.data?.orderId;
      update([BookSimControllerUpdateKey.updateOrdId]);
    }
  }

  Future onClickRequestOTP({required BuildContext context, required Function({required int? ordId, required String? phoneNumber, required String? userPhoneNumber}) openOTP}) async {
    if (isLoading.value) {
      return;
    }
    isLoading.toggle();
    bool isPhoneNumber = phoneController.text.validatePhoneNumber();
    if (!isPhoneNumber) {
      dialogAsk(context, 'Số điện thoại không hợp lệ');
    } else {
      // rest API
      await getOTP(context: context);
    }
    if (messErr.value.isNotEmpty) {
      dialogAsk(context, messErr.value);
    }
    isLoading.toggle();
    if (messErr.value.isEmpty && ordId != null) {
      openOTP.call(ordId: ordId, phoneNumber: itemSim?.msisdn, userPhoneNumber: phoneController.text.trim());
    }
  }
}

// book SIM OTP sheet
class BookSimOTPController extends GetxController {
  BookSimOTPController({this.ordId});
  int? ordId;
  RxBool resend = true.obs;
  RxBool isCountDown = true.obs;
  final otpController = TextEditingController();
  Rx<int> timeOtp = 180.obs;
  Timer? timer;
  RxBool isLoading = false.obs;
  RxBool isCallAPISuccess = true.obs;
  Rx<String> messErr = ''.obs;
  @override
  void onInit() {
    super.onInit();
    // count down
    startCountTime();
  }

  @override
  void dispose() {
    timer?.cancel();
    super.dispose();
  }

  void startCountTime() {
    const oneSec = const Duration(seconds: 1);
    timer = Timer.periodic(oneSec, (Timer t) {
      if (timeOtp.value == 0) {
        t.cancel();
      } else {
        timeOtp.value--;
      }
    });
  }

  void setResend(bool? r) {
    resend.value = r ?? false;
  }

  bool isCountDownOtp() {
    return timeOtp.value > 0;
  }

  Future resendOTP({required BuildContext context}) async {
    if (ordId == null) {
      return;
    }
    isCallAPISuccess.toggle();
    var res = await ShoppingRepository.instance.postResendOTPSim(ordId: ordId);
    isCallAPISuccess.toggle();
    if (res.code == 0) {
    } else {
      if (res.code == 30026) {
        // disableResend.value = false;
        // disableBtnResend.value = false;
        isCountDown.value = false;
        dialogAsk(context, res.message, isExit: true);
        return;
      }
      dialogAsk(context, res.message);
    }
    update();
  }

  Future onClickBookSim({required BuildContext context, required Function() openPickProduct}) async {
    if (ordId == null || otpController.text.trim().isEmpty || isLoading.value) {
      return;
    }
    isLoading.toggle();
    var res = await ShoppingRepository.instance.postBookSim(ordId: ordId, otp: otpController.text.trim());
    messErr.value = res?.message ?? '';
    isLoading.toggle();
    if (res?.code == 0) {
      messErr.value = '';
      openPickProduct.call();
    }
    if (messErr.value.isNotEmpty) {
      dialogAsk(context, messErr.value);
    }
  }
}

class BookSimControllerUpdateKey {
  static const String updateOrdId = 'book_sim_controller/ordId';
}
