import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:infinite_scroll_pagination/infinite_scroll_pagination.dart';
import 'package:shopping/constants/app_assets_paths.dart';
import 'package:shopping/constants/app_colors.dart';
import 'package:shopping/constants/app_text_style.dart';
import 'package:shopping/constants/enum_package_type.dart';
import 'package:shopping/loading_screen.dart';
import 'package:shopping/models/home_model.dart';
import 'package:shopping/models/product.dart';
import 'package:shopping/pages/insurance/insurance_controller.dart';
import 'package:shopping/pages/insurance/widgets/item_insurance_widget.dart';
import 'package:shopping/pages/package/package_screen.dart';
import 'package:shopping/utils/responsive_utils.dart';

class InsuranceScreen extends StatelessWidget {
  final PackageType? packageType;
  final HomeData? homeData;

  const InsuranceScreen({super.key, this.packageType, this.homeData});
  @override
  Widget build(BuildContext context) {
    final double w = MediaQuery.of(context).size.width;
    final int _cntItem = ResponsiveUtils.isMobile(context)
        ? 2
        : ResponsiveUtils.isTablet(context)
            ? 3
            : 4;
    // itemWidth = (device width - 2 viền - khoảng cách giữa các item)/số lượng item 1 row
    final double _itemWidth = (w - 30 - (_cntItem - 1) * 15) / _cntItem;
    return GetBuilder<InsuranceController>(
      init: InsuranceController(
          packageType: packageType, homeData: homeData, context: context),
      builder: (controller) => LoadingOverlay(
        child: Scaffold(
          appBar: AppBar(
            titleSpacing: 0,
            leading: GestureDetector(
              child: Container(
                width: 20,
                height: 20,
                color: Colors.transparent,
                child: SvgPicture.asset(SvgPath.svgArrowBack,
                    colorFilter: ColorFilter.mode(AppColors.black, BlendMode.srcIn), fit: BoxFit.scaleDown),
              ),
              onTap: () async {
                Navigator.pop(context);
              },
            ),
            backgroundColor: AppColors.white,
            elevation: 5,
            shadowColor: AppColors.black.withValues(alpha: 0.4),
            surfaceTintColor: AppColors.white,
            title: Text(
              "Bảo hiểm".toUpperCase(),
              style:
                  AppTextStyle.s16Bold.copyWith(color: AppColors.black2E2E2E),
            ),
            centerTitle: true,
          ),
          body: ColoredBox(
            color: AppColors.white,
            child: Padding(
                padding: EdgeInsets.symmetric(horizontal: 15, vertical: 13),
                child: GetBuilder<InsuranceController>(
                    id: InsuranceControllerUpdateKey.updateListIns,
                    builder: (controller) => PagedGridView(
                          pagingController: controller.pageController,
                          physics: BouncingScrollPhysics(),
                          gridDelegate:
                              SliverGridDelegateWithFixedCrossAxisCount(
                            crossAxisCount: _cntItem,
                            mainAxisSpacing: 15,
                            crossAxisSpacing: 15,
                            childAspectRatio: _itemWidth / 174,
                          ),
                          builderDelegate: PagedChildBuilderDelegate<Product>(
                            itemBuilder: (c, i, index) {
                              return GestureDetector(
                                onTap: () {},
                                child: SizedBox(
                                  child: ItemInsuranceWidget(
                                    productInsurance: i,
                                    itemWidth: _itemWidth,
                                  ),
                                ),
                              );
                            },
                            noItemsFoundIndicatorBuilder: (context) {
                              return EmptyListWidget(
                                  messageFail: controller.messageFail.value,
                                  isSearchingOrFilter: false);
                            },
                            firstPageProgressIndicatorBuilder: (context) =>
                                SizedBox(),
                            newPageProgressIndicatorBuilder: (context) =>
                                SizedBox(),
                          ),
                        ))),
          ),
        ),
      ),
    );
  }
}
