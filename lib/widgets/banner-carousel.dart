import 'package:carousel_slider/carousel_slider.dart';
import 'package:flutter/material.dart';

class BannerCarousel extends StatefulWidget {
  late final List<String> banners = ["https://images.wallpapersden.com/image/download/purple-sunrise-4k-vaporwave_bGplZmiUmZqaraWkpJRmbmdlrWZlbWU.jpg"];
  BannerCarousel();
  @override
  State<StatefulWidget> createState() {
    return _BannerCarouselState();
  }
}

class _BannerCarouselState extends State<BannerCarousel> {
  int _current = 0;
  final CarouselSliderController _controller = CarouselSliderController();

  @override
  Widget build(BuildContext context) {
    return Stack(alignment: Alignment.bottomCenter, children: [
      CarouselSlider(
        items: imageSliders(context, widget.banners),
        carouselController: _controller,
        options: CarouselOptions(
            autoPlay: false,
            aspectRatio: 3.8,
            enlargeCenterPage: false,
            viewportFraction: 1,
            enableInfiniteScroll: true,
            onPageChanged: (index, reason) {
              setState(() {
                _current = index;
              });
            }),
      ),
      Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: widget.banners.asMap().entries.map((entry) {
          return GestureDetector(
            onTap: () => _controller.animateToPage(entry.key),
            child: Container(
              width: _current == entry.key ? 18 : 6,
              height: 6,
              margin: const EdgeInsets.symmetric(vertical: 15.0, horizontal: 4.0),
              decoration: BoxDecoration(
                  borderRadius: const BorderRadius.all(
                    Radius.circular(5),
                  ),
                  color: (Theme.of(context).brightness == Brightness.dark ? Colors.white : Colors.black).withValues(alpha: _current == entry.key ? 0.9 : 0.4)),
            ),
          );
        }).toList(),
      ),
    ]);
  }

  List<Widget> imageSliders(BuildContext context, List<String> banners) => banners
      .map(
        (item) => Container(
          margin: const EdgeInsets.all(0),
          child: GestureDetector(
            onTap: () async {},
            child: ClipRRect(
              borderRadius: const BorderRadius.all(Radius.circular(8.0)),
              child: Stack(
                children: <Widget>[
                  // CachedNetworkImage(
                  //   imageUrl: 'https://images.wallpapersden.com/image/download/purple-sunrise-4k-vaporwave_bGpl.jpg',
                  //   width: double.infinity,
                  //   height: double.infinity,
                  //   fit: BoxFit.cover,
                  //   errorWidget: (context, url, error) {
                  //     return const ImagePlaceholder();
                  //   },
                  // ),
                  Image.network(
                    'https://images.wallpapersden.com/image/download/purple-sunrise-4k-vaporwave_bGpl.jpg',
                    height: double.infinity,
                    fit: BoxFit.cover,
                    width: double.infinity,
                  ),
                  Positioned(
                    bottom: 0.0,
                    left: 0.0,
                    right: 0.0,
                    child: Container(
                      decoration: const BoxDecoration(
                        gradient: LinearGradient(
                          colors: [Color.fromARGB(200, 0, 0, 0), Color.fromARGB(0, 0, 0, 0)],
                          begin: Alignment.bottomCenter,
                          end: Alignment.topCenter,
                        ),
                      ),
                    ),
                  ),
                  Container(
                    alignment: Alignment.centerLeft,
                    padding: const EdgeInsets.only(left: 10),
                    child: SizedBox(
                      width: MediaQuery.of(context).size.width * 7 / 10,
                      child: Text(
                        '',
                        maxLines: 3,
                        overflow: TextOverflow.ellipsis,
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 13,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      )
      .toList();
}
