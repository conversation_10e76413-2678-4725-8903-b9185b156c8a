import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:shopping/apis/ShoppingRepository.dart';
import 'package:shopping/app_services/api_service.dart';
import 'package:shopping/constants/enum_insurance_type.dart';
import 'package:shopping/enum/enum_ins_moto.dart';
import 'package:shopping/loading_screen.dart';
import 'package:shopping/models/base_response.dart';
import 'package:shopping/models/insurance_create_order_request_form.dart';
import 'package:shopping/models/insurance_filter_response.dart';
import 'package:shopping/models/insurance_get_price_form_request.dart';
import 'package:shopping/models/insurance_get_price_response.dart';
import 'package:shopping/models/product.dart';
import 'package:shopping/pages/insurance/pages/moto/moto_step_confirm_screen.dart';
import 'package:shopping/widgets/app_dialog.dart';

class InsuranceMotoStepProductController extends GetxController {
  InsuranceMotoStepProductController(
      {required this.productIns, required this.context});

  // input
  final Product? productIns;
  final BuildContext context;

  // loading
  RxBool isLoading = false.obs;
  String? errMessage;

  // loai xe
  List<LoaiXe>? listLoaiXe;
  LoaiXe? loaiXeChoosed;

  // thời hạn bảo hiểm
  MotoTimeType motoTimeTypeChoosed = MotoTimeType.one;

  // ngày hiệu lực/ ngày hết hạn
  DateTime timeStart = DateTime.now();
  String startTimeTxt = '';
  String endTimeTxt = '';

  // mã khuyến mại
  List<MaGiamGia>? listMaKM;
  MaGiamGia? maKMChoosed;
  String? errMaKm;

  // gói bảo hiểm mua thêm
  List<SpMuaThem>? listSpMuaThem;
  List<SpMuaThem> listSpMuaThemChoosed = [];
  List<SpMuaThem> listSpMuaThemBatBuoc = [];

  // tiền
  InsuranceGetPriceFormResquest requestGetPriceForm =
      InsuranceGetPriceFormResquest();
  InsuranceGetPriceResponse? insuranceGetPriceResponse;
  RxBool isLoadingMoney = false.obs;
  num total = 0;
  num disscount = 0;
  num totalPay = 0;

  @override
  void onInit() async {
    super.onInit();
    _clearData();
    await _initData();
    updateTimeStart(timeStart);
    await _calculateMoney();
  }

  Future<void> _initData() async {
    if (isLoading.value) {
      return;
    }
    isLoading.value = true;
    BaseResponse<InsuranceFilterResponse> res =
        await DelayLoadingCallApi.delayCallApi(ShoppingRepository.instance
            .getInsFilter(insId: productIns?.id ?? 0));
    if (res.code == CODE_SUCCESS) {
      listLoaiXe = res.data?.data?.loaiXe ?? [];
      listMaKM = res.data?.data?.maGiamGia ?? [];
      listSpMuaThem = res.data?.data?.spMuaThem ?? [];
      errMessage = null;
    } else {
      errMessage = res.message;
      dialogAsk(context, res.message);
    }
    _setup();
    await _calculateMoney();
    update([
      InsuranceMotoStepProductControllerUpdateKey.updateMotoType,
      InsuranceMotoStepProductControllerUpdateKey.updateCouponChoosed,
      InsuranceMotoStepProductControllerUpdateKey.updateBonusChoosed
    ]);
    isLoading.value = false;
  }

  void _clearData() {
    listLoaiXe?.clear();
    listMaKM?.clear();
    listSpMuaThem?.clear();
  }

  void _setup() {
    if ((listLoaiXe ?? []).isEmpty) {
      listLoaiXe?.add(LoaiXe(name: 'Không có dữ liệu'));
    }
    if ((listMaKM ?? []).isEmpty) {
      listMaKM?.add(MaGiamGia(name: 'Không có dữ liệu'));
    }
    loaiXeChoosed = listLoaiXe?.firstOrNull;
    maKMChoosed = listMaKM?.firstOrNull;
    // setup auto check cho gói bảo hiểm bắt buộc
    if ((listSpMuaThem ?? []).isNotEmpty) {
      if (productIns?.getInsType == InsuranceType.moto ||
          productIns?.getInsType == InsuranceType.car) {
        for (SpMuaThem i in listSpMuaThem ?? []) {
          if (i.code == 'BB') {
            listSpMuaThemBatBuoc.add(i);
          }
        }
      } else if (productIns?.getInsType == InsuranceType.land) {
        for (SpMuaThem i in listSpMuaThem ?? []) {
          if (i.code == 'COBAN') {
            listSpMuaThemBatBuoc.add(i);
          }
        }
      }
    }
    listSpMuaThemChoosed.addAll(listSpMuaThemBatBuoc);
  }

  void updateTimeStart(DateTime? newValue) {
    if (newValue == null) {
      return;
    }
    timeStart = newValue;
    startTimeTxt = DateFormat("dd/MM/yyyy").format(timeStart);
    _updateTimeEnd();
  }

  Future<void> _updateTimeEnd() async {
    endTimeTxt = DateFormat("dd/MM/yyyy").format(timeStart
        .add(Duration(days: (365 * motoTimeTypeChoosed.getDurationTime))));
    // endTimeTxt = DateFormat("dd-MM-yyyy").format(DateFormat("dd-MM-yyyy").parse(
    //     '${timeStart.day}-${timeStart.month}-${timeStart.year + motoTimeTypeChoosed.getDurationTime}'));
    await _calculateMoney();
    update([InsuranceMotoStepProductControllerUpdateKey.updateTime]);
  }

  void updateMotoType(LoaiXe newValue) async {
    loaiXeChoosed = newValue;
    update([InsuranceMotoStepProductControllerUpdateKey.updateMotoType]);
    await _calculateMoney();
  }

  void updateMotoTimeType(MotoTimeType newValue) {
    motoTimeTypeChoosed = newValue;
    update([InsuranceMotoStepProductControllerUpdateKey.updateMotoTimeType]);
    _updateTimeEnd();
  }

  void updateCouponChoosed(MaGiamGia newValue) async {
    maKMChoosed = newValue;
    update([InsuranceMotoStepProductControllerUpdateKey.updateCouponChoosed]);
    await _calculateMoney();
  }

  void onSelectInsBonus(SpMuaThem newValue) async {
    // với gói bắt buộc ko được bỏ chọn
    if (listSpMuaThemBatBuoc.contains(newValue)) {
      return;
    }
    // với các gói còn lại
    if (listSpMuaThemChoosed.contains(newValue)) {
      listSpMuaThemChoosed.remove(newValue);
    } else {
      listSpMuaThemChoosed.add(newValue);
    }
    update([InsuranceMotoStepProductControllerUpdateKey.updateBonusChoosed]);
    await _calculateMoney();
  }

  Future<void> _calculateMoney() async {
    requestGetPriceForm = requestGetPriceForm.copyWith(
      loaiBh: productIns?.group,
      loaiXe: loaiXeChoosed?.id,
      thoiHan: motoTimeTypeChoosed.getDurationTime,
      tgBatDau: timeStart.millisecondsSinceEpoch ~/ 1000,
      maKm: maKMChoosed?.id,
      spMuaKem: listSpMuaThemChoosed.map((e) => e.code ?? '').toList(),
    );
    isLoadingMoney.value = true;
    BaseResponse<InsuranceGetPriceResponse> res =
        await DelayLoadingCallApi.delayCallApi(ShoppingRepository.instance
            .postGetPrice(formRequest: requestGetPriceForm));
    if (res.code == CODE_SUCCESS) {
      insuranceGetPriceResponse = res.data;
      total = insuranceGetPriceResponse?.data?.tongTien ?? 0;
      disscount = insuranceGetPriceResponse?.data?.giam ?? 0;
      totalPay = insuranceGetPriceResponse?.data?.thanhToan ?? 0;
      errMessage = null;
      _validateMaKM();
    } else {
      errMessage = res.message;
      dialogAsk(context, res.message);
    }
    update([InsuranceMotoStepProductControllerUpdateKey.updateMoney]);
    isLoadingMoney.value = false;
  }

  void _validateMaKM() {
    // update tbao lỗi mã gg
    if (disscount == 0 && maKMChoosed != null) {
      errMaKm = 'Sản phẩm không áp dụng mã giảm giá này';
    } else {
      errMaKm = null;
    }
    update([InsuranceMotoStepProductControllerUpdateKey.updateCouponChoosed]);
  }

  void onClickBtn() {
    // case đang load tiền
    if (isLoadingMoney.value == true) {
      return;
    }
    if (errMessage != null) {
      // case có tbao lỗi
      dialogAsk(context, errMessage ?? 'N/A');
    } else {
      // happy case
      InsuranceCreateOrdRequestForm insCreateOrdForm =
          InsuranceCreateOrdRequestForm(
        productId: productIns?.id,
        loaiBh: requestGetPriceForm.loaiBh,
        loaiXe: requestGetPriceForm.loaiXe,
        thoiHan: requestGetPriceForm.thoiHan,
        tgBatDau: requestGetPriceForm.tgBatDau,
        mdSuDung: requestGetPriceForm.mdSuDung,
        soChoNgoi: requestGetPriceForm.soChoNgoi,
        trongTai: requestGetPriceForm.trongTai,
        namDaSd: requestGetPriceForm.namDaSd,
        gtriNgoiNha: requestGetPriceForm.gtriNgoiNha,
        maKm: requestGetPriceForm.maKm,
        ddDk: insuranceGetPriceResponse?.data?.dataDk,
      );
      Navigator.push(
          context,
          MaterialPageRoute(
              builder: (_) => MotoStepConfirmScreen(
                  insuranceCreateOrdRequestForm: insCreateOrdForm)));
    }
  }
}

class InsuranceMotoStepProductControllerUpdateKey {
  static const String updateMotoType = '/update_moto_type';
  static const String updateTime = '/update_time';
  static const String updateMotoTimeType = '/update_moto_time_type';
  static const String updateCouponChoosed = '/update_coupon_choosed';
  static const String updateBonusChoosed = '/update_bonus_choosed';
  static const String updateMoney = '/update_money';
}
