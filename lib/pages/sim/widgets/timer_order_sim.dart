import 'dart:async';

import 'package:flutter/material.dart';
import 'package:shopping/constants/app_text_style.dart';

import '../../../constants/app_colors.dart';
import '../../../utils/convert/time_util.dart';

class TimerOrderCount extends StatefulWidget {
  const TimerOrderCount({
    super.key,
  });

  @override
  State<TimerOrderCount> createState() => _TimerOrderCountState();
}

class _TimerOrderCountState extends State<TimerOrderCount> {
  Timer? timer;
  int count = 1200;
  @override
  void initState() {
    super.initState();
    Future.delayed(Duration(milliseconds: 500), () {
      Timer.periodic(const Duration(seconds: 1), (t) {
        if (timer == null) {
          timer = t;
        }
        if (count == 0) {
          //  t.cancel();
        } else {
          setState(() {
            count -= 1;
            //print(count);
          });
        }
      });
    });
  }

  @override
  void didUpdateWidget(mode) {
    super.didUpdateWidget(mode);
    count = 1200;
  }

  @override
  void dispose() {
    super.dispose();
    timer?.cancel();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Container(
          width: double.infinity,
          padding: EdgeInsets.all(5),
          decoration: BoxDecoration(
            color: AppColors.redF5D7D3,
            borderRadius: BorderRadius.circular(8),
          ),
          child: Center(
            child: RichText(
              textAlign: TextAlign.center,
              text: TextSpan(
                  text: 'Vui lòng điền thông tin và thanh toán trong \n vòng ',
                  style: AppTextStyle.s13Medium.copyWith(
                    color: AppColors.black2E2E2E,
                  ),
                  children: <TextSpan>[
                    TextSpan(
                      text: TimeUtil.formatTime(time: count),
                      style: AppTextStyle.s13Bold.copyWith(
                        color: AppColors.redCE3722,
                      ),
                    ),
                  ]),
            ),
          ),
        ),
      ],
    );
  }
}
