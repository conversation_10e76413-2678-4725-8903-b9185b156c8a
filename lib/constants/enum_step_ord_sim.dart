enum StepStatus { done, process, next }

enum StepOrdSimStatus { pickSim, pickProduct, order, pay }

extension StepOrdSimStatusExt on StepOrdSimStatus {
  String get getName {
    switch (this) {
      case StepOrdSimStatus.pickSim:
        return '<PERSON>ọn số';
      case StepOrdSimStatus.pickProduct:
        return 'Chọn gói cước';
      case StepOrdSimStatus.order:
        return 'Đặt mua';
      case StepOrdSimStatus.pay:
        return 'Thanh toán';
    }
  }
}
