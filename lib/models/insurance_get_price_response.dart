class InsuranceGetPriceResponse {
  Data? data;

  InsuranceGetPriceResponse({
    this.data,
  });

  factory InsuranceGetPriceResponse.fromJson(Map<String, dynamic> json) =>
      InsuranceGetPriceResponse(
        data: json["data"] == null ? null : Data.fromJson(json["data"]),
      );

  Map<String, dynamic> toJson() => {
        "data": data?.toJson(),
      };
}

class Data {
  num? tongTien;
  num? giam;
  num? thanhToan;
  List<DataDk>? dataDk;

  Data({
    this.tongTien,
    this.giam,
    this.thanhToan,
    this.dataDk,
  });

  factory Data.fromJson(Map<String, dynamic> json) => Data(
        tongTien: json["tong_tien"],
        giam: json["giam"],
        thanhToan: json["thanh_toan"],
        dataDk: json["DATA_DK"] == null
            ? []
            : List<DataDk>.from(
                json["DATA_DK"]!.map((x) => DataDk.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "tong_tien": tongTien,
        "giam": giam,
        "thanh_toan": thanhToan,
        "DATA_DK": dataDk == null
            ? []
            : List<dynamic>.from(dataDk!.map((x) => x.toJson())),
      };
}

class DataDk {
  String? maBh;
  num? tien;
  num? phi;
  num? giam;
  num? phiAp;
  num? thue;
  num? ttoan;

  DataDk({
    this.maBh,
    this.tien,
    this.phi,
    this.giam,
    this.phiAp,
    this.thue,
    this.ttoan,
  });

  factory DataDk.fromJson(Map<String, dynamic> json) => DataDk(
        maBh: json["MA_BH"],
        tien: json["TIEN"],
        phi: json["PHI"],
        giam: json["GIAM"],
        phiAp: json["PHI_AP"],
        thue: json["THUE"],
        ttoan: json["TTOAN"],
      );

  Map<String, dynamic> toJson() => {
        "MA_BH": maBh,
        "TIEN": tien,
        "PHI": phi,
        "GIAM": giam,
        "PHI_AP": phiAp,
        "THUE": thue,
        "TTOAN": ttoan,
      };
}
