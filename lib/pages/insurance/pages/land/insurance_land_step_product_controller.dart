import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:shopping/apis/ShoppingRepository.dart';
import 'package:shopping/app_services/api_service.dart';
import 'package:shopping/constants/enum_insurance_type.dart';
import 'package:shopping/enum/enum_ins_land.dart';
import 'package:shopping/loading_screen.dart';
import 'package:shopping/models/base_response.dart';
import 'package:shopping/models/insurance_create_order_request_form.dart';
import 'package:shopping/models/insurance_filter_response.dart';
import 'package:shopping/models/insurance_get_price_form_request.dart';
import 'package:shopping/models/insurance_get_price_response.dart';
import 'package:shopping/models/product.dart';
import 'package:shopping/pages/insurance/pages/land/land_step_confirm_screen.dart';

import 'package:shopping/widgets/app_dialog.dart';

class InsuranceLandStepProductController extends GetxController {
  InsuranceLandStepProductController(
      {required this.productIns, required this.context});

  // input
  final Product? productIns;
  final BuildContext context;

  // loading
  RxBool isLoading = false.obs;
  String? errMessage;

  // loai xe
  List<LoaiXe>? listLoaiXe;
  LoaiXe? loaiXeChoosed;

  // số năm sử dụng
  int? yearUsed;
  String? yearUsedTxt;
  RxBool isValidateYearUsed = false.obs;
  // giá trị ngôi nhà
  String? landValueTxt;
  String? landValue;
  RxBool isValidateLandValue = false.obs;

  // thời hạn bảo hiểm
  LandTimeType landTimeTypeChoosed = LandTimeType.one;

  // ngày hiệu lực/ ngày hết hạn
  DateTime timeStart = DateTime.now();
  String startTimeTxt = '';
  String endTimeTxt = '';

  // mã khuyến mại
  List<MaGiamGia>? listMaKM;
  MaGiamGia? maKMChoosed;
  String? errMaKm;

  // gói bảo hiểm mua thêm
  List<SpMuaThem>? listSpMuaThem;
  List<SpMuaThem> listSpMuaThemChoosed = [];
  List<SpMuaThem> listSpMuaThemBatBuoc = [];

  // tiền
  InsuranceGetPriceFormResquest requestGetPriceForm =
      InsuranceGetPriceFormResquest();
  InsuranceGetPriceResponse? insuranceGetPriceResponse;
  RxBool isLoadingMoney = false.obs;
  num total = 0;
  num disscount = 0;
  num totalPay = 0;

  // loading btn
  RxBool isLoadingBtn = false.obs;
  @override
  void onInit() async {
    super.onInit();
    _clearData();
    await _initData();
    updateTimeStart(timeStart, isInit: true);
  }

  Future<void> _initData() async {
    if (isLoading.value) {
      return;
    }
    isLoading.value = true;
    BaseResponse<InsuranceFilterResponse> res =
        await DelayLoadingCallApi.delayCallApi(ShoppingRepository.instance
            .getInsFilter(insId: productIns?.id ?? 0));
    if (res.code == CODE_SUCCESS) {
      listLoaiXe = res.data?.data?.loaiXe ?? [];
      listMaKM = res.data?.data?.maGiamGia ?? [];
      listSpMuaThem = res.data?.data?.spMuaThem ?? [];
      errMessage = null;
    } else {
      errMessage = res.message;
      dialogAsk(context, res.message);
    }
    _setup();
    update([
      InsuranceLandStepProductControllerUpdateKey.updateLandType,
      InsuranceLandStepProductControllerUpdateKey.updateCouponChoosed,
      InsuranceLandStepProductControllerUpdateKey.updateBonusChoosed,
    ]);
    isLoading.value = false;
  }

  void _clearData() {
    listLoaiXe?.clear();
    listMaKM?.clear();
    listSpMuaThem?.clear();
  }

  void _setup() {
    if ((listLoaiXe ?? []).isEmpty) {
      listLoaiXe?.add(LoaiXe(name: 'Không có dữ liệu'));
    }
    if ((listMaKM ?? []).isEmpty) {
      listMaKM?.add(MaGiamGia(name: 'Không có dữ liệu'));
    }
    loaiXeChoosed = listLoaiXe?.firstOrNull;
    maKMChoosed = listMaKM?.firstOrNull;
    // setup auto check cho gói bảo hiểm bắt buộc
    if ((listSpMuaThem ?? []).isNotEmpty) {
      if (productIns?.getInsType == InsuranceType.moto ||
          productIns?.getInsType == InsuranceType.car) {
        for (SpMuaThem i in listSpMuaThem ?? []) {
          if (i.code == 'BB') {
            listSpMuaThemBatBuoc.add(i);
          }
        }
      } else if (productIns?.getInsType == InsuranceType.land) {
        for (SpMuaThem i in listSpMuaThem ?? []) {
          if (i.code == 'COBAN') {
            listSpMuaThemBatBuoc.add(i);
          }
        }
      }
    }
    listSpMuaThemChoosed.addAll(listSpMuaThemBatBuoc);
  }

  void updateTimeStart(DateTime? newValue, {bool? isInit}) {
    if (newValue == null) {
      return;
    }
    timeStart = newValue;
    startTimeTxt = DateFormat("dd/MM/yyyy").format(timeStart);
    _updateTimeEnd(isInit: isInit);
  }

  Future<void> _updateTimeEnd({bool? isInit}) async {
    endTimeTxt = DateFormat("dd/MM/yyyy").format(timeStart
        .add(Duration(days: (365 * landTimeTypeChoosed.getDurationTime))));
    if (isInit != true) {
      await _calculateMoney();
    }
    update([InsuranceLandStepProductControllerUpdateKey.updateTime]);
  }

  Future<void> _calculateMoney() async {
    // check data (đủ thông tin request chưa)
    isValidateLandValue.value = true;
    isValidateYearUsed.value = true;
    if (validateSoNamSd() != null || validateGTriNha() != null) {
      return;
    }
    // convert dữ liệu số chỗ ngồi và trọng tải
    convertData();

    // request API get price
    requestGetPriceForm = requestGetPriceForm.copyWith(
      loaiBh: productIns?.group,
      loaiXe: loaiXeChoosed?.id,
      thoiHan: landTimeTypeChoosed.getDurationTime,
      tgBatDau: timeStart.millisecondsSinceEpoch ~/ 1000,
      maKm: maKMChoosed?.id,
      spMuaKem: listSpMuaThemChoosed.map((e) => e.code ?? '').toList(),
      namDaSd: yearUsed,
      gtriNgoiNha: landValue,
    );
    isLoadingMoney.value = true;
    BaseResponse<InsuranceGetPriceResponse> res =
        await DelayLoadingCallApi.delayCallApi(ShoppingRepository.instance
            .postGetPrice(formRequest: requestGetPriceForm));
    if (res.code == CODE_SUCCESS) {
      insuranceGetPriceResponse = res.data;
      total = insuranceGetPriceResponse?.data?.tongTien ?? 0;
      disscount = insuranceGetPriceResponse?.data?.giam ?? 0;
      totalPay = insuranceGetPriceResponse?.data?.thanhToan ?? 0;
      errMessage = null;
      _validateMaKM();
    } else {
      errMessage = res.message;
      dialogAsk(context, res.message);
    }
    update([InsuranceLandStepProductControllerUpdateKey.updateMoney]);
    isLoadingMoney.value = false;
  }

  void _validateMaKM() {
    // update tbao lỗi mã gg
    if (disscount == 0 && maKMChoosed != null) {
      errMaKm = 'Sản phẩm không áp dụng mã giảm giá này';
    } else {
      errMaKm = null;
    }
    update([InsuranceLandStepProductControllerUpdateKey.updateCouponChoosed]);
  }

  void onChangeYearUsed(String strNewYear) async {
    yearUsedTxt = strNewYear;
    await _calculateMoney();
  }

  String? validateSoNamSd() {
    if (isValidateYearUsed.value == true) {
      if (yearUsedTxt == null || (yearUsedTxt ?? '').isEmpty) {
        return 'Vui lòng nhập số năm sử dụng';
      }
      yearUsed = int.parse(yearUsedTxt ?? '0');
      if ((yearUsed ?? 0) < 1) {
        return 'Tối thiểu 01 năm sử dụng';
      }
      if ((yearUsed ?? 0) > 30) {
        return 'Tối đa 30 năm sử dụng';
      }
    }
    return null;
  }

  void onChangeLandValue(String strNewValue) async {
    // dữ liệu làm request
    landValue = strNewValue;
    // landValue = strNewValue.replaceAll(',', '').replaceAll('.', '');
    // cv data

    landValueTxt = strNewValue;
    // landValueTxt = ConvertMoney.convertVNDMoney(_tmp).replaceAll('.', ',');
    await _calculateMoney();
  }

  String? validateGTriNha() {
    if (isValidateLandValue.value == true) {
      if (landValue == null || (landValue ?? '').isEmpty) {
        return 'Vui lòng nhập giá trị ngôi nhà';
      }
      num? _gtriNha = num.parse(landValue ?? '0');
      // Giá trị nhập vào phải >= 100tr và <= 5 tỷ
      if (_gtriNha < 100000000 || _gtriNha > 5000000000) {
        return 'Giá trị cho phép từ 100.000.000đ đến 5.000.000.000đ';
      }
    }
    return null;
  }

  void updateLandType(LoaiXe newValue) async {
    loaiXeChoosed = newValue;
    update([InsuranceLandStepProductControllerUpdateKey.updateLandType]);
    await _calculateMoney();
  }

  void updateLandTimeType(LandTimeType newValue) {
    landTimeTypeChoosed = newValue;
    update([InsuranceLandStepProductControllerUpdateKey.updateLandTimeType]);
    _updateTimeEnd();
  }

  void updateCouponChoosed(MaGiamGia newValue) async {
    maKMChoosed = newValue;
    update([InsuranceLandStepProductControllerUpdateKey.updateCouponChoosed]);
    await _calculateMoney();
  }

  void onSelectInsBonus(SpMuaThem newValue) async {
    // với gói bắt buộc ko được bỏ chọn
    if (listSpMuaThemBatBuoc.contains(newValue)) {
      return;
    }
    // với các gói còn lại
    if (listSpMuaThemChoosed.contains(newValue)) {
      listSpMuaThemChoosed.remove(newValue);
    } else {
      listSpMuaThemChoosed.add(newValue);
    }
    update([InsuranceLandStepProductControllerUpdateKey.updateBonusChoosed]);
    await _calculateMoney();
  }

  Future<void> onClickBtn(BuildContext context) async {
    /// case đang loading
    if (isLoadingBtn.value == true ||
        isLoadingMoney.value == true ||
        errMessage != null) {
      return;
    }

    /// validate các trường
    isValidateLandValue.value = true;
    isValidateYearUsed.value = true;
    if (validateGTriNha() != null || validateSoNamSd() != null) {
      // case thông tin ko hợp lệ
      return;
    }

    /// tính tiền lại
    await _calculateMoney();

    /// loading  btn
    isLoadingBtn.value = true;

    if (errMessage == null) {
      // convert
      InsuranceCreateOrdRequestForm _insCreateOrdForm =
          InsuranceCreateOrdRequestForm(
        productId: productIns?.id,
        loaiBh: requestGetPriceForm.loaiBh,
        loaiXe: requestGetPriceForm.loaiXe,
        thoiHan: requestGetPriceForm.thoiHan,
        tgBatDau: requestGetPriceForm.tgBatDau,
        mdSuDung: requestGetPriceForm.mdSuDung,
        soChoNgoi: requestGetPriceForm.soChoNgoi,
        trongTai: requestGetPriceForm.trongTai,
        namDaSd: requestGetPriceForm.namDaSd,
        gtriNgoiNha: requestGetPriceForm.gtriNgoiNha,
        maKm: requestGetPriceForm.maKm,
        ddDk: insuranceGetPriceResponse?.data?.dataDk,
      );
      Navigator.push(
          context,
          MaterialPageRoute(
              builder: (_) => LandStepConfirmScreen(
                    insuranceCreateOrdRequestForm: _insCreateOrdForm,
                  )));
    }
    isLoadingBtn.value = false;
  }

  void convertData() {
    yearUsed = int.parse(yearUsedTxt ?? '0');
  }
}

class InsuranceLandStepProductControllerUpdateKey {
  static const String updateLandType = '/update_land_type';
  static const String updateTime = '/update_time';
  static const String updateLandTimeType = '/update_land_time_type';
  static const String updateCouponChoosed = '/update_coupon_choosed';
  static const String updateBonusChoosed = '/update_bonus_choosed';
  static const String updateMoney = '/update_money';
}
