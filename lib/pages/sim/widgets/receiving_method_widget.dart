import 'package:flutter/material.dart';
import 'package:shopping/constants/app_colors.dart';
import 'package:shopping/constants/app_text_style.dart';
import 'package:shopping/constants/enum_receiving_method_widget.dart';

class ChoosenAddressReceiving {
  static List<ReceivingMethodByType> listReceiveMethod = [ReceivingMethodByType.specificLocation, ReceivingMethodByType.tradingLocation];
}

class CustomRadioBtnLineWidget extends StatelessWidget {
  const CustomRadioBtnLineWidget({
    super.key,
    required this.item,
    required this.groupValue,
    required this.onChanged,
  });
  final ReceivingMethodByType item;
  final ReceivingMethodByType? groupValue;
  final Function(ReceivingMethodByType?) onChanged;
  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(14),
        border: Border.all(
          color: AppColors.greyD9D9D9, //color of border
          width: 1, //width of border
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Row(
          children: [
            SizedBox(
              width: 20,
              height: 32,
              child: Radio<ReceivingMethodByType>(
                toggleable: false,
                activeColor: AppColors.redCE3722,
                fillColor: WidgetStateProperty.resolveWith<Color>((Set<WidgetState> states) {
                  if (states.contains(WidgetState.selected)) {
                    return AppColors.redCE3722;
                  }
                  return AppColors.greyDFE4EA;
                }),
                materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                value: item,
                groupValue: groupValue,
                onChanged: onChanged,
              ),
            ),
            const SizedBox(width: 10),
            Text(
              item.nameLocation,
              style: AppTextStyle.s14Medium.copyWith(
                fontWeight: FontWeight.w500,
                height: 24 / 14,
                color: AppColors.black111928,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
