class PaymentInforResponse {
  String? merId;
  String? currency;
  int? amount;
  String? invoiceNo;
  String? goodsNm;
  String? notiUrl;
  String? description;
  String? userLanguage;
  int? timeStamp;
  String? merTrxId;
  String? merchantToken;

  PaymentInforResponse({
    this.merId,
    this.currency,
    this.amount,
    this.invoiceNo,
    this.goodsNm,
    this.notiUrl,
    this.description,
    this.userLanguage,
    this.timeStamp,
    this.merTrxId,
    this.merchantToken,
  });

  factory PaymentInforResponse.fromJson(Map<String, dynamic> json) =>
      PaymentInforResponse(
        merId: json["merId"],
        currency: json["currency"],
        amount: json["amount"],
        invoiceNo: json["invoiceNo"],
        goodsNm: json["goodsNm"],
        notiUrl: json["notiUrl"],
        description: json["description"],
        userLanguage: json["userLanguage"],
        timeStamp: json["timeStamp"],
        merTrxId: json["merTrxId"],
        merchantToken: json["merchantToken"],
      );

  Map<String, dynamic> toJson() => {
        "merId": merId,
        "currency": currency,
        "amount": amount,
        "invoiceNo": invoiceNo,
        "goodsNm": goodsNm,
        "notiUrl": notiUrl,
        "description": description,
        "userLanguage": userLanguage,
        "timeStamp": timeStamp,
        "merTrxId": merTrxId,
        "merchantToken": merchantToken,
      };
}
