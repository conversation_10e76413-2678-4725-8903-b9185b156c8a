import 'package:flutter/material.dart';
import 'package:shopping/constants/app_colors.dart';
import 'package:shopping/constants/app_text_style.dart';

class AppTextField extends StatelessWidget {
  const AppTextField({
    super.key,
    this.controller,
    this.hintText,
    this.labelStyle,
    this.onChanged,
    this.onFieldSubmitted,
    this.maxLength,
  });
  final TextEditingController? controller;
  final String? hintText;
  final TextStyle? labelStyle;
  final Function(String)? onChanged;
  final Function(String)? onFieldSubmitted;
  final int? maxLength;
  @override
  Widget build(BuildContext context) {
    return TextFormField(
      onChanged: onChanged,
      onFieldSubmitted: onFieldSubmitted,
      style: labelStyle,
      controller: controller,
      maxLength: maxLength,
      decoration: InputDecoration(
        labelStyle: labelStyle,
        isDense: true,
        contentPadding: const EdgeInsets.symmetric(
          horizontal: 10,
          vertical: 12,
        ),
        hintText: hintText,
        hintStyle: AppTextStyle.s14Regular.copyWith(color: AppColors.black2E2E2E, height: 20 / 14),
        counterText: '',
        enabledBorder: OutlineInputBorder(
          borderSide: const BorderSide(width: 1, color: AppColors.blue005BF9),
          borderRadius: BorderRadius.circular(10),
        ),
        focusedBorder: OutlineInputBorder(
          borderSide: const BorderSide(width: 1, color: AppColors.blue005BF9),
          borderRadius: BorderRadius.circular(10),
        ),
      ),
    );
  }
}
