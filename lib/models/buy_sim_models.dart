import 'dart:convert';

import 'package:shopping/enum/enum_sim_type.dart';

GetOtpResponse getOtpResponseFromJson(String str) =>
    GetOtpResponse.fromJson(json.decode(str));

String getOtpResponseToJson(GetOtpResponse data) => json.encode(data.toJson());

class GetOtpResponse {
  final int? orderId;

  GetOtpResponse({
    this.orderId,
  });

  factory GetOtpResponse.fromJson(Map<String, dynamic> json) => GetOtpResponse(
        orderId: json["order_id"],
      );

  Map<String, dynamic> toJson() => {
        "order_id": orderId,
      };
}

FilterResponse filterResponseFromJson(String str) =>
    FilterResponse.fromJson(json.decode(str));

String filterResponseToJson(FilterResponse data) => json.encode(data.toJson());

class FilterResponse {
  final Item? item;

  FilterResponse({
    this.item,
  });

  factory FilterResponse.fromJson(Map<String, dynamic> json) => FilterResponse(
        item: json["item"] == null ? null : Item.fromJson(json["item"]),
      );

  Map<String, dynamic> toJson() => {
        "item": item?.toJson(),
      };
}

class Item {
  final GiaSim? giaSim;

  Item({
    this.giaSim,
  });

  factory Item.fromJson(Map<String, dynamic> json) => Item(
        giaSim:
            json["gia_sim"] == null ? null : GiaSim.fromJson(json["gia_sim"]),
      );

  Map<String, dynamic> toJson() => {
        "gia_sim": giaSim?.toJson(),
      };
}

class GiaSim {
  final num? giaSimVatLy;
  final num? giaESim;
  final num? phiHoaMangTraTruoc;
  final num? phiHoaMangTraSau;
  final num? phiGiaoHangTaiQuay;
  final num? phiGiaoHangTaiNha;

  GiaSim({
    this.giaSimVatLy,
    this.giaESim,
    this.phiHoaMangTraTruoc,
    this.phiHoaMangTraSau,
    this.phiGiaoHangTaiQuay,
    this.phiGiaoHangTaiNha,
  });

  factory GiaSim.fromJson(Map<String, dynamic> json) => GiaSim(
        giaSimVatLy: json["gia_sim_vat_ly"],
        giaESim: json["gia_e_sim"],
        phiHoaMangTraTruoc: json["phi_hoa_mang_tra_truoc"],
        phiHoaMangTraSau: json["phi_hoa_mang_tra_sau"],
        phiGiaoHangTaiQuay: json["phi_giao_hang_tai_quay"],
        phiGiaoHangTaiNha: json["phi_giao_hang_tai_nha"],
      );

  Map<String, dynamic> toJson() => {
        "gia_sim_vat_ly": giaSimVatLy,
        "gia_e_sim": giaESim,
        "phi_hoa_mang_tra_truoc": phiHoaMangTraTruoc,
        "phi_hoa_mang_tra_sau": phiHoaMangTraSau,
        "phi_giao_hang_tai_quay": phiGiaoHangTaiQuay,
        "phi_giao_hang_tai_nha": phiGiaoHangTaiNha,
      };
  num getGiaSim(SimType simType) {
    if (simType == SimType.physical) {
      return (giaSimVatLy ?? 0) + (phiHoaMangTraTruoc ?? 0);
    } else {
      return (giaESim ?? 0) + (phiHoaMangTraTruoc ?? 0);
    }
  }
}
