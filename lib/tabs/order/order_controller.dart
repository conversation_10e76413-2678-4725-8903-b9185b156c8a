import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:infinite_scroll_pagination/infinite_scroll_pagination.dart';
import 'package:shopping/apis/ShoppingRepository.dart';
import 'package:shopping/models/order_detail.dart';

import '../../models/order.dart';

class OrderController extends GetxController {
  Future<void> loadContents(int pageType) async {}
  static const per_page = 200;
  var isSearch = false.obs;
  var keyword = ''.obs;
  TextEditingController searchController = TextEditingController();
  FocusNode searchFocusNode = FocusNode();
  var page_index = 0.obs;
  var orderDetail = Rxn<OrderDetail>();
  Rx<bool> isLoading = false.obs;
  final PagingController<int, Order> scController =
      PagingController(firstPageKey: 1);
  final PagingController<int, Order> prController =
      PagingController(firstPageKey: 1);
  final PagingController<int, Order> faController =
      PagingController(firstPageKey: 1);
  final PagingController<int, Order> allController =
      PagingController(firstPageKey: 1);
  final PagingController<int, Order> refundController =
      PagingController(firstPageKey: 1);

  @override
  void onInit() {
    allController.addPageRequestListener((pageKey) {
      getOrder(pageKey: pageKey);
    });
    scController.addPageRequestListener((pageKey) {
      getOrder(pageKey: pageKey, status: 1);
    });
    prController.addPageRequestListener((pageKey) {
      getOrder(pageKey: pageKey, status: 2);
    });
    faController.addPageRequestListener((pageKey) {
      getOrder(pageKey: pageKey, status: 10);
    });
    refundController.addPageRequestListener((pageKey) {
      getOrder(pageKey: pageKey, status: 3);
    });

    super.onInit();
  }

  void toggleSearch() {
    keyword.value = '';
    isSearch.toggle();
    searchController.clear();
    if (isSearch.value == false) {
      switch (page_index.value) {
        case 0:
          allController.itemList?.clear();
          getOrder(pageKey: 1);
        case 1:
          scController.itemList?.clear();
          getOrder(pageKey: 1, status: 2);
        case 2:
          prController.itemList?.clear();
          getOrder(pageKey: 1, status: 1);
        case 3:
          faController.itemList?.clear();
          getOrder(pageKey: 1, status: 10);
        case 4:
          refundController.itemList?.clear();
          getOrder(pageKey: 1, status: 3);
      }
    }
    if (isSearch.value) {
      searchFocusNode.requestFocus();
    }
  }

  void changePage(int page) {
    // scrollController.
    if (page != page_index.value) {
      page_index.value = page;
      prController.itemList = [];
      allController.itemList = [];
      scController.itemList = [];
      faController.itemList = [];
      switch (page) {
        case 0:
          getOrder(pageKey: 1, status: null, keyword: keyword.value);
        case 1:
          getOrder(pageKey: 1, status: 2, keyword: keyword.value);
        case 2:
          getOrder(pageKey: 1, status: 1, keyword: keyword.value);
        case 3:
          getOrder(pageKey: 1, status: 10, keyword: keyword.value);
        case 4:
          getOrder(pageKey: 1, status: 3, keyword: keyword.value);
      }
    }
    // pageController.jumpToPage(page);
  }

  void changeSearch(bool iS) {
    print(iS);
    isSearch.value = iS;
    keyword.value = '';
    if (iS == false) {
      switch (page_index.value) {
        case 0:
          allController.itemList?.clear();
          getOrder(pageKey: 1);
        case 1:
          scController.itemList?.clear();
          getOrder(pageKey: 1, status: 2);
        case 2:
          prController.itemList?.clear();
          getOrder(pageKey: 1, status: 1);
        case 3:
          faController.itemList?.clear();
          getOrder(pageKey: 1, status: 10);
        case 4:
          refundController.itemList?.clear();
          getOrder(pageKey: 1, status: 3);
      }
    }
  }

  void onSearch() {
    // keyword.value = value ?? '';
    getOrder(pageKey: 1, keyword: keyword.value);
    prController.itemList?.clear();
    allController.itemList?.clear();
    scController.itemList?.clear();
    faController.itemList?.clear();
    refundController.itemList?.clear();
  }

  // void onSearch(String? value) {
  //   keyword.value = value ?? '';
  //   getOrder(pageKey: 1, keyword: keyword.value);
  //   prController.itemList?.clear();
  //   allController.itemList?.clear();
  //   scController.itemList?.clear();
  //   faController.itemList?.clear();
  // }
  void onChangeSetValue(String value) {
    // set value
    keyword.value = value.trim();
  }

  @override
  void onReady() async {
    print("onReady");
  }

  @override
  void onClose() {
    super.onClose();
    prController.dispose();
    allController.dispose();
    scController.dispose();
    faController.dispose();
    refundController.dispose();
  }

  getOrder({
    int? pageKey,
    int? status,
    String? keyword,
  }) async {
    final res = await ShoppingRepository.instance
        .getOrder2(status: status, page: pageKey, keyword: keyword);
    final List<Order> listOrder = res.data?.items ?? [];
    final isLastPage = listOrder.length < per_page;
    if (isLastPage) {
      switch (status) {
        case 1:
          scController.appendLastPage(listOrder);
        case 2:
          prController.appendLastPage(listOrder);
        case 3:
          refundController.appendLastPage(listOrder);
        case 10:
          faController.appendLastPage(listOrder);
        default:
          allController.appendLastPage(listOrder);
      }
    } else {
      final nextPageKey = pageKey! + 1;
      switch (status) {
        case 1:
          scController.appendPage(listOrder, nextPageKey);
        case 2:
          prController.appendPage(listOrder, nextPageKey);
        case 3:
          refundController.appendPage(listOrder, nextPageKey);
        case 10:
          faController.appendPage(listOrder, nextPageKey);
        default:
          allController.appendPage(listOrder, nextPageKey);
      }
    }
  }

  //get order detail
  Future<void> getOrderDetail(int? id) async {
    isLoading.toggle();
    final res = await ShoppingRepository.instance.getOrderDetail2(id);
    isLoading.toggle();
    if (res.code == 0) {
      orderDetail.value = res.data!;
      update();
    }
  }
}
