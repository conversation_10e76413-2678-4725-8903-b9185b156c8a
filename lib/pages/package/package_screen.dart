import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:infinite_scroll_pagination/infinite_scroll_pagination.dart';
import 'package:shopping/constants/app_assets_paths.dart';
import 'package:shopping/constants/app_colors.dart';
import 'package:shopping/constants/app_text_style.dart';
import 'package:shopping/constants/enum_package_type.dart';
import 'package:shopping/loading_screen.dart';
import 'package:shopping/models/home_model.dart';
import 'package:shopping/models/product.dart';
import 'package:shopping/pages/package/package_controller.dart';
import 'package:shopping/pages/package/widgets/filter_endraw_widget.dart';
import 'package:shopping/pages/package/widgets/product_widget.dart';
import 'package:shopping/tabs/home/<USER>/item_package_action.dart';
import 'package:shopping/utils/responsive_utils.dart';

class PackageScreen extends StatelessWidget {
  final PackageType packageType;
  final HomeData? homeData;
  PackageScreen({super.key, required this.packageType, this.homeData}) {}
  @override
  Widget build(BuildContext context) {
    final double w = MediaQuery.of(context).size.width;
    final int _cntItem = ResponsiveUtils.isMobile(context)
        ? 2
        : ResponsiveUtils.isTablet(context)
            ? 3
            : 4;
    // itemWidth = (device width - 2 viền - khoảng cách giữa các item)/số lượng item 1 row
    final double _itemWidth = (w - 30 - (_cntItem - 1) * 15) / _cntItem;
    return GetBuilder<PackageController>(
      init: PackageController(
          packageTypeInput: packageType,
          homeData: homeData ?? null,
          context: context),
      builder: (controller) => LoadingOverlay(
        child: Scaffold(
          appBar: AppBar(
            titleSpacing: 0,
            leading: GestureDetector(
              child: Container(
                width: 20,
                height: 20,
                color: Colors.transparent,
                child: SvgPicture.asset(SvgPath.svgArrowBack,
                    colorFilter: ColorFilter.mode(AppColors.black, BlendMode.srcIn), fit: BoxFit.scaleDown),
              ),
              onTap: () async {
                if (controller.isSearching.value) {
                  controller.toggleSearch();
                  // await controller.getListData();
                } else {
                  Navigator.pop(context);
                }
              },
            ),
            backgroundColor: AppColors.white,
            elevation: 5,
            shadowColor: AppColors.black.withValues(alpha: 0.4),
            surfaceTintColor: AppColors.white,
            title: GetBuilder<PackageController>(
              id: PackageControllerUpdateKey.updatePackageTitle,
              builder: (controller) => Obx(() => controller.isSearching.value
                  ? _SearchField(controller: controller)
                  : Text(controller.packageTitle,
                      style: AppTextStyle.s16Bold
                          .copyWith(color: AppColors.black2E2E2E))),
            ),
            centerTitle: true,
            actions: [
              Obx(() => controller.isSearching.value
                  ? const SizedBox(width: 24)
                  : Container(
                      width: 36,
                      height: 36,
                      margin: EdgeInsets.only(right: 24),
                      // decoration: BoxDecoration(
                      //     borderRadius: BorderRadius.circular(23),
                      //     color: AppColors.greyF1F1F3),
                      child: Center(
                        child: IconButton(
                          icon: SvgPicture.asset(SvgPath.svgIconSearch,
                              width: 16, height: 16, colorFilter: ColorFilter.mode(AppColors.black, BlendMode.srcIn)),
                          onPressed: () {
                            controller.toggleSearch();
                          },
                        ),
                      ),
                    )),
            ],
          ),
          endDrawerEnableOpenDragGesture: false,
          onEndDrawerChanged: (isOpened) {
            controller.onInitDataToOpenFilter();
          },
          endDrawer: FilterEndrawWidget(controller: controller),
          body: ColoredBox(
            color: AppColors.white,
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 10),
              child: Column(mainAxisSize: MainAxisSize.min, children: [
                Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Expanded(
                      child: SizedBox(
                          height: 28,
                          child: GetBuilder<PackageController>(
                            id: PackageControllerUpdateKey.updateTab,
                            builder: (controller) => ListView.builder(
                                itemCount: controller.listTabType.length,
                                scrollDirection: Axis.horizontal,
                                itemBuilder: (_, index) {
                                  return Obx(() => GestureDetector(
                                        onTap: () {
                                          controller.onChangeTab(
                                              controller.listTabType[index]);
                                        },
                                        child: Container(
                                            margin: EdgeInsets.only(right: 5),
                                            padding: EdgeInsets.symmetric(
                                                horizontal: 12, vertical: 4),
                                            decoration: BoxDecoration(
                                                borderRadius:
                                                    BorderRadius.circular(20),
                                                border: controller.listTabType[
                                                            index] ==
                                                        controller
                                                            .packageTypeChoosed
                                                            .value
                                                    ? Border.all(
                                                        color: AppColors
                                                            .blue30AAB7)
                                                    : null),
                                            child: Text(
                                              controller.listTabType[index]
                                                  .nameTypePackage,
                                              style: AppTextStyle.s13Medium.copyWith(
                                                  color: controller.listTabType[
                                                              index] ==
                                                          controller
                                                              .packageTypeChoosed
                                                              .value
                                                      ? AppColors.blue30AAB7
                                                      : AppColors.black),
                                            )),
                                      ));
                                }),
                          )),
                    ),
                    const SizedBox(width: 5),
                    Builder(
                      builder: (context) => GestureDetector(
                        onTap: () {
                          // open endraw
                          Scaffold.of(context).openEndDrawer();
                        },
                        child: Container(
                          color: Colors.transparent,
                          child: Padding(
                            padding: const EdgeInsets.only(bottom: 4),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                SvgPicture.asset(SvgPath.svgIconFilter),
                                Obx(() => Text(
                                      '${controller.countNumTypeFilter.value == 0 ? "   " : "(${controller.countNumTypeFilter.value})"}',
                                      style: AppTextStyle.s12Medium
                                          .copyWith(color: AppColors.black),
                                    )),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
                // list item package
                const SizedBox(height: 10),
                GetBuilder<PackageController>(
                    id: PackageControllerUpdateKey.updateListProduct,
                    builder: (controller) => Expanded(
                          child: PagedGridView(
                            keyboardDismissBehavior:
                                ScrollViewKeyboardDismissBehavior.onDrag,
                            pagingController: controller.pageController,
                            physics: BouncingScrollPhysics(),
                            gridDelegate:
                                SliverGridDelegateWithFixedCrossAxisCount(
                              crossAxisCount: _cntItem,
                              mainAxisSpacing: 15,
                              crossAxisSpacing: 15,
                              childAspectRatio: _itemWidth / 180,
                            ),
                            builderDelegate: PagedChildBuilderDelegate<Product>(
                              itemBuilder: (c, i, index) {
                                return GestureDetector(
                                  onTap: () {
                                    ItemPackageAction.openDetail(context, i);
                                  },
                                  child: ProductPackageWidget(
                                    product: i,
                                    openBuy: () {
                                      ItemPackageAction.openSheetBuy(
                                          context, i);
                                    },
                                  ),
                                );
                              },
                              noItemsFoundIndicatorBuilder: (context) {
                                return EmptyListWidget(
                                    messageFail: controller.messageFail.value,
                                    isSearchingOrFilter: (controller
                                            .isSearching.value ||
                                        controller.countNumTypeFilter.value !=
                                            0));
                              },
                              firstPageProgressIndicatorBuilder: (context) =>
                                  SizedBox(),
                              newPageProgressIndicatorBuilder: (context) =>
                                  SizedBox(),
                            ),
                          ),
                        ))
              ]),
            ),
          ),
        ),
      ),
    );
  }
}

class _SearchField extends StatelessWidget {
  const _SearchField({required this.controller});
  final PackageController controller;
  @override
  Widget build(BuildContext context) {
    return Container(
      height: 44,
      margin: EdgeInsets.only(bottom: 4),
      child: TextField(
          focusNode: controller.focusNode,
          style: AppTextStyle.s14Regular.copyWith(color: AppColors.black2E2E2E),
          maxLength: 200,
          controller: controller.searchController,
          // onChanged: controller.onChangeSetValue,
          cursorColor: AppColors.black2E2E2E,
          keyboardType: TextInputType.text,
          autofocus: true,
          onEditingComplete: () {
            controller.onTapSearch();
          },
          decoration: InputDecoration(
            contentPadding: EdgeInsets.symmetric(vertical: 10, horizontal: 12),
            hintText: 'Tìm kiếm gói cước theo tên hoặc số điện thoại',
            hintStyle: AppTextStyle.s13Regular.copyWith(color: AppColors.black),
            filled: true,
            fillColor: AppColors.greyF5F6F9,
            counterText: '',
            border: const UnderlineInputBorder(),
            enabledBorder: OutlineInputBorder(
              borderSide:
                  const BorderSide(width: 1, color: AppColors.greyE5E5E5),
              borderRadius: BorderRadius.circular(30),
            ),
            focusedBorder: OutlineInputBorder(
              borderSide:
                  const BorderSide(width: 1, color: AppColors.greyE5E5E5),
              borderRadius: BorderRadius.circular(30),
            ),
            suffixIcon: Row(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                GestureDetector(
                  behavior: HitTestBehavior.opaque,
                  onTap: controller.onTapSearch,
                  child: Padding(
                      padding: const EdgeInsets.all(15),
                      child: SvgPicture.asset(
                        SvgPath.svgIconSearch,
                        colorFilter: ColorFilter.mode(AppColors.black, BlendMode.srcIn),
                      )),
                )
              ],
            ),
          )),
    );
  }
}

class EmptyListWidget extends StatelessWidget {
  const EmptyListWidget(
      {super.key, this.messageFail, required this.isSearchingOrFilter});
  final String? messageFail;
  final bool isSearchingOrFilter;
  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          SvgPicture.asset(SvgPath.svgFilterEmpty),
          const SizedBox(height: 24),
          Text(
            (messageFail != null && (messageFail?.length != 0))
                ? messageFail ?? ''
                : isSearchingOrFilter
                    ? "Không có sản phẩm phù hợp"
                    : "Không có sản phẩm nào",
            style: AppTextStyle.s15Regular,
            textAlign: TextAlign.center,
          )
        ],
      ),
    );
  }
}
