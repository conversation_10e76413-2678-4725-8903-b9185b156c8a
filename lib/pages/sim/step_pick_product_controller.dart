import 'dart:async';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:infinite_scroll_pagination/infinite_scroll_pagination.dart';
import 'package:shopping/apis/ShoppingRepository.dart';
import 'package:shopping/app_services/api_service.dart';
import 'package:shopping/constants/enum_filter_product.dart';
import 'package:shopping/constants/enum_package_type.dart';
import 'package:shopping/constants/enum_step_ord_sim.dart';
import 'package:shopping/loading_screen.dart';
import 'package:shopping/models/base_response.dart';
import 'package:shopping/models/home_model.dart';
import 'package:shopping/models/product.dart';

class StepPickProductController extends GetxController
    with WidgetsBindingObserver {
  StepPickProductController({this.homeData});
  final HomeData? homeData;
  // line step status bar
  final List<StepOrdSimStatus> listStepOrdName = StepOrdSimStatus.values;
  // loadmore
  final PagingController<int, Product> pageController =
      PagingController(firstPageKey: 1);
  final int perPage = 50;
  // message lỗi
  Rx<String> messageFail = ''.obs;

  /// filter
  // gói cước
  List<CycleType> listCycleTypeChoosed = [];
  List<CycleType> listCycleResult = [];
  // khoảng giá
  List<PriceFromType> listPriceFromTypeChoosed = [];
  List<PriceFromType> listPriceFromResult = [];
  // sắp xếp
  OrderByType? orderByTypeChoosed;
  OrderByType? orderByTypeResult;
  // số lượng tiêu chí filter
  Rx<int> countNumTypeFilter = 0.obs;
  // count time
  Rx<int> countTime = 1800.obs;
  Timer? timer;
  DateTime? _pausedTime;
  @override
  void onInit() {
    _initData();
    super.onInit();
    WidgetsBinding.instance.addObserver(this);
    _countDownTime();
  }

  @override
  void onClose() {
    timer?.cancel();
    WidgetsBinding.instance.removeObserver(this);
    super.onClose();
  }

  @override
  void didChangeAppLifecycleState(state) {
    super.didChangeAppLifecycleState(state);
    if (state == AppLifecycleState.paused) {
      _pauseTimer();
    } else if (state == AppLifecycleState.resumed) {
      _resumeTimer();
    }
  }

  void _pauseTimer() {
    timer?.cancel();
    _pausedTime = DateTime.now(); // Lưu time khi app paused
  }

  void _resumeTimer() {
    if (_pausedTime != null) {
      // Tính time từ khi app paused đến khi app được resumed lại
      final elapsed = DateTime.now().difference(_pausedTime!).inSeconds;
      if (countTime.value <= elapsed) {
        countTime.value = 0;
      } else {
        countTime.value -= elapsed;
      }
    }
    if (countTime.value <= 0) {
      return;
    } else {
      _countDownTime(); // Restart the timer
    }
  }

  void _countDownTime() {
    timer = Timer.periodic(const Duration(seconds: 1), (t) {
      if (timer == null) {
        timer = t;
      }
      if (countTime.value <= 0) {
        timer?.cancel();
        countTime.value = 0;
      } else {
        countTime.value--;
      }
    });
  }

  void _initData() async {
    _initFilter();
    pageController.addPageRequestListener((pageKey) {
      getListData(pageKey: pageKey);
    });
    // await getListData(pageKey: 1);
  }

  void _initFilter() {
    listCycleResult.clear();
    listPriceFromResult.clear();
    orderByTypeResult = null;
    countNumTypeFilter.value = 0;
  }

  void onInitDataToOpenFilter() {
    listCycleTypeChoosed.clear();
    listCycleTypeChoosed.addAll(listCycleResult);
    listPriceFromTypeChoosed.clear();
    listPriceFromTypeChoosed.addAll(listPriceFromResult);
    orderByTypeChoosed = null;
    orderByTypeChoosed = orderByTypeResult;
    update([
      StepPickProductControllerUpdateKey.updateListCycleType,
      StepPickProductControllerUpdateKey.updateListPriceType,
      StepPickProductControllerUpdateKey.updateOrderByType
    ]);
  }

  void onResetFilter() async {
    _initFilter();
    onInitDataToOpenFilter();
    countNumTypeFilter.value = countFilter();
    await getListData(pageKey: 1);
  }

  void onSelectFilterCycle(CycleType cycleType) {
    // case set giá trị false (do select chẵn lần)
    if (listCycleTypeChoosed.contains(cycleType)) {
      listCycleTypeChoosed.remove(cycleType);
    } else {
      listCycleTypeChoosed.add(cycleType);
    }
    update([StepPickProductControllerUpdateKey.updateListCycleType]);
  }

  bool isFilterCycleSelected(CycleType cycleType) {
    return (listCycleTypeChoosed.contains(cycleType));
  }

  void onSelectFilterPrice(PriceFromType priceFromType) {
    // case set giá trị false (do select chẵn lần)
    if (listPriceFromTypeChoosed.contains(priceFromType)) {
      listPriceFromTypeChoosed.remove(priceFromType);
    } else {
      listPriceFromTypeChoosed.add(priceFromType);
    }
    update([StepPickProductControllerUpdateKey.updateListPriceType]);
  }

  bool isFilterPriceSelected(PriceFromType priceFromType) {
    return (listPriceFromTypeChoosed.contains(priceFromType));
  }

  void onSelectFilterOrderBy(OrderByType orderByType) {
    if (orderByTypeChoosed == orderByType) {
      orderByTypeChoosed = null;
    } else {
      orderByTypeChoosed = orderByType;
    }
    update([StepPickProductControllerUpdateKey.updateOrderByType]);
  }

  int countFilter() {
    int tmp = 0;
    if (listCycleResult.isNotEmpty) tmp++;
    if (listPriceFromResult.isNotEmpty) tmp++;
    if (orderByTypeResult != null) tmp++;
    return tmp;
  }

  void onSubmitFilter() async {
    // gán giá trị
    _initFilter();
    listCycleResult.addAll(listCycleTypeChoosed);
    listPriceFromResult.addAll(listPriceFromTypeChoosed);
    orderByTypeResult = orderByTypeChoosed;
    // count lọc
    countNumTypeFilter.value = countFilter();
    await getListData(pageKey: 1);
  }

  Future getListData({int? pageKey}) async {
    // context.loaderOverlay.show();
    BaseResponse<ProductsResponse> res;
    // phần filter
    List<int>? _listCycle = _getListCycleFilter();
    List<int>? _listPrice = _getListPriceFilter();
    String? _orderBy = _getOrderByFilter();
    // mở từ top action => get API group = SIM
    if (!isFromConfigHome) {
      res = await DelayLoadingCallApi.delayCallApi(ShoppingRepository.instance
          .get_products2(
              group: PackageType.sim.getValue,
              cycles: _listCycle,
              prices: _listPrice,
              orderBy: _orderBy,
              perPage: perPage,
              page: pageKey));
    } else {
      // mở từ config home
      res = await DelayLoadingCallApi.delayCallApi(ShoppingRepository.instance
          .get_products2(
              pageSettingId: homeData?.id,
              cycles: _listCycle,
              prices: _listPrice,
              orderBy: _orderBy,
              perPage: perPage,
              page: pageKey));
    }
    // context.loaderOverlay.hide();
    // nếu là trang đầu tiên -> clear data
    if (pageKey == 1) {
      // listProductResult.clear();
      pageController.itemList?.clear();
    }
    if (res.code == CODE_SUCCESS) {
      if (res.message != null && (res.message?.isNotEmpty ?? false)) {
        messageFail.value = res.message ?? '';
        pageController.itemList?.clear();
        // listProductResult = [];
      } else {
        final List<Product>? listP = res.data?.items;
        final isLastPage = listP!.length < perPage;
        if (isLastPage) {
          pageController.appendLastPage(listP);
        } else {
          final nextPageKey = pageKey! + 1;
          pageController.appendPage(listP, nextPageKey);
        }
        // listProductResult = pageController.itemList!;
        messageFail.value = '';
      }
    }
    // listProductResult.addAll(pageController.itemList ?? []);
    update([StepPickProductControllerUpdateKey.updateListProduct]);
  }

  List<int>? _getListCycleFilter() {
    return (listCycleResult.isEmpty
        ? null
        : List<int>.from(listCycleResult.map((e) => e.getValue)));
  }

  List<int>? _getListPriceFilter() {
    List<int> _listTmp = [];
    if (listPriceFromResult.isNotEmpty) {
      // check case chứa giá 1tr => xoá r add vào sau thành phần tử cuối
      if (listPriceFromResult.contains(PriceFromType.f1M)) {
        listPriceFromResult.remove(PriceFromType.f1M);
        listPriceFromResult.add(PriceFromType.f1M);
      }
      for (PriceFromType e in listPriceFromResult) {
        _listTmp.addAll(e.getListPrice);
      }
    }
    return listPriceFromResult.isEmpty ? null : _listTmp;
  }

  String? _getOrderByFilter() {
    return orderByTypeResult?.getValue ?? null;
  }

  // case open từ config home
  bool get isFromConfigHome {
    return (homeData != null);
  }
}

class StepPickProductControllerUpdateKey {
  static const String updateListProduct =
      'step_pick_product_controller/update_list_product';
  static const String updateListCycleType =
      'step_pick_product_controller/update_list_cycle_type';
  static const String updateListPriceType =
      'step_pick_product_controller/update_list_price_type';
  static const String updateOrderByType =
      'step_pick_product_controller/update_list_order_by_type';
}
