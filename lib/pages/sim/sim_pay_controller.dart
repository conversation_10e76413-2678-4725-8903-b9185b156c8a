import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:shopping/apis/ShoppingRepository.dart';
import 'package:shopping/app_services/api_service.dart';
import 'package:shopping/app_services/app_storage.dart';
import 'package:shopping/constants/enum_step_ord_sim.dart';
import 'package:shopping/enum/enum_payment_type.dart';
import 'package:shopping/loading_screen.dart';
import 'package:shopping/models/base_response.dart';
import 'package:shopping/models/list_bank_infor_response.dart';
import 'package:shopping/models/payment_infor_response.dart';
import 'package:shopping/models/sim_model.dart';
import 'package:shopping/pages/sim/item_sim_action.dart';
import 'package:shopping/pages/sim/step_pick_product_controller.dart';
import 'package:shopping/shopping.dart';
import 'package:shopping/shopping_controller.dart';
import 'package:shopping/utils/extension/int_extension.dart';
import 'package:shopping/utils/extension/run_mode_extension.dart';
import 'package:shopping/utils/toast_utils.dart';
import 'package:shopping/widgets/app_dialog.dart';
import 'package:url_launcher/url_launcher.dart';

class SimPayController extends GetxController {
  SimPayController(this.simOrder, this.context);
  SIMOrder simOrder;
  final BuildContext context;
  // count time
  StepPickProductController stepPickProductController =
      StepPickProductController();
  Rx<bool> isLoading = false.obs;
  Rx<String> messErr = ''.obs;
  //
  String couponTxt = '';
  String inviteCodeTxt = '';
  // Phương thức thanh toán
  PaymentOnlineType paymentOnlineChoosed = PaymentOnlineType.qr;
  bool isShowListBank = false;
  ListBankInforResponse? listBankInforResponse;
  List<PaymentOnlineType?> listPaymentOnlineType = [];
  List<Bank> listBank = [];
  Bank? bankSelected;
  // get infor pay
  PaymentInforResponse? paymentInforResponse;
  // String web browser
  String urlPayOnWeb = '';
  RxBool enableBtn = true.obs;

  // get list bank infor to show
  // create sim -> get ordId
  //getPaymentInfor (lấy thông tin thanh toán)
  // mở webview

  @override
  void onInit() {
    // step pick controller
    if (Get.isRegistered<StepPickProductController>()) {
      stepPickProductController = Get.find<StepPickProductController>();
    }
    _initData();
    super.onInit();
  }

  // line step status bar
  final List<StepOrdSimStatus> listStepOrdName = StepOrdSimStatus.values;
  Rx<bool> isRememberMe = true.obs;

  Future _initData() async {
    isLoading.toggle();
    BaseResponse<ListBankInforResponse> res =
        await DelayLoadingCallApi.delayCallApi(
            ShoppingRepository.instance.getListBankInfor());
    isLoading.toggle();
    if (res.code == CODE_SUCCESS) {
      listBankInforResponse = res.data;
      listPaymentOnlineType = listBankInforResponse?.getListPayTypeSim ?? [];
      if (listPaymentOnlineType.length == 2) {
        listPaymentOnlineType = PaymentOnlineType.values;
      }
      paymentOnlineChoosed =
          listPaymentOnlineType.first ?? PaymentOnlineType.qr;
      _checkShowBank();
      listBank = listBankInforResponse?.bank ?? [];
    } else {
      dialogAsk(context, res.message);
    }
    _updatePaymentTypeForCreate();
    update([SIMPayControllerUpdateKey.updateData]);
  }

  void _checkShowBank() {
    if (paymentOnlineChoosed == PaymentOnlineType.atm) {
      isShowListBank = true;
    } else {
      isShowListBank = false;
    }
  }

  void onChangedPaymetType(PaymentOnlineType? value) {
    if (value == null) {
      return;
    }
    paymentOnlineChoosed = value;
    _checkShowBank();
    _updateStateBtn();
    update([
      SIMPayControllerUpdateKey.updatePaymentType,
      SIMPayControllerUpdateKey.updateStateEnabeButton
    ]);
  }

  bool isSelected(PaymentOnlineType value) {
    return paymentOnlineChoosed == value;
  }

  void onChangedRemeberMe(bool? value) {
    isRememberMe.value = value ?? false;
    _updateStateBtn();
    update([SIMPayControllerUpdateKey.updateAgreePolicy]);
  }

  void _updateStateBtn() {
    if (paymentOnlineChoosed == PaymentOnlineType.atm) {
      enableBtn.value = (isRememberMe.value && bankSelected != null);
    } else {
      enableBtn.value = isRememberMe.value;
    }
    update([SIMPayControllerUpdateKey.updateAgreePolicy]);
  }

  void onClickBank(Bank item) {
    if (bankSelected == item) {
      bankSelected = null;
    } else {
      bankSelected = item;
    }
    _updateStateBtn();
    update([
      SIMPayControllerUpdateKey.updatePaymentType,
      SIMPayControllerUpdateKey.updateStateEnabeButton
    ]);
  }

  Future<void> onClickButtonConfirm() async {
    // create SIM
    bool? isCreateSuccess = await _createSIMOrder();
    // get payment infor
    if (isCreateSuccess == true) {
      await _getPaymentInfor();
      Map<String, dynamic>? map = paymentInforResponse?.toJson();
      if (paymentInforResponse == null ||
          simOrder.orderId == null ||
          map == null) {
        return;
      }
      // xử lý data => open web thanh toan
      RunMode _rMode = ApiService.instance.runMode;
      String _baseUrlWeb = _rMode.getUrlOnWeb;
      String _payUrl = map.entries
          .map((e) =>
              '${Uri.encodeComponent(e.key)}=${Uri.encodeComponent('${e.value}')}')
          .join('&');
      String _payType = 'payType=${paymentOnlineChoosed.getValue}';
      String _customizeCallBack =
          'callBackUrl=$_baseUrlWeb/payment/result/${simOrder.orderId}';
      String _customizeReqDomain = 'reqDomain=$_baseUrlWeb';
      String _customizeWindowColor = 'windowColor=%23ef5459';
      String _userPhoneNumber = 'user_phone_number=${simOrder.userPhoneNumber}';
      String _phoneNumber = 'phone_number=${simOrder.phoneNumber}';
      int? _appId = AppStorage.instance.getInt2(SKeysPK.appId) ??
          Get.find<ShoppingController>().appId;
      String _deepLink = 'deep_link=${_appId?.getDeepLink}&is_mobile=true';
      String _bankCode;
      if (isShowListBank && bankSelected != null) {
        _bankCode = '&bankCode=${bankSelected?.code}';
      } else {
        _bankCode = '';
      }
      String _typeSimMobile = 'type=sim&order=${simOrder.orderId}';
      urlPayOnWeb +=
          '$_baseUrlWeb/payment-new/${simOrder.orderId}?$_payUrl&$_payType&$_customizeCallBack&$_customizeReqDomain&$_customizeWindowColor&$_typeSimMobile&$_userPhoneNumber&$_phoneNumber&$_deepLink$_bankCode';
      try {
        // pop về home
        ActionBack.onBack(context: context);
        await launchUrl(Uri.parse(urlPayOnWeb),
            mode: LaunchMode.externalApplication);
      } catch (e) {
        ToastUtils.showFail('Xảy ra lỗi khi mở trang thanh toán: $e');
      }
    }
  }

  Future<bool?> _createSIMOrder() async {
    if (isLoading.value) {
      return null;
    }
    isLoading.toggle();
    var res = await ShoppingRepository.instance.postCreate(simOrder);
    isLoading.toggle();
    if (res.code == 0) {
      return true;
      // Navigator.push(
      //     context,
      //     MaterialPageRoute(
      //         builder: (context) => NotificationBuyPackageScreen(
      //               simResponse: res.data,
      //             )));
    } else {
      dialogAsk(context, res.message, isBackAction: true, callOk: () {
        ShoppingRepository.instance.postUnBookSim(ordId: simOrder.orderId);
        ActionBack.onBack(context: context);
        // Navigator.of(context).popUntil(ModalRoute.withName("/page_list_sim"));
      });
    }
    return false;
  }

  void _updatePaymentTypeForCreate() {
    // update payment_type // do dix chỉ thanh toán online => fix type = 2
    simOrder = simOrder.copyWith(paymentType: 2);
  }

  void updateCoupon(String? newValue) {
    if (newValue == null) {
      return;
    }
    couponTxt = newValue;
  }

  void updateInviteCode(String? newValue) {
    if (newValue == null) {
      return;
    }
    inviteCodeTxt = newValue;
  }

  Future _getPaymentInfor() async {
    if (simOrder.orderId == null) {
      return;
    }
    isLoading.toggle();
    BaseResponse<PaymentInforResponse> response = await ShoppingRepository
        .instance
        .getPaymentInfor(ordId: simOrder.orderId ?? 0);
    isLoading.toggle();
    if (response.code == CODE_SUCCESS) {
      paymentInforResponse = response.data;
    }
  }
}

class SIMPayControllerUpdateKey {
  static const String updateAgreePolicy =
      'sim_pay_controller/update_agree_policy';
  static const String updateData = 'sim_pay_controller/update_data';
  static const String updatePaymentType =
      'sim_pay_controller/update_payment_type';
  static const String updateStateEnabeButton =
      'sim_pay_controller/update_state_enable_button';
}
