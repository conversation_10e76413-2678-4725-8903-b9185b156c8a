import 'dart:io';
import 'dart:math' as math;

import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:image_picker/image_picker.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:shopping/app_services/app_storage.dart';
import 'package:shopping/utils/extension/string_extension.dart';

import '../../../constants/app_assets_paths.dart';
import '../../../constants/app_colors.dart';
import '../../../constants/app_text_style.dart';
import '../../../widgets/app_dialog.dart';

class ProfileAvatarWidget extends StatefulWidget {
  const ProfileAvatarWidget({
    super.key,
    this.path,
    this.onChangeAvt,
    this.name,
  });
  final String? path;
  final Function(XFile?)? onChangeAvt;
  final String? name;

  @override
  State<ProfileAvatarWidget> createState() => _ProfileAvatarWidgetState();
}

class _ProfileAvatarWidgetState extends State<ProfileAvatarWidget> {
  dynamic s;
  XFile? imgFile;
  @override
  void initState() {
    super.initState();
  }

  Future<void> getDataPermission() async {
    s = await SharedPreferences.getInstance();
  }

  // check quyền và select image

  Future permissionAndSelectImg() async {
    // check có phải lần đầu denied không?
    final firstP = s.getString(SKeysPK.firstPermissionPhoto);
    if (firstP == null) {
      await s.setString(SKeysPK.firstPermissionPhoto, 'hihi');
      // check case multi request image => nếu null thì mới cho pick ảnh
      if (imgFile == null) {
        imgFile = await ImagePicker().pickImage(source: ImageSource.gallery);
      }
      if (imgFile != null) {
        await imgFile?.readAsBytes();
        widget.onChangeAvt?.call(imgFile);
        // đổi avt xong thì clear imgFile cho cấe tiếp tục change avt
        imgFile = null;
      }
    } else {
      try {
        if (imgFile == null) {
          imgFile = await ImagePicker().pickImage(source: ImageSource.gallery);
        }
        if (imgFile != null) {
          await imgFile?.readAsBytes();
          widget.onChangeAvt?.call(imgFile);
          // đổi avt xong thì clear imgFile cho cấe tiếp tục change avt
          imgFile = null;
        }
      } catch (e) {
        // case multiple_request (PlatformException(multiple_request, Cancelled by a second request, null, null)
        if(e.toString().contains("multiple")){
           imgFile = null;
          return;
        }
        if (await Permission.mediaLibrary.isDenied == true && Platform.isIOS) {
          dialogAsk(context, 'Vui lòng cấp quyền truy cập thư viện',
              isShowLater: true,
              textButton: 'Mở cài đặt',
              isBackAction: true, callOk: () {
            openAppSettings();
            Navigator.pop(context);
          });
        }
        imgFile = null;
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder(
        future: getDataPermission(),
        builder: (context, snap) {
          return Center(
            child: Stack(
              children: [
                Container(
                  height: 134,
                  width: 134,
                  // color: Colors.red,
                  color: Colors.transparent,
                  child: Center(
                    child: GestureDetector(
                      onTap: () async {
                        await permissionAndSelectImg();
                      },
                      child: Container(
                        height: 124,
                        width: 124,
                        decoration: BoxDecoration(
                          // color: Color(0xffebf3fe),
                          color: widget.name?.isNotEmpty == true
                              ? Color((math.Random().nextDouble() * 0xFFFFFF)
                                      .toInt())
                                  .withValues(alpha: 0.5)
                              : Color(0xffebf3fe),
                          shape: BoxShape.circle,
                          image: (widget.path?.isNotEmpty == true)
                              ? DecorationImage(
                                  image: NetworkImage(widget.path ?? ''),
                                  fit: BoxFit.cover)
                              : null,
                        ),
                        child: widget.path?.isEmpty == true
                            ? Center(
                                child: Container(
                                  height: 64,
                                  width: 64,
                                  child: widget.name?.isNotEmpty == true
                                      ? Center(
                                          child: Text(
                                            '${widget.name?.getNameAvatar()}',
                                            style:
                                                AppTextStyle.s24Medium.copyWith(
                                              color: Color((math.Random()
                                                              .nextDouble() *
                                                          0xFFFFFF)
                                                      .toInt())
                                                  .withValues(alpha: 1.0),
                                            ),
                                          ),
                                        )
                                      : SvgPicture.asset(
                                          SvgPath.svgUserAvatar,
                                        ),
                                ),
                              )
                            : const SizedBox.shrink(),
                      ),
                    ),
                  ),
                ),
                Positioned(
                  right: 0,
                  bottom: 0,
                  child: GestureDetector(
                    onTap: () async {
                      await permissionAndSelectImg();
                    },
                    child: Container(
                      height: 46,
                      width: 46,
                      decoration: BoxDecoration(
                        color: AppColors.greyF5F6F9,
                        shape: BoxShape.circle,
                        border: Border.all(
                          width: 2,
                          color: Colors.white,
                        ),
                      ),
                      child: Center(
                          child: SvgPicture.asset(
                        SvgPath.svgCamera,
                        fit: BoxFit.fill,
                      )),
                    ),
                  ),
                ),
              ],
            ),
          );
        });
  }
}
