import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:shopping/constants/app_assets_paths.dart';
import 'package:shopping/constants/app_colors.dart';
import 'package:shopping/constants/app_text_style.dart';
import 'package:shopping/models/product.dart';
import 'package:shopping/utils/check_overflow_text.dart';

class ProductPackageWidget extends StatelessWidget {
  const ProductPackageWidget(
      {super.key,
      required this.product,
      required this.openBuy,
      this.isProductHome,
      this.isBuyWithSim});
  final Product? product;
  final Function() openBuy;
  final bool? isProductHome;
  final bool? isBuyWithSim;
  @override
  Widget build(BuildContext context) {
    return Container(
      height: 180,
      decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(14),
          border: Border.all(width: 1, color: AppColors.pinkE1EBFF),
          image: DecorationImage(
              image: AssetImage((isProductHome != null && isProductHome == true)
                  ? ImagePath.imgBgProductHome
                  : ImagePath.imgBgItemProductPackage),
              fit: BoxFit.fill)),
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: 9),
        child: Column(
          mainAxisSize: MainAxisSize.max,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              height: 18,
              margin: EdgeInsets.only(top: 10),
              child: Row(
                children: [
                  Flexible(
                      child: Text(
                    product?.getContent(1) ?? '',
                    style:
                        AppTextStyle.s15Medium.copyWith(color: AppColors.black),
                    overflow: TextOverflow.clip,
                    maxLines: 1,
                    softWrap: false,
                  )),
                  Visibility(
                      visible: product?.isPriceContent(1) ?? false,
                      child: Text(
                        'đ',
                        style: AppTextStyle.s10.copyWith(
                          color: AppColors.black,
                        ),
                        overflow: TextOverflow.ellipsis,
                      ))
                ],
              ),
            ),
            Container(
              height: 18,
              margin: EdgeInsets.only(top: 5),
              child: Row(
                children: [
                  Flexible(
                      child: Text(
                    product?.getContent(2) ?? '',
                    style: AppTextStyle.s15SemiBold
                        .copyWith(color: AppColors.black),
                    overflow: TextOverflow.clip,
                    maxLines: 1,
                    softWrap: false,
                  )),
                  Visibility(
                      visible: product?.isPriceContent(2) ?? false,
                      child: Text(
                        'đ',
                        style: AppTextStyle.s10Bold.copyWith(
                          color: AppColors.black,
                        ),
                        overflow: TextOverflow.ellipsis,
                      ))
                ],
              ),
            ),
            Container(
              height: 21,
              margin: EdgeInsets.only(top: 5),
              child: Row(
                mainAxisSize: MainAxisSize.max,
                children: [
                  (product?.isShowIconWithPosition(3) ?? false)
                      ? Padding(
                          padding: const EdgeInsets.only(right: 4),
                          child: SvgPicture.asset(SvgPath.svgIconContentCheck),
                        )
                      : SizedBox(),
                  Flexible(
                    child: Text(
                      product?.getContent(3) ?? '',
                      style: AppTextStyle.s13Regular
                          .copyWith(color: AppColors.black, height: 20 / 13),
                      overflow: TextOverflow.clip,
                      maxLines: 1,
                      softWrap: false,
                    ),
                  ),
                  Visibility(
                      visible: product?.isPriceContent(3) ?? false,
                      child: Text(
                        'đ',
                        style: AppTextStyle.s8.copyWith(
                          color: AppColors.black,
                        ),
                        overflow: TextOverflow.ellipsis,
                      ))
                ],
              ),
            ),
            Container(
              height: 21,
              child: Row(
                mainAxisSize: MainAxisSize.max,
                children: [
                  (product?.isShowIconWithPosition(4) ?? false)
                      ? Padding(
                          padding: const EdgeInsets.only(right: 4),
                          child: SvgPicture.asset(SvgPath.svgIconContentCheck),
                        )
                      : SizedBox(),
                  Flexible(
                    child: Text(
                      product?.getContent(4) ?? '',
                      style: AppTextStyle.s13Regular
                          .copyWith(color: AppColors.black, height: 20 / 13),
                      overflow: TextOverflow.clip,
                      maxLines: 1,
                      softWrap: false,
                    ),
                  ),
                  Visibility(
                      visible: product?.isPriceContent(4) ?? false,
                      child: Text(
                        'đ',
                        style: AppTextStyle.s8.copyWith(
                          color: AppColors.black,
                        ),
                        overflow: TextOverflow.ellipsis,
                      ))
                ],
              ),
            ),
            Container(
              height: 23,
              margin: EdgeInsets.only(top: 5),
              child: Row(
                children: [
                  // tính số icon có thể show
                  (product?.getListLogo == null ||
                          (product?.getListLogo ?? []).length == 0)
                      ? const SizedBox()
                      : Flexible(
                          child: LayoutBuilder(builder: (context, constraints) {
                            double maxWidth = constraints.maxWidth;
                            String content4 = 'Miễn phí ';
                            double textWidth = CheckOverFlow.textWidth(
                              text: "$content4",
                              style: TextStyle(
                                fontSize: 10,
                                fontWeight: FontWeight.w500,
                                color: AppColors.black,
                              ),
                            );
                            return Container(
                              width: maxWidth,
                              child: CheckOverFlow.doesTextOverflow(
                                      text: "$content4",
                                      style: TextStyle(
                                        fontSize: 10,
                                        fontWeight: FontWeight.w500,
                                        color: AppColors.black,
                                      ),
                                      maxWidth: maxWidth)
                                  ? Text(
                                      content4,
                                      overflow: TextOverflow.clip,
                                      maxLines: 1,
                                      softWrap: false,
                                      style: TextStyle(
                                        fontSize: 10,
                                        fontWeight: FontWeight.w500,
                                        color: AppColors.black,
                                      ),
                                    )
                                  : Row(
                                      children: [
                                        Row(
                                          mainAxisSize: MainAxisSize.min,
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Text(
                                              content4,
                                              overflow: TextOverflow.clip,
                                              maxLines: 1,
                                              softWrap: false,
                                              style: TextStyle(
                                                fontSize: 10,
                                                fontWeight: FontWeight.w500,
                                                color: AppColors.black,
                                              ),
                                            ),
                                          ],
                                        ),
                                        SizedBox(width: 2),
                                        product?.getListLogo == null
                                            ? SizedBox()
                                            : ListView.builder(
                                                shrinkWrap: true,
                                                physics:
                                                    NeverScrollableScrollPhysics(),
                                                scrollDirection:
                                                    Axis.horizontal,
                                                itemCount: CheckOverFlow
                                                    .calculateNumIcon(
                                                        maxWidth: maxWidth,
                                                        textWidth: textWidth,
                                                        listIconLength: product
                                                                ?.getListLogo
                                                                ?.length ??
                                                            0,
                                                        iconSize: 23),
                                                itemBuilder: (context, index) =>
                                                    Padding(
                                                  padding: EdgeInsets.only(
                                                      left: index == 0 ? 0 : 3),
                                                  child: Image.network(
                                                    product?.getListLogo?[
                                                            index] ??
                                                        '',
                                                    width: 20,
                                                    height: 20,
                                                  ),
                                                ),
                                              ),
                                      ],
                                    ),
                            );
                          }),
                        ),
                ],
              ),
            ),
            GestureDetector(
              onTap: openBuy,
              child: Container(
                  height: 34,
                  margin: EdgeInsets.only(bottom: 8, top: 9),
                  decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(30),
                      border:
                          Border.all(width: 1, color: AppColors.blue30AAB7)),
                  child: Center(
                    child: Text(
                      (isBuyWithSim ?? false)
                          ? 'CHỌN GÓI'
                          : 'Đăng ký'.toUpperCase(),
                      style: AppTextStyle.s13SemiBold.copyWith(
                        height: 17 / 13,
                        color: AppColors.blue30AAB7,
                      ),
                    ),
                  )),
            )
          ],
        ),
      ),
    );
  }
}
