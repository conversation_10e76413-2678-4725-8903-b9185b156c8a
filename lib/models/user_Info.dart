// To parse this JSON data, do
//
//     final userInfor = userInforFrom<PERSON>son(jsonString);

import 'dart:convert';

import 'package:intl/intl.dart';

UserInfo userInforFromJson(String str) => UserInfo.fromJson(json.decode(str));

String userInforToJson(UserInfo data) => json.encode(data.toJson());

class UserInfo {
  int? id;
  String? code;
  String? fullname;
  String? imgAvatar;
  String? phoneNumber;
  String? cccd;
  String? birthday;
  int? sex;
  String? email;
  int? cityId;
  int? districtId;
  int? wardId;
  int? streetId;
  String? address;
  String? addressFull;
  int? channelRegister;

  UserInfo({
    this.id,
    this.code,
    this.fullname,
    this.imgAvatar,
    this.phoneNumber,
    this.cccd,
    this.birthday,
    this.sex,
    this.email,
    this.cityId,
    this.districtId,
    this.wardId,
    this.streetId,
    this.address,
    this.addressFull,
    this.channelRegister,
  });

  UserInfo copyWith({
    int? id,
    String? code,
    String? fullname,
    String? imgAvatar,
    String? phoneNumber,
    String? cccd,
    String? birthday,
    int? sex,
    String? email,
    int? cityId,
    int? districtId,
    int? wardId,
    int? streetId,
    String? address,
    String? addressFull,
    int? channelRegister,
  }) =>
      UserInfo(
        id: id ?? this.id,
        code: code ?? this.code,
        fullname: fullname ?? this.fullname,
        imgAvatar: imgAvatar ?? this.imgAvatar,
        phoneNumber: phoneNumber ?? this.phoneNumber,
        cccd: cccd ?? this.cccd,
        birthday: birthday ?? this.birthday,
        sex: sex ?? this.sex,
        email: email ?? this.email,
        cityId: cityId ?? this.cityId,
        districtId: districtId ?? this.districtId,
        wardId: wardId ?? this.wardId,
        streetId: streetId ?? this.streetId,
        address: address ?? this.address,
        addressFull: addressFull ?? this.addressFull,
        channelRegister: channelRegister ?? this.channelRegister,
      );

  factory UserInfo.fromJson(Map<String, dynamic> json) => UserInfo(
        id: json["id"] != null ? json["id"] : null,
        code: json["code"] != null ? json["code"] : null,
        fullname: json["fullname"] != null ? json["fullname"] : null,
        imgAvatar: json["img_avatar"] != null ? json["img_avatar"] : null,
        phoneNumber: json["phone_number"] != null ? json["phone_number"] : null,
        cccd: json["cccd"] != null ? json["cccd"] : null,
        birthday: json["birthday"] != null ? json["birthday"] : null,
        sex: json["sex"] != null ? json["sex"] : null,
        email: json["email"] != null ? json["email"] : null,
        cityId: json["city_id"] != null ? json["city_id"] : null,
        districtId: json["district_id"] != null ? json["district_id"] : null,
        wardId: json["ward_id"] != null ? json["ward_id"] : null,
        streetId: json["street_id"] != null ? json["street_id"] : null,
        address: json["address"] != null ? json["address"] : null,
        addressFull: json["address_full"] != null ? json["address_full"] : null,
        channelRegister: json["channel_register"] != null ? json["channel_register"] : null,
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "code": code,
        "fullname": fullname,
        "img_avatar": imgAvatar,
        "phone_number": phoneNumber,
        "cccd": cccd,
        "birthday": birthday,
        "sex": sex,
        "email": email,
        "city_id": cityId,
        "district_id": districtId,
        "ward_id": wardId,
        "street_id": streetId,
        "address": address,
        "channel_register": channelRegister,
      };
  Map<String, dynamic> toJsonUpdate() => {
        "phone_number": phoneNumber,
        "fullname": fullname,
        "cccd": cccd,
        "birthday": birthday?.isNotEmpty == true ? ((DateFormat('dd/MM/yyyy').parse(birthday ?? '').millisecondsSinceEpoch) / 1000).round() : null,
        "email": email,
        "sex": sex,
        "city_id": cityId,
        "district_id": districtId != -1 ? districtId : null,
        "ward_id": wardId != -1 ? wardId : null,
        "street_id": streetId != -1 ? streetId : null,
        "address": address,
      };
}
