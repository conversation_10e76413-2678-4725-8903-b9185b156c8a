#!/bin/bash
if [[ "$T_CI_TAG" =~ ^dev_* ]]; then
    echo 'Build Dev'
    # rm -rf lib/config/config.dart
    # echo $T_CI_DEV > lib/config/config.dart
    # cat lib/config/config.dart
fi
if [[ "$T_CI_TAG" =~ ^staging_* ]]; then
    echo 'Build Stag'
    # cat example/web/.well-known/assetlinks.json
    # rm -rf lib/config/config.dart
    # echo $T_CI_STAG > lib/config/config.dart
    # cat lib/config/config.dart
fi
if [[ "$T_CI_TAG" =~ ^pro_* ]]; then
    echo 'Build Pro'
    # rm -rf lib/config/config.dart
    # echo $T_CI_PRO > lib/config/config.dart
    # cat lib/config/config.dart
fi