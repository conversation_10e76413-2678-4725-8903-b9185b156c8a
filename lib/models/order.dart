import 'dart:convert';

import 'package:shopping/enum/enum_order_product_type.dart';

OrderResponse orderResponseFromJson(String str) =>
    OrderResponse.fromJson(json.decode(str));

String orderResponseToJson(OrderResponse data) => json.encode(data.toJson());

class OrderResponse {
  final List<Order>? items;

  OrderResponse({
    this.items,
  });

  factory OrderResponse.fromJson(Map<String, dynamic> json) => OrderResponse(
        items: json["items"] == null
            ? []
            : List<Order>.from(json["items"]!.map((x) => Order.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "items": items == null
            ? []
            : List<dynamic>.from(items!.map((x) => x.toJson())),
      };
}

class Order {
  final List<ProductItem>? productItems;
  final int? id;
  final String? code;
  final int? createdAt;
  final String? timeAt;
  final int? totalPrice;
  final int? status;
  final int? type;
  final String? supplier;
  final String? thoiGian;
  final String? gcnBh;
  final String? linkTc;

  Order({
    this.productItems,
    this.id,
    this.code,
    this.createdAt,
    this.timeAt,
    this.totalPrice,
    this.status,
    this.type,
    this.supplier,
    this.thoiGian,
    this.gcnBh,
    this.linkTc,
  });

  factory Order.fromJson(Map<String, dynamic> json) => Order(
        productItems: json["product_items"] == null
            ? []
            : List<ProductItem>.from(
                json["product_items"]!.map((x) => ProductItem.fromJson(x))),
        id: json["id"],
        code: json["code"],
        createdAt: json["created_at"],
        timeAt: json["time_at"],
        totalPrice: json["total_price"],
        status: json["status"],
        type: json["type"],
        supplier: json["supplier"],
        thoiGian: json["thoi_gian"],
        gcnBh: json["gcn_bh"],
        linkTc: json["link_tc"],
      );

  Map<String, dynamic> toJson() => {
        "product_items": productItems == null
            ? []
            : List<dynamic>.from(productItems!.map((x) => x.toJson())),
        "id": id,
        "code": code,
        "created_at": createdAt,
        "time_at": timeAt,
        "total_price": totalPrice,
        "status": status,
      };
  String? get getItemProductInfor {
    String dataName = "";
    String simName = "";
    if (productItems == null || productItems?.length == 0) {
      return "";
    }
    for (int i = 0; i < productItems!.length; i++) {
      // bảo hiểm abic
      if (isInsABICOrder) {
        return productItems?[0].productNameStr ?? 'N/A';
      }
      if (productItems?[i].getOrderProductType == OrderProductType.goicuoc) {
        dataName = '${productItems?[i].productName ?? ""}';
        if (productItems?.length == 1) {
          return dataName;
        }
      }
      if (productItems?[i].getOrderProductType == OrderProductType.sim) {
        simName = '${productItems?[i].productNameStr ?? ""}';
      }
    }
    if (dataName.isNotEmpty) {
      return '$simName, Gói $dataName';
    } else {
      return '$simName';
    }
  }

  bool get isInsABICOrder {
    return type == 3;
  }
}

class ProductItem {
  final int? type;
  final String? productName;
  final String? msisdn;
  final String? productNameStr;
  final int? giaSim;
  final int? quantity;
  final int? totalPrice;

  ProductItem({
    this.type,
    this.productName,
    this.msisdn,
    this.productNameStr,
    this.giaSim,
    this.quantity,
    this.totalPrice,
  });

  factory ProductItem.fromJson(Map<String, dynamic> json) => ProductItem(
        type: json["type"],
        productName: json["product_name"],
        msisdn: json["msisdn"],
        productNameStr: json["product_name_str"],
        giaSim: json["gia_sim"],
        quantity: json["quantity"],
        totalPrice: json["total_price"],
      );

  Map<String, dynamic> toJson() => {
        "type": type,
        "product_name": productName,
        "msisdn": msisdn,
        "product_name_str": productNameStr,
        "gia_sim": giaSim,
        "quantity": quantity,
        "total_price": totalPrice,
      };
  OrderProductType? get getOrderProductType {
    if (type == null) {
      return null;
    }
    return OrderProductType.values.firstWhere((e) => e.getValue == type);
  }

  String? get getProductInfor {
    if (getOrderProductType == OrderProductType.goicuoc) {
      return productName;
    }
    if (getOrderProductType == OrderProductType.sim) {
      if (productName == null) {
        return productNameStr;
      }
      return '$productNameStr, Gói $productName';
    }
    return null;
  }
}
