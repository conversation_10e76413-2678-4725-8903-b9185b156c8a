import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';
import 'package:loader_overlay/loader_overlay.dart';
import 'package:shopping/apis/ShoppingRepository.dart';
import 'package:shopping/app_services/api_service.dart';

import '../../models/user_Info.dart';
import '../../widgets/app_dialog.dart';

class ProfileController extends GetxController {
  late String vdcpToken;

  Future<void> loadContents(int pageType) async {}
  UserInfo? user;
  @override
  void onInit() {
    getUser();
    super.onInit();
  }

  @override
  void onReady() async {
    print("onReady");
  }

  @override
  void onClose() {
    super.onClose();
  }

  getUser() async {
    final res = await ShoppingRepository.instance.getUserInfo2();
    if (res.code == CODE_SUCCESS) {
      user = res.data;
      update();
    }
  }

  updateUser(UserInfo? u) async {
    user = u;
    update();
    //save to storage when neccessary
  }

  pickAvatar(BuildContext context, XFile path) async {
    final res = await ShoppingRepository.instance.uploadAvatar2(path);
    if (res?.isNotEmpty == true) {
      user = user?.copyWith(imgAvatar: res);
      update();
      context.loaderOverlay.hide();
    } else {
      dialogAsk(context, 'Thay đổi avatar không thành công');
      context.loaderOverlay.hide();
    }
  }
}
