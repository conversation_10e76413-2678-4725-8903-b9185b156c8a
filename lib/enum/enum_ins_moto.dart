enum MotoType { tren50cc, duoi50cc, mayDien, baBanh, khac }

enum MotoTimeType { one, two, three }

extension MotoTypeExt on MotoType {
  String get getName {
    switch (this) {
      case MotoType.tren50cc:
        return 'Từ 50cc trở lên';
      case MotoType.duoi50cc:
        return 'Dưới 50cc';
      case MotoType.mayDien:
        return 'Xe máy điện';
      case MotoType.baBanh:
        return 'Mô tô 3 bánh';
      case MotoType.khac:
        return 'Các loại xe khác';
    }
  }
}

extension MotoTimeExt on MotoTimeType {
  String get getName {
    switch (this) {
      case MotoTimeType.one:
        return '1 năm';
      case MotoTimeType.two:
        return '2 năm';
      case MotoTimeType.three:
        return '3 năm';
    }
  }

  int get getDurationTime {
    switch (this) {
      case MotoTimeType.one:
        return 1;
      case MotoTimeType.two:
        return 2;
      case MotoTimeType.three:
        return 3;
    }
  }
}
