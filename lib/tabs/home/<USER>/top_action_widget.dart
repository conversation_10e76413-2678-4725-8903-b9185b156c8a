import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:shopping/constants/app_colors.dart';
import 'package:shopping/constants/app_text_style.dart';

class TopActionWidget extends StatelessWidget {
  const TopActionWidget(
      {super.key,
      required this.actionName,
      required this.iconPath,
      required this.isHotDeal});
  final String actionName;
  final String iconPath;
  final bool isHotDeal;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Container(
          width: 52,
          height: 52,
          decoration: BoxDecoration(
            color: AppColors.pinkFFF0E9,
            borderRadius: BorderRadius.circular(100),
          ),
          child: Center(
              child: SizedBox(
            child: SvgPicture.asset(iconPath, width: 28, height: 28),
          )),
        ),
        const SizedBox(height: 10),
        Text(
          actionName,
          style: AppTextStyle.s13Medium
              .copyWith(fontWeight: FontWeight.w500, color: AppColors.black),
        )
      ],
    );
  }
}
