import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:shopping/constants/app_assets_paths.dart';
import 'package:shopping/constants/app_colors.dart';
import 'package:shopping/constants/app_text_style.dart';
import 'package:shopping/widgets/multi_button.dart';

import 'shopping_controller.dart';

part 'permission_app_screen_widget.dart';

class PermissionAppScreen extends StatefulWidget {
  const PermissionAppScreen({
    super.key,
    this.name,
    this.phoneNumber,
    this.email,
    this.onContinue,
    this.onCancel,
    this.isPush,
  });
  final String? name;
  final String? phoneNumber;
  final String? email;
  final Function()? onContinue;
  final Function()? onCancel;

  final bool? isPush;

  @override
  State<PermissionAppScreen> createState() => _PermissionAppScreenState();
}

class _PermissionAppScreenState extends State<PermissionAppScreen> {
  List<bool> listAccept = [true, true, true];
  bool? action;
  final shopState = Get.find<ShoppingController>().shopState;
  Worker? e;
  @override
  void initState() {
    super.initState();
    action = getAction();
    e = ever(shopState, (value) {
      if (value == ShopState.LOGIN_OK && widget.isPush == true) {
        Navigator.of(context).pop(true);
      }
    });
  }

  @override
  void dispose() {
    super.dispose();
    e?.dispose();
  }

  bool getAction() {
    return listAccept.any((element) => element == false);
  }

  void changeAccept(int index, bool? value) {
    setState(() {
      listAccept[index] = value ?? true;
      action = getAction();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.white,
      body: Stack(
        children: [
          Positioned(
            top: 10,
            left: -50,
            child: SvgPicture.asset(
              SvgPath.svgHiddenLogoApp,
            ),
          ),
          Positioned(
            bottom: 0,
            right: -30,
            child: SvgPicture.asset(
              SvgPath.svgHiddenLogoApp,
            ),
          ),
          Padding(
            padding: EdgeInsets.only(top: 10, left: 15, right: 15, bottom: 20),
            child: SingleChildScrollView(
              child: Column(
                children: [
                  _buildLogo(),
                  SizedBox(height: 30),
                  _buildBody(),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBody() {
    return Center(
      child: Column(
        children: [
          _buildTextAllowed(),
          SizedBox(
            height: 20,
          ),
          _buildLearnMore(),
          SizedBox(
            height: 50,
          ),
          _buildDefaultBox(
            title: "Tên",
            value: listAccept[0],
            onChanged: (value) {
              changeAccept(0, value);
            },
          ),
          SizedBox(
            height: 10,
          ),
          _buildDefaultBox(
            title: "Số điện thoại và email",
            value: listAccept[1],
            isName: false,
            onChanged: (value) {
              changeAccept(1, value);
            },
          ),
          SizedBox(
            height: 20,
          ),
          _buildAcceptTerm(
            listAccept[2],
            (value) {
              changeAccept(2, value);
            },
          ),
          SizedBox(
            height: 50,
          ),
          MultiButton(
            isDisableRightBtn: action,
            textLeft: "HUỶ",
            textRight: "TIẾP TỤC",
            onTapLeft: () {
              widget.isPush == true
                  ? Navigator.of(context).pop()
                  : widget.onCancel?.call();
            },
            onTapRight: () {
              if (action == false) {
                if (widget.isPush == true) {
                  Get.find<ShoppingController>().permissionAccept();
                } else {
                  widget.onContinue?.call();
                }
              }
            },
          )
        ],
      ),
    );
  }
}
