import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:shopping/constants/app_colors.dart';
import 'package:shopping/constants/app_text_style.dart';
import 'package:shopping/constants/enum_step_ord_insurance.dart';
import 'package:shopping/enum/enum_ins_land.dart';
import 'package:shopping/models/insurance_filter_response.dart';
import 'package:shopping/models/product.dart';
import 'package:shopping/pages/insurance/pages/car/insurance_car_step_product_screen.dart';
import 'package:shopping/pages/insurance/pages/land/insurance_land_step_product_controller.dart';
import 'package:shopping/pages/insurance/widgets/common_buy_insurance_view.dart';
import 'package:shopping/pages/insurance/widgets/title_lable_widget.dart';
import 'package:shopping/pages/package/widgets/filter_endraw_widget.dart';
import 'package:shopping/pages/sim/widgets/sim_text_field_widget.dart';
import 'package:shopping/utils/extension/int_extension.dart';
import 'package:shopping/widgets/app_radio_button_widget.dart';

class InsuranceLandStepProductScreen extends StatelessWidget {
  const InsuranceLandStepProductScreen({super.key, required this.productIns});
  final Product? productIns;
  @override
  Widget build(BuildContext context) {
    return GetBuilder<InsuranceLandStepProductController>(
      init: InsuranceLandStepProductController(
          productIns: productIns, context: context),
      builder: (controller) => Obx(
        () => CommonBuyInsuranceView(
          title: 'BẢO HIỂM NHÀ TƯ NHÂN',
          stepType: StepOrdInsuranceType.inforProduct,
          isLoading: controller.isLoading.value,
          bottonWidget: GetBuilder<InsuranceLandStepProductController>(
              id: InsuranceLandStepProductControllerUpdateKey.updateMoney,
              builder: (controller) => Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Obx(() => ItemLableWidget(
                            isLoadingMoney: controller.isLoadingMoney.value,
                            lable: 'Tổng tiền',
                            lableStyle: AppTextStyle.s12Medium
                                .copyWith(color: AppColors.black),
                            money:
                                ConvertMoney.convertVNDMoney(controller.total),
                            moneyStyle: AppTextStyle.s13SemiBold,
                          )),
                      const SizedBox(height: 4),
                      Obx(() => ItemLableWidget(
                            isLoadingMoney: controller.isLoadingMoney.value,
                            lable: 'Voucher',
                            lableStyle: AppTextStyle.s12Medium
                                .copyWith(color: AppColors.black),
                            money: ConvertMoney.convertVNDMoney(
                                controller.disscount * -1),
                            moneyStyle: AppTextStyle.s13SemiBold,
                          )),
                      const SizedBox(height: 4),
                      Obx(() => ItemLableWidget(
                            isLoadingMoney: controller.isLoadingMoney.value,
                            lable: 'Tổng tiền thanh toán',
                            lableStyle: AppTextStyle.s14Medium
                                .copyWith(color: AppColors.black),
                            money: ConvertMoney.convertVNDMoney(
                                controller.totalPay),
                            moneyStyle: AppTextStyle.s18SemiBold,
                          )),
                    ],
                  )),
          lableBtn: 'Tiếp tục',
          isLoadingBtn: controller.isLoadingBtn.value,
          onClickBtn: () async {
            await controller.onClickBtn(context);
          },
          body: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              //
              Padding(
                padding: EdgeInsets.only(top: 18),
                child: Text(
                  'Thông tin sản phẩm',
                  style:
                      AppTextStyle.s18SemiBold.copyWith(color: AppColors.black),
                ),
              ),
              GetBuilder<InsuranceLandStepProductController>(
                id: InsuranceLandStepProductControllerUpdateKey.updateLandType,
                builder: (controller) => CustomCommonDropdown<LoaiXe>(
                  lable: 'Loại nhà',
                  isRequired: true,
                  lableTextStyle:
                      AppTextStyle.s14Medium.copyWith(color: AppColors.black),
                  contentTextStyle:
                      AppTextStyle.s14Medium.copyWith(color: AppColors.black),
                  items: controller.listLoaiXe ?? [],
                  itemAsString: (p0) => p0.name ?? 'N/A',
                  value: controller.loaiXeChoosed,
                  onChanged: (LoaiXe? newValue) {
                    if (newValue == null) {
                      return;
                    }
                    controller.updateLandType(newValue);
                  },
                ),
              ),
              const SizedBox(height: 11),
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Flexible(
                    flex: 1,
                    child: Obx(
                      () => SIMTextFieldWidgetOnlyNumber(
                        title: 'Số năm đã sử dụng',
                        hintText: 'Số năm sử dụng',
                        isRequired: true,
                        maxLength: 2,
                        keyBoardType: TextInputType.number,
                        contentPadding:
                            EdgeInsets.symmetric(vertical: 13, horizontal: 10),
                        text: controller.yearUsedTxt,
                        errorText: controller.validateSoNamSd(),
                        onChanged: (p0) {
                          controller.isValidateYearUsed.value = false;
                          controller.onChangeYearUsed(p0);
                        },
                        onSubmitted: (p0) {
                          controller.isValidateYearUsed.value = true;
                          controller.onChangeYearUsed(p0);
                        },
                      ),
                    ),
                  ),
                  SizedBox(width: 20),
                  Flexible(
                      flex: 1,
                      child: Obx(
                        () => SIMTextFieldWidgetOnlyNumber(
                          title: 'Giá trị ngôi nhà',
                          hintText: 'Giá trị ngôi nhà',
                          isRequired: true,
                          maxLength: 10,
                          keyBoardType: TextInputType.number,
                          contentPadding: EdgeInsets.symmetric(
                              vertical: 13, horizontal: 12),
                          text: controller.landValueTxt,
                          errorText: controller.validateGTriNha(),
                          onChanged: (p0) {
                            controller.isValidateLandValue.value = false;
                            controller.onChangeLandValue(p0);
                          },
                          onSubmitted: (p0) {
                            controller.isValidateLandValue.value = true;
                            controller.onChangeLandValue(p0);
                          },
                        ),
                      )),
                ],
              ),
              TitleLable(lable: 'Thời hạn bảo hiểm', isRequired: true),
              GetBuilder<InsuranceLandStepProductController>(
                id: InsuranceLandStepProductControllerUpdateKey
                    .updateLandTimeType,
                builder: (controller) => SizedBox(
                  height: 40,
                  child: ListView.separated(
                    physics: NeverScrollableScrollPhysics(),
                    scrollDirection: Axis.horizontal,
                    itemCount: LandTimeType.values.length,
                    itemBuilder: (context, index) {
                      LandTimeType item = LandTimeType.values[index];
                      return InkWell(
                        splashColor: Colors.transparent,
                        onTap: () {
                          controller.updateLandTimeType(item);
                        },
                        child: AppRadioButtonWidget<LandTimeType>(
                            item: item,
                            groupValue: controller.landTimeTypeChoosed,
                            onChanged: (LandTimeType? value) {
                              if (value == null) {
                                return;
                              }
                              controller.updateLandTimeType(value);
                            },
                            borderColor: Colors.transparent,
                            widget: Text(item.getName,
                                style: AppTextStyle.s14Medium
                                    .copyWith(color: AppColors.black))),
                      );
                    },
                    separatorBuilder: (BuildContext context, int index) =>
                        SizedBox(width: 25),
                  ),
                ),
              ),
              // ngày
              GetBuilder<InsuranceLandStepProductController>(
                id: InsuranceLandStepProductControllerUpdateKey.updateTime,
                builder: (controller) => Row(
                  children: [
                    Flexible(
                      flex: 1,
                      child: SIMTextFieldWidget(
                        title: 'Ngày hiệu lực',
                        isRequired: true,
                        isDate: true,
                        text: controller.startTimeTxt,
                        pickDate: (p0) {
                          controller.updateTimeStart(p0);
                        },
                        initDate: controller.timeStart,
                        readOnly: true,
                        datePickerEntryMode: DatePickerEntryMode.calendarOnly,
                        firstdate: DateTime.now(),
                      ),
                    ),
                    SizedBox(width: 20),
                    Flexible(
                        flex: 1,
                        child: SIMTextFieldWidget(
                            title: 'Ngày hết hạn',
                            text: controller.endTimeTxt,
                            readOnly: true,
                            filled: true,
                            colorFill: AppColors.greyF5F5F5)),
                  ],
                ),
              ),
              TitleLable(lable: 'Gói bảo hiểm mua thêm', isRequired: false),
              GetBuilder<InsuranceLandStepProductController>(
                  id: InsuranceLandStepProductControllerUpdateKey
                      .updateBonusChoosed,
                  builder: (controller) => (controller.listSpMuaThem ?? [])
                          .isEmpty
                      ? Center(
                          child: Text(
                          '(Chưa có dữ liệu)',
                          style: AppTextStyle.s12
                              .copyWith(fontStyle: FontStyle.italic),
                        ))
                      : ListView.builder(
                          itemCount: (controller.listSpMuaThem ?? []).length,
                          physics: NeverScrollableScrollPhysics(),
                          shrinkWrap: true,
                          itemBuilder: (context, index) {
                            SpMuaThem? item = controller.listSpMuaThem?[index];
                            return InkWell(
                              splashColor: Colors.transparent,
                              onTap: () {
                                if (item == null) {
                                  return;
                                }
                                controller.onSelectInsBonus(item);
                              },
                              child: CheckBoxLineWidget(
                                content: Column(
                                  mainAxisSize: MainAxisSize.min,
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(item?.name ?? 'N/A',
                                        style: AppTextStyle.s14Medium
                                            .copyWith(color: AppColors.black)),
                                    const SizedBox(height: 5),
                                    Text(item?.description ?? 'N/A',
                                        style: AppTextStyle.s13Medium.copyWith(
                                            color: AppColors.black,
                                            fontStyle: FontStyle.italic)),
                                  ],
                                ),
                                isSelected: controller.listSpMuaThemChoosed
                                        .contains(item) ==
                                    true,
                                onChanged: (p0) {
                                  if (item == null) {
                                    return;
                                  }
                                  controller.onSelectInsBonus(item);
                                },
                              ),
                            );
                          },
                        )),
              GetBuilder<InsuranceLandStepProductController>(
                id: InsuranceLandStepProductControllerUpdateKey
                    .updateCouponChoosed,
                builder: (controller) => Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    CustomCommonDropdown<MaGiamGia>(
                      lable: 'Mã khuyến mại',
                      isRequired: false,
                      isErr: controller.errMaKm != null,
                      lableTextStyle: AppTextStyle.s14Medium
                          .copyWith(color: AppColors.black),
                      contentTextStyle: AppTextStyle.s14Medium
                          .copyWith(color: AppColors.black),
                      items: controller.listMaKM ?? [],
                      itemAsString: (p0) => p0.name ?? 'N/A',
                      value: controller.maKMChoosed,
                      onChanged: (MaGiamGia? newValue) {
                        if (newValue == null) {
                          return;
                        }
                        controller.updateCouponChoosed(newValue);
                      },
                    ),
                    controller.errMaKm != null
                        ? Padding(
                            padding: EdgeInsets.only(top: 2, left: 10),
                            child: Text(
                              controller.errMaKm ?? 'N/A',
                              style: AppTextStyle.s13Medium
                                  .copyWith(color: Colors.red),
                            ),
                          )
                        : const SizedBox()
                  ],
                ),
              ),
              const SizedBox(height: 5)
            ],
          ),
        ),
      ),
    );
  }
}
