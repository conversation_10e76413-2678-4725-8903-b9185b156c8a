import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:infinite_scroll_pagination/infinite_scroll_pagination.dart';
import 'package:loader_overlay/loader_overlay.dart';
import 'package:shopping/apis/ShoppingRepository.dart';
import 'package:shopping/app_services/api_service.dart';
import 'package:shopping/constants/enum_package_type.dart';
import 'package:shopping/loading_screen.dart';
import 'package:shopping/models/base_response.dart';
import 'package:shopping/models/home_model.dart';
import 'package:shopping/models/product.dart';

class InsuranceController extends GetxController {
  //input
  final PackageType? packageType;
  final HomeData? homeData;
  final BuildContext context;

  InsuranceController({this.packageType, this.homeData, required this.context});

  // đổi model bảo hiểm
  final PagingController<int, Product> pageController =
      PagingController(firstPageKey: 1);
  final int perPage = 50;

  // message lỗi
  Rx<String> messageFail = ''.obs;

  @override
  void onInit() {
    super.onInit();
    pageController.addPageRequestListener((pageKey) {
      getListData(pageKey: pageKey);
    });
  }

  Future getListData({int? pageKey}) async {
    context.loaderOverlay.show();
    BaseResponse<ProductsResponse> res;
    // mở từ top action
    if (homeData == null) {
      res = await DelayLoadingCallApi.delayCallApi(ShoppingRepository.instance
          .get_products2(type: 3, perPage: perPage, page: pageKey));
    } else {
      // mở từ config home
      res = await DelayLoadingCallApi.delayCallApi(ShoppingRepository.instance
          .get_products2(
              pageSettingId: homeData?.id,
              type: 3,
              perPage: perPage,
              page: pageKey));
    }
    context.loaderOverlay.hide();
    // nếu là trang đầu tiên -> clear data
    if (pageKey == 1) {
      pageController.itemList?.clear();
    }
    if (res.code == CODE_SUCCESS) {
      if (res.message != null && (res.message?.isNotEmpty ?? false)) {
        messageFail.value = res.message ?? '';
        pageController.itemList?.clear();
      } else {
        final List<Product>? listP = res.data?.items;
        final isLastPage = listP!.length < perPage;
        if (isLastPage) {
          pageController.appendLastPage(listP);
        } else {
          final nextPageKey = pageKey! + 1;
          pageController.appendPage(listP, nextPageKey);
        }
        messageFail.value = '';
      }
    }
    update([InsuranceControllerUpdateKey.updateListIns]);
  }
}

class InsuranceControllerUpdateKey {
  static const String updateListIns = '/update_list_insurance';
}
