import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:shopping/apis/ShoppingRepository.dart';
import 'package:shopping/app_services/app_storage.dart';
import 'package:shopping/constants/enum_filter_sim.dart';
import 'package:shopping/models/home_model.dart';
import 'package:shopping/models/list_sim_response.dart';
import 'package:shopping/pages/sim/item_sim_action.dart';

class SimPhoneNumberController extends GetxController {
  SimPhoneNumberController({this.homeData});
  final HomeData? homeData;
  // random SIM
  Rx<Offset> fabPosition = Offset(20, 20).obs;
  // filter
  List<SupplierType> listSupplierChoosed = [SupplierType.vinaphone];
  List<SupplierType> listSupplierTypeResult = [SupplierType.vinaphone];
  PhonenumberStartAt? phonenumberStartAtChoosed;
  PhonenumberStartAt? phonenumberStartAtResult;
  // List<PhonenumberStartAt> listPhonenumberStartAtChoosed = [];
  // List<PhonenumberStartAt> listPhonenumberStartAtResult = [];
  final double maxW = Get.size.width;
  final double maxH = Get.size.height;
  // search
  TextEditingController searchController = TextEditingController();
  FocusNode focusNode = FocusNode();
  String searchKeyWord = '';
  FocusNode searchFocusNode = FocusNode();
  // API list SIM
  List<ItemSim> listSim = [];
  final int perPage = 50;
  final int page = 1;
  //  loading
  Rx<bool> isLoading = false.obs;
  // mess err
  Rx<String> messErr = ''.obs;
  @override
  void onInit() {
    initData();
    super.onInit();
  }

  Future initData() async {
    _initFilter();
    //
    fabPosition.value = Offset(maxW - 200, maxH - 200);
    clearSearch();
    await _getListSim();
  }

  void _initFilter() {
    phonenumberStartAtResult = null;
    listSupplierTypeResult.clear();
    // mặc định nhà mạng là vinaphone
    listSupplierTypeResult.add(SupplierType.vinaphone);
  }

  void onInitDataToOpenFilter() {
    phonenumberStartAtChoosed = null;
    phonenumberStartAtChoosed = phonenumberStartAtResult;
    listSupplierChoosed.clear();
    listSupplierChoosed.addAll(listSupplierTypeResult);
    update([
      SimPhoneNumberControllerUpdateKey.updateListPhonenumberStartAtType,
      SimPhoneNumberControllerUpdateKey.updateListSupplierType
    ]);
  }

  bool isFilterSupplierSelected(SupplierType supplier) {
    return listSupplierChoosed.contains(supplier);
  }

  void onSelectFilterSupplier(SupplierType supplierType) {
    if (listSupplierChoosed.contains(supplierType)) {
      listSupplierChoosed.remove(supplierType);
    } else {
      listSupplierChoosed.add(supplierType);
    }
    update([SimPhoneNumberControllerUpdateKey.updateListSupplierType]);
  }

  void onSelectFilterPhonenumberStartAt(PhonenumberStartAt phonenumberStartAt) {
    if (phonenumberStartAtChoosed == phonenumberStartAt) {
      phonenumberStartAtChoosed = null;
    } else {
      phonenumberStartAtChoosed = phonenumberStartAt;
    }
    update(
        [SimPhoneNumberControllerUpdateKey.updateListPhonenumberStartAtType]);
  }

  Future onResetFilter() async {
    _initFilter();
    onInitDataToOpenFilter();
    // call API
    await _getListSim();
  }

  Future onSubmitFilter() async {
    // gán giá trị
    _initFilter();
    phonenumberStartAtResult = phonenumberStartAtChoosed;
    listSupplierTypeResult.addAll(listSupplierChoosed);
    // call API
    await _getListSim();
  }

  void onDragBtnRandom(Offset offset) {
    double tmpdx = offset.dx;
    double tmpdy = offset.dy;
    if (tmpdx < 0) {
      tmpdx = 0;
    } else if (tmpdx >= maxW - 200) {
      tmpdx = maxW - 200;
    }
    if (tmpdy < 0) {
      tmpdy = 0;
    } else if (tmpdy >= maxH - 200) {
      tmpdy = maxH - 200;
    }
    fabPosition.value = Offset(tmpdx, tmpdy);
  }

  Future onClickRandonSim({required BuildContext context}) async {
    if (isLoading.value) {
      return;
    }
    isLoading.toggle();
    var response = await ShoppingRepository.instance.getRandomSim();
    messErr.value = response?.message ?? '';
    isLoading.toggle();
    if (response?.code == 0) {
      ItemSim? simRandom = response?.data?.item;
      // set new value storage
      AppStorage.instance.setBool2(SKeysPK.isBookSimFromHome, false);
      ItemSimAction.openSheetBookSim(
        context: context,
        itemSim: simRandom,
        homeData: homeData,
      );
    }
  }

  void onTapSearch() async {
    searchKeyWord = searchController.text.trim();
    await _getListSim();
  }

  void clearSearch() {
    searchKeyWord = '';
    searchController.clear();
  }

  int? _getPrefix() {
    return phonenumberStartAtResult?.getValue ?? null;
  }

  Future _getListSim({int? page}) async {
    if (isLoading.value) {
      return;
    }
    // data filter
    int? prefix = _getPrefix();
    isLoading.toggle();
    var res = await ShoppingRepository.instance.getListSim(
        perPage: perPage, page: page, keyword: searchKeyWord, prefix: prefix);
    messErr.value = res?.message ?? '';
    if (res?.code == 0) {
      listSim = res?.data?.items ?? [];
    }
    update([SimPhoneNumberControllerUpdateKey.updateListSim]);
    isLoading.toggle();
  }
}

class SimPhoneNumberControllerUpdateKey {
  static const String updateListSim = 'sim_controller/update_list_sim';
  static const String updateListSupplierType =
      'sim_controller/update_list_supplier_type';
  static const String updateListPhonenumberStartAtType =
      'sim_controller/update_list_phone_number_startAt_type';
}
