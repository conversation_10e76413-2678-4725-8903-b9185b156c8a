import 'package:carousel_slider/carousel_slider.dart';
import 'package:flutter/material.dart';
import 'package:shopping/constants/app_assets_paths.dart';
import 'package:shopping/constants/app_colors.dart';

class BannerCarouselSliderWidget extends StatefulWidget {
  BannerCarouselSliderWidget({super.key, required this.listBanner});

  final List<String?>? listBanner;

  @override
  State<BannerCarouselSliderWidget> createState() =>
      BannerCarouselSliderWidgetState();
}

class BannerCarouselSliderWidgetState
    extends State<BannerCarouselSliderWidget> {
  int _current = 0;
  @override
  Widget build(BuildContext context) {
    // fix cứng banner
    List<String?> listBanner = widget.listBanner ?? [];
    return Stack(
      alignment: Alignment.center,
      children: [
        SizedBox(
          width: double.infinity,
          child: ClipRRect(
              child: CarouselSlider(
            items: listBanner.isEmpty
                ? [Image.asset(ImagePath.banner1)]
                : listBanner
                    .map(
                      (banner) => SizedBox(
                        width: double.infinity,
                        child: Image.network(
                          banner ?? '',
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) {
                            return Container(
                              width: double.infinity,
                              decoration: BoxDecoration(
                                image: DecorationImage(
                                  image: AssetImage(ImagePath.banner1),
                                  fit: BoxFit.fill,
                                ),
                              ),
                            );
                          },
                        ),
                      ),
                    )
                    .toList(),
            options: CarouselOptions(
                autoPlay: true,
                onPageChanged: (index, reason) {
                  setState(() {
                    _current = index;
                  });
                },
                autoPlayInterval: const Duration(seconds: 3),
                enlargeCenterPage: false,
                viewportFraction: 1,
                enableInfiniteScroll: true,
                height: double.infinity),
          )),
        ),
        Positioned(
            bottom: 8,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: listBanner.map((e) {
                var index = listBanner.indexOf(e);
                return GestureDetector(
                  child: Container(
                    width: 11,
                    height: 11,
                    margin: const EdgeInsets.symmetric(horizontal: 3.0),
                    decoration: BoxDecoration(
                      borderRadius: const BorderRadius.all(
                        Radius.circular(7),
                      ),
                      color: _current == index
                          ? AppColors.redCE3722
                          : AppColors.greyD9D9D9,
                    ),
                  ),
                );
              }).toList(),
            )),
      ],
    );
  }
}
