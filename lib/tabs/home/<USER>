import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:shopping/constants/app_colors.dart';
import 'package:shopping/constants/enum_package_type.dart';
import 'package:shopping/constants/page_type.dart';
import 'package:shopping/enum/home_type.dart';
import 'package:shopping/loading_screen.dart';
import 'package:shopping/pages/insurance/insurance_screen.dart';
import 'package:shopping/pages/package/sim_phone_number_screen.dart';
import 'package:shopping/tabs/home/<USER>';
import 'package:shopping/tabs/home/<USER>/search_home.dart';
import 'package:shopping/tabs/home/<USER>/hot_deal_widget.dart';
import 'package:shopping/tabs/home/<USER>/insurance_view.dart';
import 'package:shopping/tabs/home/<USER>/list_package_view.dart';
import 'package:shopping/tabs/home/<USER>/report_flag_widget.dart';
import 'package:shopping/widgets/banner_carousel_slider_widget.dart';

import '../../constants/app_assets_paths.dart';
import '../../pages/package/package_screen.dart';
import 'widgets/news_view.dart';
import 'widgets/top_action_widget.dart';

// ignore: must_be_immutable
class HomeTab extends StatelessWidget {
  var isShowNavigatorBar = false;

  HomeTab(
    this.isShowNavigatorBar, {
    Key? key,
  }) : super(key: key) {}
  @override
  Widget build(BuildContext context) {
    return GetBuilder<ShoppingHomeController>(
        init: ShoppingHomeController(0),
        builder: (controller) => Scaffold(
              backgroundColor: AppColors.white,
              body: RefreshIndicator(
                onRefresh: () => controller.loadContents(PageType.home),
                child: Stack(
                  children: [
                    SingleChildScrollView(
                      physics: const AlwaysScrollableScrollPhysics(),
                      controller: controller.scrollController,
                      child: _BuildMainContentView(controller: controller),
                    ),
                    Positioned(
                      height: 80,
                      top: 0,
                      left: 0,
                      right: 0,
                      child: Obx(
                        () => Container(
                          width: double.infinity,
                          decoration: BoxDecoration(
                            boxShadow: controller.isShowAppBar.value == false
                                ? []
                                : <BoxShadow>[
                                    BoxShadow(
                                        color: AppColors.black.withOpacity(0.4),
                                        blurRadius: 8.0,
                                        offset: Offset(0.0, 0.75))
                                  ],
                            color: controller.isShowAppBar.value == false
                                ? Colors.transparent
                                : Colors.white,
                          ),
                          child: Padding(
                            padding: EdgeInsets.symmetric(
                                horizontal: 16, vertical: 8),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              crossAxisAlignment: CrossAxisAlignment.end,
                              children: [
                                GestureDetector(
                                  onTap: () {
                                    Navigator.of(context).pop();
                                  },
                                  child: Container(
                                    height: 30,
                                    width: 30,
                                    decoration: BoxDecoration(
                                      color: Colors.white,
                                      shape: BoxShape.circle,
                                    ),
                                    child: Center(
                                      child: SvgPicture.asset(
                                          SvgPath.svgArrowBack,
                                          width: 13,
                                          height: 13,
                                          colorFilter: ColorFilter.mode(
                                              AppColors.black2E2E2E,
                                              BlendMode.srcIn)),
                                    ),
                                  ),
                                ),
                                Obx(
                                  () => controller.isShowAppBar.value
                                      ? SvgPicture.asset(
                                          SvgPath.svgIconLogoHome)
                                      : SizedBox(),
                                ),
                                GestureDetector(
                                  onTap: () {
                                    Navigator.of(context).push(
                                        MaterialPageRoute(
                                            builder: (context) =>
                                                SearchHome()));
                                  },
                                  child: Container(
                                    height: 30,
                                    width: 30,
                                    decoration: BoxDecoration(
                                      color: Colors.white,
                                      shape: BoxShape.circle,
                                    ),
                                    child: Center(
                                      child: IconButton(
                                        icon: SvgPicture.asset(
                                            SvgPath.svgIconSearch,
                                            width: 13,
                                            height: 13,
                                            colorFilter: ColorFilter.mode(
                                                AppColors.black2E2E2E,
                                                BlendMode.srcIn)),
                                        onPressed: () => Navigator.of(context)
                                            .push(MaterialPageRoute(
                                                builder: (context) =>
                                                    SearchHome())),
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ),
                    Obx(
                      () => controller.isShowBtnRdSim.value
                          ? Positioned(
                              left: controller.fabPosition.value.dx,
                              top: controller.fabPosition.value.dy,
                              child: GestureDetector(
                                onTap: () async {
                                  await controller.onClickRandonSim(
                                      context: context);
                                },
                                child: Draggable(
                                  onDragEnd: (details) {
                                    controller.onDragBtnRandom(details.offset);
                                  },
                                  feedback: SvgPicture.asset(SvgPath.svgRandom),
                                  child: SvgPicture.asset(SvgPath.svgRandom),
                                ),
                              ))
                          : SizedBox(),
                    ),
                    Obx(() => controller.isLoadingRandomSIM.value
                        ? LoadingWidget()
                        : SizedBox())
                  ],
                ),
              ),
            ));
  }
}

class _BuildMainContentView extends StatelessWidget {
  const _BuildMainContentView({required this.controller});
  final ShoppingHomeController controller;

  @override
  Widget build(BuildContext context) {
    return Padding(
        padding: const EdgeInsets.symmetric(horizontal: 15),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            SizedBox(
              width: MediaQuery.of(context).size.width,
              height: 210,
              child: OverflowBox(
                maxWidth: MediaQuery.of(context).size.width,
                child: Container(
                  width: double.infinity,
                  child: GetBuilder<ShoppingHomeController>(
                    id: HomeControllerUpdateKey.updateHomePackage,
                    builder: (controller) => BannerCarouselSliderWidget(
                      listBanner: controller.listLinkBanner,
                    ),
                  ),
                  // decoration: BoxDecoration(
                  //   // color: Colors.blue,
                  //   borderRadius: BorderRadius.circular(30),
                  //   image: DecorationImage(
                  //     image: AssetImage(
                  //       'packages/shopping/assets/images/bg_banner.png',
                  //     ),
                  //     fit: BoxFit.fill,
                  //   ),
                  // ),
                ),
              ),
            ),
            const SizedBox(height: 20),

            SizedBox(
              width: MediaQuery.of(context).size.width,
              height: 87,
              child: OverflowBox(
                maxWidth: MediaQuery.of(context).size.width,
                child: SizedBox(
                  height: 87,
                  // width: double.infinity,
                  child: ListView.builder(
                    physics: const ClampingScrollPhysics(),
                    scrollDirection: Axis.horizontal,
                    shrinkWrap: true,
                    padding: EdgeInsets.zero,
                    itemCount: controller.listTopAction.length,
                    itemBuilder: (_, index) {
                      return Container(
                        width: (MediaQuery.of(context).size.width - 30) / 5 < 88
                            ? 88
                            : (MediaQuery.of(context).size.width - 30) / 5,
                        child: GestureDetector(
                          onTap: () {
                            controller.listTopAction[index] ==
                                    PackageType.insuranceABIC
                                ? Navigator.push(
                                    context,
                                    MaterialPageRoute(
                                        builder: (context) => InsuranceScreen(
                                            packageType:
                                                PackageType.insuranceABIC)))
                                : controller.listTopAction[index] ==
                                        PackageType.sim
                                    ? Navigator.push(
                                        context,
                                        MaterialPageRoute(
                                          settings: RouteSettings(
                                              name: "/page_list_sim"),
                                          builder: (context) =>
                                              SimPhoneNumberScreen(),
                                        ),
                                      )
                                    : Navigator.push(
                                        context,
                                        MaterialPageRoute(
                                          builder: (context) => PackageScreen(
                                            packageType:
                                                controller.listTopAction[index],
                                          ),
                                        ),
                                      );
                          },
                          child: TopActionWidget(
                            actionName:
                                controller.listTopAction[index].nameTypePackage,
                            iconPath: controller.listTopAction[index].icon,
                            isHotDeal: controller.listTopAction[index] ==
                                PackageType.hotDeal,
                          ),
                        ),
                      );
                    },
                  ),
                ),
              ),
            ),
            const SizedBox(height: 28),
            GetBuilder<ShoppingHomeController>(
                id: HomeControllerUpdateKey.updateHomePackage,
                builder: (controller) => Obx(
                      () => controller.isLoading.value
                          ? LoadingWidget()
                          // check list SIM
                          : ListView.separated(
                              padding: EdgeInsets.zero,
                              shrinkWrap: true,
                              physics: const NeverScrollableScrollPhysics(),
                              itemCount: controller.listHomeData.length,
                              itemBuilder: (context, index) {
                                var homeData = controller.listHomeData[index];
                                return homeData.getHomeType == HomeType.sim
                                    ? GetBuilder<ShoppingHomeController>(
                                        id: HomeControllerUpdateKey
                                            .updateListSIM,
                                        builder: (controller) => Obx(() =>
                                            ListSimView(
                                                isShimmer: controller
                                                    .isLoadingSIM.value,
                                                homeData: homeData,
                                                listSim: controller
                                                    .mapListSim[homeData.id!])))
                                    : homeData.getHomeType == HomeType.product
                                        ? ListPackageView(homeData: homeData)
                                        : homeData.getHomeType ==
                                                HomeType.hotDeal
                                            ? HotDealWidget(homeData: homeData)
                                            : homeData.getHomeType ==
                                                    HomeType.reportFlag
                                                ? Obx(() => ReportFlagWidget(
                                                    homeData: homeData,
                                                    reportFlag:
                                                        controller.reportFlag,
                                                    isLoading: controller
                                                        .isLoadingReport.value))
                                                : homeData.getHomeType ==
                                                        HomeType.abic
                                                    ? InsuranceView(
                                                        homeData: homeData)
                                                    : const SizedBox();
                              },
                              separatorBuilder: (_, index) => SizedBox(
                                  height: index + 1 ==
                                          controller.listHomeData.length
                                      ? 0
                                      : 28),
                            ),
                    )),
            //News
            NewsView(),
            SizedBox(height: 20)
          ],
        ));
  }
}
