import 'dart:async';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:shopping/apis/ShoppingRepository.dart';
import 'package:shopping/app_services/app_storage.dart';
import 'package:shopping/enum/enum_payment_type.dart';
import 'package:shopping/models/base_response.dart';
import 'package:shopping/models/buy_package.dart';
import 'package:shopping/models/product.dart';
import 'package:shopping/models/validate_phone_number_response.dart';
import 'package:shopping/pages/package/package_controller.dart';
import 'package:shopping/tabs/home/<USER>/search_home_controller.dart';
import 'package:shopping/tabs/home/<USER>/payment_screen.dart';
import 'package:shopping/tabs/order/order_controller.dart';
import 'package:shopping/utils/convert/validator_util.dart';
import 'package:shopping/widgets/app_dialog.dart';

class BuyPackageController extends GetxController {
  final phoneController = TextEditingController();
  final referralCodeController = TextEditingController();
  final otpController = TextEditingController();

  //send server
  var isSendServer = false.obs;

  final phoneFocusNode = FocusNode();
  var focusPhone = Rxn(false);
  int orderId = 0;
  String messageValidate = "";
  String orderCode = "";

  Timer? timer, timerDelay;

  var timeOtp = 180.obs;
  var delay = 1800.obs;

  int countFailOtp = 0;
  int maxFailOtp = 5;

  int countOtp = 0;
  int maxOtp = 5;
  int timeStart = 0;

  var enablePin = Rxn(true);
  var disableTimeOtp = Rxn(true);
  var disableResend = Rxn(true);
  var disableBtnResend = Rxn(true);

  var rs = true.obs;
  var isCountDown = true.obs;
  // online
  PaymentType paymentTypeChoosed = PaymentType.tkc;

  void onChangePayment(PaymentType value) {
    if (paymentTypeChoosed != value) {
      paymentTypeChoosed = value;
      update(["updatePaymentType"]);
    }
  }

  bool isSelected(PaymentType value) {
    return paymentTypeChoosed == value;
  }

  Future onDonePickPaymentType(
      {required BuildContext context,
      required Product product,
      required Function(int?) openOTP,
      required String phoneNumber,
      required String referCode}) async {
    if (paymentTypeChoosed == PaymentType.tkc) {
      // thanh toán bằng tài khoản chính
      setupLayout(context);
      buyPackage(context, phoneNumber, product.id!, referCode, 0, openOTP);
    } else {
      await buyPackageOnline(
          isFromSheetPick: true,
          context: context,
          phone: phoneNumber,
          productId: product.id ?? 0,
          referCode: referCode);
    }
  }

  // online
  void startTimerOtp() {
    timeOtp = 180.obs;
    Timer.periodic(
      const Duration(seconds: 1),
      (Timer t) {
        if (timer == null) {
          timer = t;
        }
        if (timeOtp.value == 0) {
          t.cancel();

          disableResend.value = true;
          disableTimeOtp.value = false;
          enablePin.value = false;
        } else {
          timeOtp--;
          print(timeOtp.value);
        }
      },
    );
    update();
  }

  bool isCountDownOtp() {
    return timeOtp.value > 0;
  }

  void setResend(bool? r) {
    rs.value = r ?? false;
    print('set resend $r and ${rs.value}');
  }

  void setupLayout(BuildContext context) {
    int timeNow = (DateTime.now().millisecondsSinceEpoch ~/ 1000);

    if (AppStorage.instance.getInt2(SKeysPK.timeStart) != null) {
      int timeValidity =
          timeNow - AppStorage.instance.getInt2(SKeysPK.timeStart)!;
      if (timeValidity <= 1800) {
        enablePin.value = false;
        disableTimeOtp.value = false;
        disableResend.value = false;
        startTimeDelay(1800 - timeValidity);
        if (AppStorage.instance.getInt2(SKeysPK.countOtp)! == maxOtp) {
          //  dialogAsk(context, "Vượt quá số lượng OTP. Vui lòng thử lại sau");
          return;
        }
      } else {
        sendOTP();
      }
    } else {
      print("send OTP");
      sendOTP();
    }
  }

  void startTimeDelay(int time) {
    delay = time.obs;
    timerDelay = Timer.periodic(
      const Duration(seconds: 1),
      (Timer timer) {
        if (delay.value == 0) {
          timer.cancel();
          // countOtp = AppStorage().getInt(SKeys.countOtp)!;
          debugPrint('count otp in delay: $countOtp');
          if (countOtp == maxOtp) {
            AppStorage.instance.removeString2(SKeysPK.countOtp);
            countOtp = 0;
          }
          //countFailOtp = 0;
          //countResend = 0;
          enablePin.value = true;
          disableResend.value = true;
          disableBtnResend.value = true;
          disableTimeOtp.value = false;
          AppStorage.instance.removeString2(SKeysPK.timeStart);
          debugPrint('end time delay');
        } else {
          delay--;
          // debugPrint('delay time: $delay');
        }
      },
    );
    update();
  }

  @override
  void onInit() {
    super.onInit();
    timer?.cancel();
    timerDelay?.cancel();
    String strPhone = Get.isRegistered<PackageController>()
        ? Get.find<PackageController>().searchKeyWord
        : Get.isRegistered<SearchHomeController>()
            ? Get.find<SearchHomeController>().keyword.value
            : '';
    phoneController.text =
        (ValidatorUlti.isPhoneNumber(strPhone.trim())) ? strPhone.trim() : '';
    print("BuyPackageController init");
  }

  @override
  void onReady() {
    super.onReady();
    phoneController.addListener(() {});
    referralCodeController.addListener(() {});
    phoneFocusNode.addListener(() {
      focusPhone.value = phoneFocusNode.hasFocus;
    });
  }

  @override
  void onClose() {
    super.onClose();
    timer?.cancel();
    timerDelay?.cancel();
    isSendServer.value = false;
  }

  void sendOTP() {
    if (AppStorage.instance.getInt2(SKeysPK.countOtp) != null) {
      countOtp = AppStorage.instance.getInt2(SKeysPK.countOtp)!;
    }
    disableResend.value = false;
    if (countOtp != maxOtp) {
      // startTimerOtp();
      countOtp++;
      print(countOtp);
      AppStorage.instance.setInt2(SKeysPK.countOtp, countOtp);
    } else {
      // ToastUtils.showSuccess("Vượt quá số lượng OTP. Vui lòng thử lại sau");
      disableTimeOtp.value = false;
      disableBtnResend.value = false;
      enablePin.value = false;
      AppStorage.instance.setInt2(
          SKeysPK.timeStart, DateTime.now().millisecondsSinceEpoch ~/ 1000);
      print(DateTime.now().millisecondsSinceEpoch ~/ 1000);
      startTimeDelay(1800);
    }
    update();
  }

  checkValidateBuyPackage(
      {required BuildContext context,
      required Product product,
      required Function(int?) openOTP,
      required Function({required BuildContext ctx}
              // {required Function() funcPayOnline,
              // required Function() funcPayTKC}
              )
          openSheetPickPaymentType}) async {
    if (phoneController.text.isEmpty) {
      dialogAsk(context, 'Số điện thoại không được để trống.');
      return;
    }
    if (phoneController.text.length < 9 || phoneController.text.length > 12) {
      dialogAsk(context, 'Số điện thoại từ 9 đến 12 ký tự');
      return;
    }
    String number = ValidatorUlti.normalizePhoneNumber(phoneController.text);
    if (!ValidatorUlti.isPhoneNumber(number)) {
      dialogAsk(context, 'Số điện thoại không đúng định dạng.');
      return;
    }
    // check type thanh toán
    // nếu chỉ có 1 kiểu là 'tài khoản chính' => đi theo luồng cũ -> buyPackage (gọi api validate-buy-package-di-dong lấy ordId và mã OTP) -> check OTP -> done
    // nếu chỉ có 1 kiểu thanh toán là 'QR, ATM' => luồng mới
    // nếu có 2 kiểu thanh toán => mở sheet pick type thanh toán => chọn phương thức thanh toán mong muốn và quay lại 2 bước trên
    if (product.isOnlyPayTKC) {
      // thanh toán bằng tài khoản chính
      setupLayout(context);
      buyPackage(context, phoneController.text, product.id!,
          referralCodeController.text, 0, openOTP);
    } else if (product.isOnlyPayOnline) {
      // thanh toán bằng QR, ATM
      await buyPackageOnline(
          isFromSheetPick: false,
          context: context,
          phone: phoneController.text.trim(),
          productId: product.id ?? 0,
          referCode: referralCodeController.text.trim());
    } else {
      // open sheet pick phương thức thanh toán
      openSheetPickPaymentType.call(ctx: Get.context!
          //   funcPayOnline: () async {
          //   await buyPackageOnline(
          //       isFromSheetPick: true,
          //       context: Get.context!,
          //       phone: phoneController.text.trim(),
          //       productId: product.id ?? 0,
          //       referCode: referralCodeController.text.trim());
          // }, funcPayTKC: () async {
          //   // thanh toán bằng tài khoản chính
          //   setupLayout(Get.context!);
          //   buyPackage(Get.context!, phoneController.text, product.id!,
          //       referralCodeController.text, 0, openOTP);
          // }
          );
    }
  }

  Future buyPackageOnline(
      {required BuildContext context,
      required String phone,
      required int productId,
      required String referCode,
      bool? isFromSheetPick}) async {
    // step 1:  api check số điện thoại
    isSendServer.value = true;
    BaseResponse<ValidatePhonenumberResponse> res =
        await ShoppingRepository.instance.getValidateMobile(phoneNumber: phone);
    if (res.code == 0) {
      isSendServer.value = false;
      // case không phải số vinaphone
      if (res.data?.isMobile == false) {
        dialogAsk(context, 'Số điện thoại phải là thuê bao Vinaphone');
        return;
      } else {
        // step 2: rest api tạo đơn -> để lấy ordId
        isSendServer.value = true;
        phoneFocusNode.unfocus();
        BaseResponse<BuyPackage> res = await ShoppingRepository.instance
            .postOrderBuyPackage2(phone, productId, referCode, 0,
                paymentType: PaymentType.tk0d);
        if (res.code == 0) {
          isSendServer.value = false;
          int? ordId = res.data?.orderId;
          // tắt sheet nhập số điện thoại
          if (isFromSheetPick == false) Navigator.pop(context);
          // chuyển sang màn thanh toán online
          Navigator.of(context).push(MaterialPageRoute(
              builder: (context) => PaymentScreen(
                    ordId: ordId,
                    isFromSheetPick: isFromSheetPick,
                    userPhoneNumber: phone,
                  )));
        } else {
          // lỗi khi tạo đơn lấy ordId
          isSendServer.value = false;
          dialogAsk(context, res.message);
        }
      }
    } else {
      // lỗi khi check sđt
      isSendServer.value = false;
      dialogAsk(context, res.message);
    }
  }

  checkValidateOtp(
      BuildContext context,
      int ordId,
      Function(String?, String?) openBuySuccess,
      Function(String?, String?) openBuyFail) {
    if (isSendServer.value == true) {
      return;
    }
    if (otpController.text.isEmpty) {
      dialogAsk(context, 'Mã xác nhận không được để trống');
      return;
    }
    validateBuyPackage(
        context, ordId, otpController.text, openBuySuccess, openBuyFail);
    // update();
  }

  void buyPackage(BuildContext context, String phone, int productId,
      String referCode, int platform, Function(int?) openOTP) async {
    isSendServer.value = true;
    var res = await ShoppingRepository.instance.postOrderBuyPackage2(
        phone, productId, referCode, platform,
        paymentType: PaymentType.tkc);
    if (res.code == 0) {
      isSendServer.value = false;
      orderId = res.data!.orderId!;
      sendOTP();
      openOTP.call(orderId);
    } else {
      isSendServer.value = false;
      dialogAsk(context, res.message);
    }
    update();
  }

  void validateBuyPackage(
      BuildContext context,
      int orderId,
      String otp,
      Function(String?, String?) openBuySuccess,
      Function(String?, String?) openBuyFail) async {
    isSendServer.value = true;
    var res =
        await ShoppingRepository.instance.postValidateBuyPackage2(orderId, otp);
    //kiem tra xem connect server thanh cong hay chua
    bool isConnectServerSuccess = false;
    if (res.data != null) {
      isConnectServerSuccess = true;
      isSendServer.value = false;
      orderCode = res.data!.orderCode!;
      messageValidate = res.data!.messages ?? "";
    } else {
      isConnectServerSuccess = false;
      isSendServer.value = false;
      messageValidate = res.message ?? "";
    }
    if (res.code == 0) {
      isSendServer.value = false;
      if (res.data != null) {
        openBuySuccess.call(orderCode, messageValidate);
        Get.find<OrderController>().changePage(0);
      } else {
        dialogAsk(context, messageValidate);
      }
    } else {
      isSendServer.value = false;
      if (res.code == 20007) {
        dialogAsk(context, res.message);
      } else if (res.code == 20008) {
        dialogAsk(context, res.message);
      } else {
        //neu khong connect server thanh cong thi thong bao loi he thong
        if (!isConnectServerSuccess) {
          dialogAsk(context, messageValidate);
        } else {
          openBuyFail.call(orderCode, messageValidate);
          Get.find<OrderController>().changePage(0);
        }
      }
    }
    update(['buy_package_loading']);
  }

  void resendOTP(BuildContext context, int orderId) async {
    var res = await ShoppingRepository.instance.postResendOtp2(orderId);
    if (res.code == 0) {
    } else {
      if (res.code == 30006) {
        // disableResend.value = false;
        // disableBtnResend.value = false;
        isCountDown.value = false;
        dialogAsk(context, res.message, isExit: true);
        return;
      }
      dialogAsk(context, res.message);
    }
    update();
  }
}
