import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:infinite_scroll_pagination/infinite_scroll_pagination.dart';
import 'package:shopping/models/order.dart';
import 'package:shopping/tabs/order/order_controller.dart';

import '../../../widgets/app_loading_widget.dart';
import 'empty_order_widget.dart';
import 'order_widget.dart';

class FailOrderTab extends GetView<OrderController> {
  @override
  Widget build(BuildContext context) {
    return PagedListView.separated(
      keyboardDismissBehavior: ScrollViewKeyboardDismissBehavior.onDrag,
      pagingController: controller.faController,
      builderDelegate: PagedChildBuilderDelegate<Order>(
        itemBuilder: (c, i, index) => OrderWidget(order: i),
        noItemsFoundIndicatorBuilder: (context) => EmptyOrderWidget(),
        firstPageErrorIndicatorBuilder: (context) => AppLoadingWidget(),
        newPageProgressIndicatorBuilder: (context) => AppLoadingWidget(),
      ),
      separatorBuilder: (c, i) => SizedBox(
        height: 20,
      ),
    );
  }
}
