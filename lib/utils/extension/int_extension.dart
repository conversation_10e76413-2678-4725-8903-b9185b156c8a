import 'package:intl/intl.dart';

extension IntExtension on int? {
  String convertToVietnamesMoney() {
    if (this == null) {
      return '0';
    }
    final formatter = NumberFormat("#,###", "vi_VN");
    return formatter.format(this);
  }

  String get getDeepLink {
    switch (this) {
      case 1: // app HND
        return 'hnd://miniapp/muasam';
      case 2: // app TNVN
        return 'tnvn://miniapp/muasam';
      default:
        return 'hnd://miniapp/muasam';
    }
  }
}

class ConvertMoney {
  static String convertVNDMoney(num? number) {
    if (number == null) {
      return '0';
    }
    final formatter = NumberFormat.currency(locale: "vi_VN", decimalDigits: 4);
    var _r = formatter.format(number);
    if (_r.contains(',0')) {
      return _r.split(',0')[0];
    } else {
      return _r.replaceAll('VND', '');
    }
  }
}
