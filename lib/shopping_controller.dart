import 'package:flutter/cupertino.dart';
import 'package:get/get.dart';
import 'package:shared_preferences/shared_preferences.dart';

import 'package:shopping/apis/ShoppingRepository.dart';
import 'package:shopping/app_services/api_service.dart';
import 'package:shopping/app_services/log.dart';


import 'app_services/app_storage.dart';
import 'models/user_Info.dart';

enum ShopState {
  INIT,
  S_ERROR,
  S_ERROR_NO_INTENET,
  USER_NOT_ACCEPT_ACCESS,
  LOGIN_OK,
}

int CODE_USER_NOT_ACCEPT_ACCPET_PROFILE = 10023;

class ShoppingController extends GetxController {
  var shopState = ShopState.INIT.obs;
  var firstLogin = ''.obs;
  int show_product_id;
  int? appId;
  UserInfo? user;
  var currentIndex = 0.obs;
  //biến lắng nghe người dùng có click để cho cấp quyền hay không ?(dùng trong trường hợp chưa cấp quyền)
  var isAcceptPermisstion = true.obs;

  List<Rx<bool>>? listPermission = [true.obs, true.obs, true.obs];

  CupertinoTabController? c = CupertinoTabController();
  SharedPreferences? s;
  ShoppingController(this.show_product_id, {this.appId});
  @override
  void onInit() async {
    super.onInit();
    isAcceptPermisstion = false.obs;
    s = await SharedPreferences.getInstance();
    firstLogin.value = s?.getString(SKeysPK.firstLogin) ?? '';
    // remove storage
    AppStorage.instance.removeString2(SKeysPK.isBookSimFromHome);
  }

  void setAcceptPermission(bool b) {
    isAcceptPermisstion.value = b;
    if (b == false) {
      currentIndex.value == 0;
    }
    update();
  }

  void setCurrentIndex(int index) {
    currentIndex.value = index;
  }

  @override
  void onReady() async {
    super.onReady();
    update_onload();
  }

  void onReload(String token) {
    update_onload();
  }
//
  // void setFirstLogin(String b) async {
  //   // firstLogin.value = b;

  //   s?.setString(SKeys.firstLogin, b);
  //   // print(AppStorage().getString(SKeys.firstLogin))
  //   print(s?.getString(SKeys.firstLogin));
  //   firstLogin.value = b;
  //   c?.index = 0;
  //   update();
  // }

  void permissionAccept() async {
    final res = await ShoppingRepository.instance.sso_vdcp_access2();
    if (res.code == 0) {
      this.shopState.value = ShopState.LOGIN_OK;
      isAcceptPermisstion.value = true;
      // lưu token acc mới
      ApiService.instance.jwt_token2 = res.data?.accessToken ?? "";
    }
  }

  void update_onload() async {
    print("update_onload");
    this.shopState.value = ShopState.INIT;
    var res = await ShoppingRepository.instance.sso2();
    if (res.code == CODE_SUCCESS) {
      ApiService.instance.jwt_token2 = res.data?.accessToken ?? "";
      print(ApiService.instance.jwt_token2);
      user = res.data?.userInfo;
      if (res.duration < 1500) {
        Future.delayed(
          Duration(milliseconds: 1500 - res.duration),
          () {
            // this.shopState.value = ShopState.USER_NOT_ACCEPT_ACCESS;
            this.shopState.value = ShopState.LOGIN_OK;
            UDPLog.instance.setUserID("${res.data?.userInfo?.id ?? 0}");
          },
        );
      } else {
        this.shopState.value = ShopState.LOGIN_OK;

        UDPLog.instance.setUserID("${res.data?.userInfo?.id ?? 0}");
      }
    } else if (res.code == CODE_USER_NOT_ACCEPT_ACCPET_PROFILE) {
      this.shopState.value = ShopState.USER_NOT_ACCEPT_ACCESS;
    } else {
      print("SSO Error ${res.message}");
      if (res.code == CODE_NO_INTERNET) {
        this.shopState.value = ShopState.S_ERROR_NO_INTENET;
        // } else if (res.statusCode == 102) {
        //   // chưa cấp quyền từ ứng dụng gốc
        //   this.shopState.value = ShopState.USER_NOT_ACCEPT_ACCESS;
      } else {
        this.shopState.value = ShopState.S_ERROR;
      }
    }
    // 10023 // chưa cấp quyền truy cập
  }

  void updateUserInfor(UserInfo? newValue) {
    user = newValue;
  }
}
