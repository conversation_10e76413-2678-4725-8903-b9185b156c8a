import 'dart:convert';
import 'dart:io';

import 'package:dio/dio.dart' as dio;
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';
import 'package:path/path.dart' as p;
import 'package:shopping/apis/shop_end_points.dart';
import 'package:shopping/app_services/api_service.dart';
import 'package:shopping/app_services/log.dart';
import 'package:shopping/enum/enum_payment_type.dart';
import 'package:shopping/models/SSO.dart';
import 'package:shopping/models/base_response.dart';
import 'package:shopping/models/buy_package.dart';
import 'package:shopping/models/buy_sim_models.dart';
import 'package:shopping/models/home_model.dart';
import 'package:shopping/models/insurance_create_order_request_form.dart';
import 'package:shopping/models/insurance_create_order_response.dart';
import 'package:shopping/models/insurance_filter_response.dart';
import 'package:shopping/models/insurance_get_price_form_request.dart';
import 'package:shopping/models/insurance_get_price_response.dart';
import 'package:shopping/models/list_bank_infor_response.dart';
import 'package:shopping/models/list_sim_response.dart';
import 'package:shopping/models/order.dart';
import 'package:shopping/models/order_detail.dart';
import 'package:shopping/models/payment_infor_response.dart';
import 'package:shopping/models/pgd_address.dart';
import 'package:shopping/models/product.dart';
import 'package:shopping/models/report_flag_response.dart';
import 'package:shopping/models/sim_model.dart';
import 'package:shopping/models/sim_order_response.dart';
import 'package:shopping/models/sim_random_response.dart';
import 'package:shopping/models/site.dart';
import 'package:shopping/models/user_Info.dart';
import 'package:shopping/models/validate_phone_number_response.dart';

import '../models/province.dart';

class ShoppingRepository extends BaseRepo {
  late String vdcp_token;
  init(String _vdcp_token) {
    this.vdcp_token = _vdcp_token;
  }

  ShoppingRepository._privateConstructor();

  static final ShoppingRepository instance =
      ShoppingRepository._privateConstructor();
  // Future<ShoppingRepository> init(String _vdcp_token) async {
  //   this.vdcp_token = _vdcp_token;
  //   return this;
  // }

  Future<BaseResponse<Site>> get_site2() async {
    try {
      final response =
          await ApiService.instance.get2(url: ShopEndPoints.get_site);
      var res = BaseResponse<Site>(response.data, Site.fromJson);
      return res;
    } catch (e) {
      return BaseResponse<Site>.withError(
          CODE_ERROR, ApiService.instance.handleError2(e));
    }
  }

  Future<BaseResponse<SSOData>> sso2() async {
    try {
      var data = json.encode({"vdcp_token": this.vdcp_token});
      final response = await ApiService.instance
          .post2(url: ShopEndPoints.post_sso, data: data);
      var res = BaseResponse<SSOData>(response, SSOData.fromJson);
      return res;
    } catch (e) {
      return BaseResponse<SSOData>.withError(
          CODE_ERROR, ApiService.instance.handleError2(e));
    }
  }

  Future<BaseResponse<SSOData>> sso_vdcp_access2() async {
    try {
      var data = json.encode({
        "vdcp_token": this.vdcp_token,
        "access_info": ["full_name", "phone_n_email"]
      });
      final response = await ApiService.instance
          .post2(url: ShopEndPoints.post_vdcp_access, data: data);

      var res = BaseResponse<SSOData>(response, SSOData.fromJson);
      return res;
    } catch (e) {
      return BaseResponse<SSOData>.withError(
          CODE_ERROR, ApiService.instance.handleError2(e));
    }
  }

  Future<AddressData> get_address_info2({int? index, int? id}) async {
    try {
      final response;
      switch (index) {
        case 0:
          {
            response = await ApiService.instance
                .get2(url: ShopEndPoints.get_city_info);
          }
        case 1:
          {
            response = await ApiService.instance
                .get2(url: ShopEndPoints.get_district_infor(id ?? 0));
          }
        case 2:
          {
            response = await ApiService.instance
                .get2(url: ShopEndPoints.get_ward_infor(id ?? 0));
          }
        case 3:
          {
            response = await ApiService.instance
                .get2(url: ShopEndPoints.get_street_infor(id ?? 0));
          }
        default:
          {
            response = await ApiService.instance
                .get2(url: ShopEndPoints.get_city_info);
          }
      }
      // final response = await _apis.get(url: ShopEndPoints.get_city_info);
      var res = AddressData.fromJson(response.data);
      Get.log(res.toString());
      return res;
    } catch (e) {
      return AddressData();
    }
  }

// package
  Future<BaseResponse<ProductsResponse>> get_products2({
    String? keyword,
    int? group,
    int? type,
    List<int>? prices,
    int? isHotDeal,
    List<int>? cycles,
    String? orderBy,
    int? perPage,
    int? page,
    int? pageSettingId,
  }) async {
    try {
      Map<String, dynamic> param = {};
      if (group != null) {
        param.addAll({"group": group});
      }
      if (type != null) {
        param.addAll({"type": type});
      }
      if (isHotDeal != null) {
        param.addAll({"is_hot_deal": isHotDeal});
      }
      if (keyword != null) {
        param.addAll({"keyword": keyword});
      }
      if (cycles != null && cycles.isNotEmpty) {
        param.addAll({"cycle[]": List<int>.from(cycles.map((x) => x))});
      }
      if (prices != null && prices.isNotEmpty) {
        param.addAll({"price[]": List<int>.from(prices.map((x) => x))});
      }
      if (perPage != null) {
        param.addAll({"per-page": perPage});
      }
      if (page != null) {
        param.addAll({"page": page});
      }
      if (orderBy != null) {
        param.addAll({"order_by": orderBy});
      }
      if (pageSettingId != null) {
        param.addAll({"page_setting_id": pageSettingId});
      }
      final response = await ApiService.instance
          .get2(url: ShopEndPoints.get_products, queryParameters: param);
      var res =
          BaseResponse<ProductsResponse>(response, ProductsResponse.fromJson);
      return res;
    } catch (e) {
      return BaseResponse<ProductsResponse>.withError(
          CODE_ERROR, ApiService.instance.handleError2(e));
    }
  }

  //User
  Future<BaseResponse<UserInfo>> getUserInfo2() async {
    final header = {
      'X-Api-Key': ApiService.instance.ApiKey2,
      'Authorization': 'Bearer ${ApiService.instance.jwt_token2}',
      'PROFILE_ID': ApiService.instance.profileIdTCCT,
      // 'Content-Type': 'multipart/form-data',
    };
    try {
      final response = await ApiService.instance
          .get2(url: ShopEndPoints.info, options: dio.Options(headers: header));
      var res = BaseResponse<UserInfo>(response, UserInfo.fromJson);
      return res;
    } catch (e) {
      return BaseResponse<UserInfo>.withError(
          CODE_ERROR, ApiService.instance.handleError2(e));
    }
  }

  Future<BaseResponse<UserInfo>> updateUserInfor2(UserInfo? user) async {
    final header = {
      'X-Api-Key': ApiService.instance.ApiKey2,
      'Authorization': 'Bearer ${ApiService.instance.jwt_token2}',
      'PROFILE_ID': ApiService.instance.profileIdTCCT,
      // 'Content-Type': 'multipart/form-data',
    };
    try {
      final response = await ApiService.instance.put2(
        url: ShopEndPoints.update_info,
        data: user?.toJsonUpdate(),
        options: dio.Options(headers: header),
      );
      var res = BaseResponse<UserInfo>(response, UserInfo.fromJson);
      return res;
    } catch (e) {
      return BaseResponse<UserInfo>.withError(
          CODE_ERROR, ApiService.instance.handleError2(e));
    }
  }

  Future<String?> uploadAvatar2(XFile path) async {
    var form = dio.FormData.fromMap({
      'file': dio.MultipartFile.fromBytes((await path.readAsBytes()),
          filename: ' ${p.basename(path.path)}.jpeg'),
    });
    final header = {
      'X-Api-Key': ApiService.instance.ApiKey2,
      'Authorization': 'Bearer ${ApiService.instance.jwt_token2}',
      'Content-Type': 'multipart/form-data',
      'PROFILE_ID': ApiService.instance.profileIdTCCT,
    };

    try {
      final response = await ApiService.instance.post2(
          url: ShopEndPoints.upload_avatar,
          data: form,
          options: dio.Options(headers: header));
      if (response.statusCode == 200) {
        print(response.data['data']['file_path']);
        return response.data['data']['file_path'];
      } else {
        return '';
      }
    } catch (e) {
      return '';
    }
  }

  // home
  Future<HomeModelResponse?> getHome2() async {
    try {
      final response = await ApiService.instance.get2(url: ShopEndPoints.home);
      int code = response.data[RESPONSE_CODE];
      if (code == 0) {
        return HomeModelResponse.fromJson(response.data);
      }
      return null;
    } catch (e) {
      return HomeModelResponse.withError(
          CODE_ERROR, ApiService.instance.handleError2(e));
    }
  }

  // home_banner
  Future<List<String?>?> getHomeBanner() async {
    try {
      final response =
          await ApiService.instance.get2(url: ShopEndPoints.homeBanner);
      if (response.statusCode == HttpStatus.ok) {
        if (response.data[RESPONSE_CODE] == CODE_SUCCESS) {
          if (response.data[RESPONSE_DATA] == null) {
            return null;
          }
          return List<String>.from(response.data[RESPONSE_DATA]!.map((x) => x));
        }
      }
      return null;
    } catch (e) {
      return null;
    }
  }

  // list sim
  Future<BaseResponse<ListSimResponse>?> getListSim(
      {String? keyword, int? page, int? perPage, int? prefix}) async {
    try {
      Map<String, dynamic> param = {};
      if (keyword != null) {
        param.addAll({"keyword": keyword});
      }
      if (page != null) {
        param.addAll({"page": page});
      }
      if (perPage != null) {
        param.addAll({"per-page": perPage});
      }
      if (prefix != null) {
        param.addAll({"prefix": prefix});
      }
      final response = await ApiService.instance
          .get2(url: ShopEndPoints.getListSim, queryParameters: param);
      var res =
          BaseResponse<ListSimResponse>(response, ListSimResponse.fromJson);
      return res;
    } catch (e) {
      return BaseResponse<ListSimResponse>.withError(
          CODE_ERROR, ApiService.instance.handleError2(e));
    }
  }

  // random sim
  Future<BaseResponse<SimRandomResponse>?> getRandomSim() async {
    try {
      final response =
          await ApiService.instance.get2(url: ShopEndPoints.getRandomSim);
      var res =
          BaseResponse<SimRandomResponse>(response, SimRandomResponse.fromJson);
      return res;
    } catch (e) {
      return BaseResponse<SimRandomResponse>.withError(
          CODE_ERROR, ApiService.instance.handleError2(e));
    }
  }

  // get OTP
  Future<BaseResponse<GetOtpResponse>?> postOTPSim(
      {String? numberBook, String? idKhoSim, String? phoneNumber}) async {
    try {
      var data = json.encode({
        "number_book": numberBook,
        "idKhoSim": idKhoSim,
        "phone_number": phoneNumber,
      });
      final header = {
        'X-Api-Key': ApiService.instance.ApiKey2,
        'Authorization': 'Bearer ${ApiService.instance.jwt_token2}',
        'PROFILE_ID': ApiService.instance.profileIdTCCT,
      };

      final response = await ApiService.instance.post2(
          url: ShopEndPoints.postOTPNumberBook,
          data: data,
          options: dio.Options(headers: header));
      UDPLog.instance
          .sendMessage("sim-so/get-otp-number-book:" + response.toString());
      print("get-otp-number-book: ${response.data.toString()}");
      var res = BaseResponse<GetOtpResponse>(response, GetOtpResponse.fromJson);
      return res;
    } catch (e) {
      return BaseResponse<GetOtpResponse>.withError(
          CODE_ERROR, ApiService.instance.handleError2(e));
    }
  }

  // resend OTP
  Future<BaseResponse<GetOtpResponse>> postResendOTPSim({int? ordId}) async {
    try {
      var data = json.encode({'order_id': ordId});
      final header = {
        'X-Api-Key': ApiService.instance.ApiKey2,
        'Authorization': 'Bearer ${ApiService.instance.jwt_token2}',
      };
      final response = await ApiService.instance.post2(
          url: ShopEndPoints.postResendOTPNumberBook,
          data: data,
          options: dio.Options(headers: header));
      UDPLog.instance
          .sendMessage("sim-so/resend-otp-number-book:" + response.toString());
      var res = BaseResponse<GetOtpResponse>(response, GetOtpResponse.fromJson);
      return res;
    } catch (e) {
      return BaseResponse<GetOtpResponse>.withError(
          CODE_ERROR, ApiService.instance.handleError2(e));
    }
  }

  // book sim
  Future<BaseResponse?> postBookSim({int? ordId, String? otp}) async {
    try {
      var data = json.encode({'order_id': ordId, 'otp': otp});
      final header = {
        'X-Api-Key': ApiService.instance.ApiKey2,
        'Authorization': 'Bearer ${ApiService.instance.jwt_token2}',
        'PROFILE_ID': ApiService.instance.profileIdTCCT,
      };
      final response = await ApiService.instance.post2(
          url: ShopEndPoints.postBookSim,
          data: data,
          options: dio.Options(headers: header));
      var res = BaseResponse.fromJson(response);
      return res;
    } catch (e) {
      return BaseResponse.withError(
          CODE_ERROR, ApiService.instance.handleError2(e));
    }
  }

  // un book sim
  Future<void> postUnBookSim({int? ordId}) async {
    try {
      var data = json.encode({'order_id': ordId});
      final header = {
        'X-Api-Key': ApiService.instance.ApiKey2,
        'Authorization': 'Bearer ${ApiService.instance.jwt_token2}',
        'PROFILE_ID': ApiService.instance.profileIdTCCT,
      };
      await ApiService.instance.post2(
          url: ShopEndPoints.postUnBookSim,
          data: data,
          options: dio.Options(headers: header));
    } catch (e) {
      return;
    }
  }

  // filter
  Future<BaseResponse<FilterResponse>?> getFilter() async {
    try {
      final response =
          await ApiService.instance.get2(url: ShopEndPoints.getFilterSim);
      var res = BaseResponse<FilterResponse>(response, FilterResponse.fromJson);
      return res;
    } catch (e) {
      return BaseResponse<FilterResponse>.withError(
          CODE_ERROR, ApiService.instance.handleError2(e));
    }
  }

  //order
  Future<BaseResponse<OrderResponse>> getOrder2({
    String? keyword,
    int? status,
    int? page,
  }) async {
    final header = {
      'X-Api-Key': ApiService.instance.ApiKey2,
      'Authorization': 'Bearer ${ApiService.instance.jwt_token2}',
      'PROFILE_ID': ApiService.instance.profileIdTCCT,
    };
    final param = {
      'keyword': keyword,
      'status': status,
      'page': page,
      'per-page': 200,
    };
    try {
      final response = await ApiService.instance.get2(
          url: ShopEndPoints.get_order,
          queryParameters: param,
          options: dio.Options(headers: header));
      var res = BaseResponse<OrderResponse>(response, OrderResponse.fromJson);
      return res;
    } catch (e) {
      return BaseResponse<OrderResponse>.withError(
          CODE_ERROR, ApiService.instance.handleError2(e));
    }
  }

  Future<BaseResponse<OrderDetail>> getOrderDetail2(int? id) async {
    final header = {
      'X-Api-Key': ApiService.instance.ApiKey2,
      'Authorization': 'Bearer ${ApiService.instance.jwt_token2}',
      'PROFILE_ID': ApiService.instance.profileIdTCCT,
    };
    try {
      final response = await ApiService.instance.get2(
          url: ShopEndPoints.get_order_by_id(id ?? 0),
          options: dio.Options(headers: header));
      var res = BaseResponse<OrderDetail>(response, OrderDetail.fromJson);
      return res;
    } catch (e) {
      return BaseResponse<OrderDetail>.withError(
          CODE_ERROR, ApiService.instance.handleError2(e));
    }
  }

  Future<BaseResponse<BuyPackage>> postOrderBuyPackage2(
      String phone, int productId, String referCode, int platform,
      {required PaymentType paymentType}) async {
    final header = {
      'X-Api-Key': ApiService.instance.ApiKey2,
      'Authorization': 'Bearer ${ApiService.instance.jwt_token2}',
      'PROFILE_ID': ApiService.instance.profileIdTCCT,
    };
    try {
      var data = json.encode({
        "phone_number": phone,
        "product_id": productId,
        "refer_code": referCode,
        "platform": platform,
        "payment_type": paymentType == PaymentType.tkc ? 3 : 4
      });
      final response = await ApiService.instance.post2(
          url: ShopEndPoints.post_orders_buy_package_di_dong,
          data: data,
          options: dio.Options(headers: header));
      var res = BaseResponse<BuyPackage>(response, BuyPackage.fromJson);
      return res;
    } catch (e) {
      return BaseResponse<BuyPackage>.withError(
          CODE_ERROR, ApiService.instance.handleError2(e));
    }
  }

  Future<BaseResponse<ValidateBuyPackage>> postValidateBuyPackage2(
      int orderId, String otp) async {
    final header = {
      'X-Api-Key': ApiService.instance.ApiKey2,
      'Authorization': 'Bearer ${ApiService.instance.jwt_token2}',
      'PROFILE_ID': ApiService.instance.profileIdTCCT,
    };
    try {
      var data = json.encode({"order_id": orderId, "otp": otp});
      final response = await ApiService.instance.post2(
          url: ShopEndPoints.post_validate_buy_package_di_dong,
          data: data,
          options: dio.Options(headers: header));
      var res = BaseResponse<ValidateBuyPackage>(
          response, ValidateBuyPackage.fromJson);
      return res;
    } catch (e) {
      return BaseResponse<ValidateBuyPackage>.withError(
          CODE_ERROR, ApiService.instance.handleError2(e));
    }
  }

  Future<BaseResponse<BuyPackage>> postResendOtp2(int orderId) async {
    final header = {
      'X-Api-Key': ApiService.instance.ApiKey2,
      'Authorization': 'Bearer ${ApiService.instance.jwt_token2}',
      'PROFILE_ID': ApiService.instance.profileIdTCCT,
    };
    try {
      var data = json.encode({"order_id": orderId});
      final response = await ApiService.instance.post2(
          url: ShopEndPoints.post_orders_resend_otp,
          data: data,
          options: dio.Options(headers: header));
      var res = BaseResponse<BuyPackage>(response, BuyPackage.fromJson);
      return res;
    } catch (e) {
      return BaseResponse<BuyPackage>.withError(
          CODE_ERROR, ApiService.instance.handleError2(e));
    }
  }

  Future<Map<int, int>> getCountProduct2({String? keyword}) async {
    try {
      final Map<int, int> listR = {};
      var response = await ApiService.instance.get2(
          url: ShopEndPoints.get_products_count,
          queryParameters: {'keyword': keyword});
      final data = response.data['data'] as Map;
      data.keys.forEach((e) {
        listR[int.parse(e)] = data[e];
      });
      return listR;
    } catch (e) {
      return {};
    }
  }

  Future<BaseResponse<Product>> getProduct2(int? id) async {
    final header = {
      'X-Api-Key': ApiService.instance.ApiKey2,
      'Authorization': 'Bearer ${ApiService.instance.jwt_token2}',
      'PROFILE_ID': ApiService.instance.profileIdTCCT,
    };
    try {
      final response = await ApiService.instance.get2(
          url: ShopEndPoints.get_product(id ?? 0),
          options: dio.Options(headers: header));
      var res = BaseResponse<Product>(response, Product.fromJson);
      return res;
    } catch (e) {
      return BaseResponse<Product>.withError(
          CODE_ERROR, ApiService.instance.handleError2(e));
    }
  }

  //SIM SỐ
  Future<PhongGiaoDichResponse> get_trading_location(
      {int? cityId, int? districtId}) async {
    try {
      final response = await ApiService.instance.get2(
          url: ShopEndPoints.getPhongGiaoDich,
          queryParameters: {'city_id': cityId, "district_id": districtId});
      var res = PhongGiaoDichResponse.fromJson(response.data);
      Get.log(res.toString());
      return res;
    } catch (e) {
      return PhongGiaoDichResponse();
    }
  }

  //Create order
  Future<SIMOrderResponse> postCreate(SIMOrder simOrderRequest) async {
    try {
      final header = {
        'X-Api-Key': ApiService.instance.ApiKey2,
        'Authorization': 'Bearer ${ApiService.instance.jwt_token2}',
        'PROFILE_ID': ApiService.instance.profileIdTCCT,
      };
      final response = await ApiService.instance.post2(
          url: ShopEndPoints.postCreate,
          data: simOrderRequest.toJson(),
          options: dio.Options(headers: header));
      UDPLog.instance
          .sendMessage("sim-so/postCreate:" + response.data.toString());
      print("postCreate ${response.data.toString()}");
      var res = SIMOrderResponse.fromJson(response.data);
      Get.log(res.toString());
      return res;
    } catch (e) {
      return SIMOrderResponse();
    }
  }

  // payment online
  Future<BaseResponse<ValidatePhonenumberResponse>> getValidateMobile(
      {required String phoneNumber}) async {
    try {
      Map<String, dynamic> param = {"phone_number": phoneNumber};
      final response = await ApiService.instance
          .get2(url: ShopEndPoints.getValidateMobile, queryParameters: param);
      var res = BaseResponse<ValidatePhonenumberResponse>(
          response, ValidatePhonenumberResponse.fromJson);
      return res;
    } catch (e) {
      return BaseResponse<ValidatePhonenumberResponse>.withError(
          CODE_ERROR, ApiService.instance.handleError2(e));
    }
  }

  Future<BaseResponse<ListBankInforResponse>> getListBankInfor() async {
    try {
      final response =
          await ApiService.instance.get2(url: ShopEndPoints.getListBankInfor);
      var res = BaseResponse<ListBankInforResponse>(
          response, ListBankInforResponse.fromJson);
      return res;
    } catch (e) {
      return BaseResponse<ListBankInforResponse>.withError(
          CODE_ERROR, ApiService.instance.handleError2(e));
    }
  }

  Future<BaseResponse<PaymentInforResponse>> getPaymentInfor(
      {required int ordId}) async {
    try {
      final response = await ApiService.instance
          .get2(url: ShopEndPoints.getPaymentInfor(ordId));
      var res = BaseResponse<PaymentInforResponse>(
          response, PaymentInforResponse.fromJson);
      return res;
    } catch (e) {
      return BaseResponse<PaymentInforResponse>.withError(
          CODE_ERROR, ApiService.instance.handleError2(e));
    }
  }

  // report flag
  Future<BaseResponse<ReportFlagResponse?>> getReportFlag() async {
    try {
      final response =
          await ApiService.instance.get2(url: ShopEndPoints.getReportFlag);
      return BaseResponse<ReportFlagResponse>(
          response, ReportFlagResponse.fromJson);
    } catch (e) {
      return BaseResponse<ReportFlagResponse>.withError(
          CODE_ERROR, ApiService.instance.handleError2(e));
    }
  }

  /// insurance
  // get infor bao hiem
  Future<BaseResponse<InsuranceFilterResponse>> getInsFilter(
      {required int insId}) async {
    try {
      final response = await ApiService.instance
          .get2(url: ShopEndPoints.getInsuranceFilter(insId));
      var res = BaseResponse<InsuranceFilterResponse>(
          response, InsuranceFilterResponse.fromJson);
      return res;
    } catch (e) {
      return BaseResponse<InsuranceFilterResponse>.withError(
          CODE_ERROR, ApiService.instance.handleError2(e));
    }
  }

  // get price, coupon discount
  Future<BaseResponse<InsuranceGetPriceResponse>> postGetPrice(
      {required InsuranceGetPriceFormResquest formRequest}) async {
    try {
      final response = await ApiService.instance.post2(
          url: ShopEndPoints.postInsuranceGetPrice, data: formRequest.toJson());
      var res = BaseResponse<InsuranceGetPriceResponse>(
          response, InsuranceGetPriceResponse.fromJson);
      return res;
    } catch (e) {
      return BaseResponse<InsuranceGetPriceResponse>.withError(
          CODE_ERROR, ApiService.instance.handleError2(e));
    }
  }

  // create order
  Future<BaseResponse<InsuranceCreateOrdResponse>> postCreateOrder(
      {required InsuranceCreateOrdRequestForm formRequest}) async {
    try {
      final header = {
        'X-Api-Key': ApiService.instance.ApiKey2,
        'Authorization': 'Bearer ${ApiService.instance.jwt_token2}',
        'PROFILE_ID': ApiService.instance.profileIdTCCT,
      };
      final response = await ApiService.instance.post2(
          url: ShopEndPoints.postInsuranceCreateOrder,
          data: formRequest.toJson(),
          options: dio.Options(headers: header));
      var res = BaseResponse<InsuranceCreateOrdResponse>(
          response, InsuranceCreateOrdResponse.fromJson);
      return res;
    } catch (e) {
      return BaseResponse<InsuranceCreateOrdResponse>.withError(
          CODE_ERROR, ApiService.instance.handleError2(e));
    }
  }
}
