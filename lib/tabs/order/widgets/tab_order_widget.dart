import 'package:flutter/material.dart';

import '../../../constants/app_colors.dart';
import '../../../constants/app_text_style.dart';

class TabOrderWidget extends StatelessWidget {
  const TabOrderWidget({
    super.key,
    this.bgColor,
    this.text,
    this.index,
    this.isActive,
  });
  final Color? bgColor;
  final String? text;
  final int? index;
  final isActive;

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 43,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(30),
        border: Border.all(
          color: isActive ? AppColors.blue30AAB7 : AppColors.white,
          width: 1,
        ),
      ),
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: 15),
        child: Center(
          child: Text(
            text ?? '',
            style: AppTextStyle.s13Medium.copyWith(
              color: isActive == true ? AppColors.blue30AAB7 : AppColors.black,
            ),
          ),
        ),
      ),
    );
  }
}
