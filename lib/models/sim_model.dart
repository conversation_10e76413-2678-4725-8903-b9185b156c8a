import 'package:shopping/enum/enum_sim_type.dart';


class SIMOrder {
  int? orderId;
  int? productId;
  String? productName;
  String? fullName;
  // String? cmnd;
  num? giaSim;
  num? phiHoaMang;
  String? phoneNumber;
  num? giaGoi;

  String? userPhoneNumber;

  int? loaiSim;
  int? hinhThucNhanSim;
  num? phiGiaoHang;
  int? tinhId;
  String? nameProvince;
  int? quanId;
  int? quanItId;
  String? nameQuan;
  int? phuongId;
  int? phuongItId;
  String? namePhuong;
  int? phoId;
  int? phoItId;
  String? namePho;
  String? diachi;
  int? diemGdId;
  String? namePhongGd;
  int? paymentType;
  String? email;

  SIMOrder({
    this.orderId,
    this.productId,
    this.productName,
    this.fullName,
    // this.cmnd,
    this.giaSim,
    this.giaGoi,
    this.phiHoaMang,
    this.phoneNumber,
    this.userPhoneNumber,
    this.loaiSim,
    this.hinhThucNhanSim,
    this.phiGiaoHang,
    this.tinhId,
    this.nameProvince,
    this.quanId,
    this.quanItId,
    this.nameQuan,
    this.phuongId,
    this.phuongItId,
    this.namePhuong,
    this.phoId,
    this.phoItId,
    this.namePho,
    this.diachi,
    this.diemGdId,
    this.namePhongGd,
    this.paymentType,
    this.email,
  });

  SIMOrder copyWith({
    int? orderId,
    int? productId,
    String? productName,
    String? fullName,
    String? cmnd,
    num? giaSim,
    num? giaGoi,
    num? phiHoaMang,
    String? phoneNumber,
    String? userPhoneNumber,
    int? loaiSim,
    int? hinhThucNhanSim,
    num? phiGiaoHang,
    int? tinhId,
    String? nameProvince,
    int? quanId,
    int? quanItId,
    String? nameQuan,
    int? phuongId,
    int? phuongItId,
    String? namePhuong,
    int? phoId,
    int? phoItId,
    String? namePho,
    String? diachi,
    int? diemGdId,
    String? namePhongGd,
    int? paymentType,
    String? email,
  }) =>
      SIMOrder(
        orderId: orderId ?? this.orderId,
        productId: productId ?? this.productId,
        productName: productName ?? this.productName,
        fullName: fullName ?? this.fullName,
        // cmnd: cmnd ?? this.cmnd,
        giaSim: giaSim ?? this.giaSim,
        giaGoi: giaGoi ?? this.giaGoi,
        phiHoaMang: phiHoaMang ?? this.phiHoaMang,
        phoneNumber: phoneNumber ?? this.phoneNumber,
        userPhoneNumber: userPhoneNumber ?? this.userPhoneNumber,
        loaiSim: loaiSim ?? this.loaiSim,
        hinhThucNhanSim: hinhThucNhanSim ?? this.hinhThucNhanSim,
        phiGiaoHang: phiGiaoHang ?? this.phiGiaoHang,
        tinhId: tinhId ?? this.tinhId,
        nameProvince: nameProvince ?? this.nameProvince,
        quanId: quanId ?? this.quanId,
        quanItId: quanItId ?? this.quanItId,
        nameQuan: nameQuan ?? this.nameQuan,
        phuongId: phuongId ?? this.phuongId,
        phuongItId: phuongItId ?? this.phuongItId,
        namePhuong: namePhuong ?? this.namePhuong,
        phoId: phoId ?? this.phoId,
        phoItId: phoItId ?? this.phoItId,
        namePho: namePho ?? this.namePho,
        diachi: diachi ?? this.diachi,
        diemGdId: diemGdId ?? this.diemGdId,
        namePhongGd: namePhongGd ?? this.namePhongGd,
        paymentType: paymentType ?? this.paymentType,
        email: email ?? this.email,
      );

  factory SIMOrder.fromJson(Map<String, dynamic> json) => SIMOrder(
        orderId: json["order_id"],
        productId: json["product_id"],
        fullName: json["fullname"],
        // cmnd: json["cmnd"],
        giaSim: json["gia_sim"],
        phiHoaMang: json["phi_hoa_mang"],
        loaiSim: json["loai_sim	"],
        hinhThucNhanSim: json["ht_nhansim"],
        phiGiaoHang: json["phi_giao_hang"],
        tinhId: json["tinh_id"],
        quanId: json["quan_id"],
        quanItId: json["quan_it_id"],
        phuongId: json["phuong_id"],
        phuongItId: json["phuong_it_id"],
        phoId: json["pho_id"],
        phoItId: json["pho_it_id"],
        diachi: json["diachi"],
        diemGdId: json["diem_gd_id"],
        paymentType: json["payment_type"],
        email: json["email"],
      );

  Map<String, dynamic> toJson() => {
        "order_id": orderId,
        "product_id": productId,
        "fullname": fullName,
        // "cmnd": cmnd,
        "gia_sim": giaSim,
        "phi_hoa_mang": phiHoaMang,
        "ht_nhansim": hinhThucNhanSim,
        "loai_sim": loaiSim,
        "phi_giao_hang": phiGiaoHang,
        "tinh_id": tinhId,
        "quan_id": quanId,
        "quan_it_id": quanItId,
        "phuong_id": phuongId,
        "phuong_it_id": phuongItId,
        "pho_id": phoId,
        "pho_it_id": phoItId,
        "diachi": diachi,
        "diem_gd_id": diemGdId,
        "payment_type": paymentType,
        "email": email,
      };
  String getAddress(SIMOrder simOrder) {
    String address = "";

    if (simOrder.hinhThucNhanSim == 1) {
      address = simOrder.diachi ?? "";
      if (address.isEmpty) {
        address =
            "${simOrder.namePho}, ${simOrder.namePhuong}, ${simOrder.nameQuan}, ${simOrder.nameProvince}";
      } else {
        address =
            "${simOrder.diachi}, ${simOrder.namePho}, ${simOrder.namePhuong}, ${simOrder.nameQuan}, ${simOrder.nameProvince}";
      }
    } else if (simOrder.hinhThucNhanSim == 2) {
      address = simOrder.namePhongGd ?? "";
    }
    return address;
  }

  num totalMoney(SIMOrder simOrder) {
    num totalMoney;
    totalMoney = (simOrder.giaSim ?? 0) +
        // (simOrder.phiGiaoHang ?? 0) + // ẩn phí giao hàng
        (simOrder.giaGoi ?? 0) +
        (simOrder.phiHoaMang ?? 0);
    return totalMoney;
  }

  num getGiaSim(SIMOrder simOrder) {
    return (simOrder.giaSim ?? 0) + (simOrder.phiHoaMang ?? 0);
  }

  SimType? get getSimType {
    return SimType.values.where((e) => e.getValue == loaiSim).firstOrNull;
  }
}
