enum CarType { cho<PERSON><PERSON><PERSON>, cho<PERSON><PERSON>, pickup }

enum CarPurposeUsedType { non_business, business }

enum CarTimeType { one, two, three }

extension CarTypeExt on CarType {
  String get getName {
    switch (this) {
      case CarType.choNguoi:
        return 'Xe chở người';
      case CarType.choHang:
        return 'Xe chở hàng (xe tải)';
      case CarType.pickup:
        return 'Xe pickup (bán tải, minivan)';
    }
  }
}

extension CarPurposeUsedTypeExt on CarPurposeUsedType {
  String get getName {
    switch (this) {
      case CarPurposeUsedType.business:
        return 'Có kinh doanh';
      case CarPurposeUsedType.non_business:
        return 'Không kinh doanh';
    }
  }

  String get getValue {
    switch (this) {
      case CarPurposeUsedType.business:
        return 'C';
      case CarPurposeUsedType.non_business:
        return 'K';
    }
  }
}

extension CarTimeExt on CarTimeType {
  String get getName {
    switch (this) {
      case CarTimeType.one:
        return '1 năm';
      case CarTimeType.two:
        return '2 năm';
      case CarTimeType.three:
        return '3 năm';
    }
  }

  int get getDurationTime {
    switch (this) {
      case CarTimeType.one:
        return 1;
      case CarTimeType.two:
        return 2;
      case CarTimeType.three:
        return 3;
    }
  }
}
