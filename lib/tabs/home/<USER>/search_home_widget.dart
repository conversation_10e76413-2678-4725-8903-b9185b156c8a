import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:infinite_scroll_pagination/infinite_scroll_pagination.dart';
import 'package:shopping/models/product.dart';
import 'package:shopping/pages/package/widgets/product_widget.dart';
import 'package:shopping/tabs/home/<USER>/not_item_search_widget.dart';
import 'package:shopping/tabs/home/<USER>/search_home_controller.dart';

import '../../../utils/responsive_utils.dart';
import '../widgets/item_package_action.dart';

class SearchHomeWidget extends StatelessWidget {
  final SearchHomeController controller;
  const SearchHomeWidget({
    super.key,
    required this.controller,
  });

  @override
  Widget build(BuildContext context) {
    final double w = MediaQuery.of(context).size.width;
    final int _cntItem = ResponsiveUtils.isMobile(context)
        ? 2
        : ResponsiveUtils.isTablet(context)
            ? 3
            : 4;
    // itemWidth = (device width - 2 viền - kho<PERSON>ng cách giữa các item)/số lượng item 1 row
    final double _itemWidth = (w - 30 - (_cntItem - 1) * 15) / _cntItem;
    // if (controller.pageController.itemList?.isEmpty == true) {
    //   return NotItemSearchWidget();
    // } else {
    // return GridView.builder(
    //   shrinkWrap: true,
    //   itemCount: listProduct?.length,
    //   gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
    //     crossAxisCount: ResponsiveUtils.isMobile(context)
    //         ? 2
    //         : ResponsiveUtils.isTablet(context)
    //             ? 3
    //             : 4,
    //     mainAxisSpacing: 20,
    //     crossAxisSpacing: 15,
    //     childAspectRatio: 156 / 196,
    //   ),
    //   itemBuilder: (_, index) {
    //     return GestureDetector(
    //       onTap: () {
    //         ItemPackageAction.openDetail(context, listProduct![index]);
    //       },
    //       child: ItemProductWidget(
    //         product: listProduct?[index],
    //         openBuy: () {
    //           ItemPackageAction.openSheetBuy(context, listProduct![index]);
    //         },
    //       ),
    //     );
    //   },
    // );
    return PagedGridView(
      // shrinkWrap: true,
      keyboardDismissBehavior: ScrollViewKeyboardDismissBehavior.onDrag,
      pagingController: controller.pageController,
      builderDelegate: PagedChildBuilderDelegate<Product>(
        itemBuilder: (c, i, index) {
          return GestureDetector(
            onTap: () {
              ItemPackageAction.openDetail(context, i);
            },
            child: ProductPackageWidget(
              product: i,
              openBuy: () {
                ItemPackageAction.openSheetBuy(context, i);
              },
            ),
          );
        },
        firstPageProgressIndicatorBuilder: (context) => SizedBox(),
        newPageProgressIndicatorBuilder: (context) => SizedBox(),
        noItemsFoundIndicatorBuilder: (context) =>
            Obx(() => controller.isLoading.value
                ? SizedBox()
                : NotItemSearchWidget(
                    messageFail: controller.messageFail.value,
                  )),
      ),

      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: _cntItem,
        mainAxisSpacing: 15,
        crossAxisSpacing: 15,
        childAspectRatio: _itemWidth / 180,
      ),
    );
  }
  // }
}
