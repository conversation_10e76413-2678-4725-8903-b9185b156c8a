import 'package:flutter/material.dart';
import 'package:get/get.dart' show GetMaterialApp;
import 'package:shopping/shopping.dart';

void main() {
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});
  @override
  Widget build(BuildContext context) {
    var shopping = Shopping(
        RunMode.stag,
        "IYBSV8I1EFIDCMPKGY8T4JO5U43XNXM7",
        "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************************************************************************************************************************************************************.Z35gShtgbPdnuqUqpa7q6I0grrkJpv0Q8ZbKSa6QPik",
        1068363806742273,
        2);
    return GetMaterialApp(
      title: 'Flutter Demo',
      theme: ThemeData(
        colorScheme: ColorScheme.fromSeed(seedColor: Colors.deepPurple),
      ),
      home: shopping.page(true, 0),
    );
  }
}
