import 'package:dropdown_button2/dropdown_button2.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:loader_overlay/loader_overlay.dart';
import 'package:shopping/apis/ShoppingRepository.dart';
import 'package:shopping/constants/app_assets_paths.dart';
import 'package:shopping/constants/app_colors.dart';
import 'package:shopping/constants/app_text_style.dart';
import 'package:shopping/constants/enum_receiving_method_widget.dart';
import 'package:shopping/constants/enum_step_ord_sim.dart';
import 'package:shopping/enum/enum_sim_type.dart';
import 'package:shopping/loading_screen.dart';
import 'package:shopping/models/pgd_address.dart';
import 'package:shopping/models/product.dart';
import 'package:shopping/models/province.dart';
import 'package:shopping/pages/sim/item_sim_action.dart';
import 'package:shopping/pages/sim/sim_order_controller.dart';
import 'package:shopping/pages/sim/widgets/count_time_order_sim_widget.dart';
import 'package:shopping/pages/sim/widgets/dash_line_widget.dart';
import 'package:shopping/pages/sim/widgets/receiving_method_widget.dart';
import 'package:shopping/pages/sim/widgets/sim_text_field_widget.dart';
import 'package:shopping/pages/sim/widgets/stepper_widget.dart';
import 'package:shopping/pages/sim/widgets/ticket_widget.dart';
import 'package:shopping/widgets/app_radio_button_widget.dart';
import 'package:shopping/widgets/required_dropdown_search.dart';

class SIMOrderScreen extends StatelessWidget {
  const SIMOrderScreen(
      {super.key,
      this.ordId,
      this.product,
      this.phoneNumber,
      this.userPhoneNumber});
  final int? ordId;
  final Product? product;
  final String? phoneNumber;
  final String? userPhoneNumber;
  @override
  Widget build(BuildContext context) {
    return GetBuilder<SimOrderController>(
        init: SimOrderController(
            ordId: ordId,
            product: product,
            context: context,
            phoneNumber: phoneNumber,
            userPhoneNumber: userPhoneNumber),
        builder: (controller) => LoaderOverlay(
              useDefaultLoading: false,
              overlayWidgetBuilder: (_) {
                return Center(
                  child: LoadingWidget(),
                );
              },
              child: Form(
                key: controller.pollKey,
                child: Scaffold(
                  backgroundColor: AppColors.white,
                  appBar: AppBar(
                    centerTitle: true,
                    backgroundColor: AppColors.white,
                    scrolledUnderElevation: 0,
                    title: const Text(
                      "SIM SỐ",
                    ),
                    surfaceTintColor: AppColors.white,
                    elevation: 5,
                    shadowColor: AppColors.black.withValues(alpha: 0.4),
                    titleTextStyle:
                        AppTextStyle.s16Bold.copyWith(color: Colors.black),
                    leading: IconButton(
                      icon: SvgPicture.asset(
                        SvgPath.svgIconBack,
                      ),
                      onPressed: () {
                        ShoppingRepository.instance.postUnBookSim(ordId: ordId);
                        ActionBack.onBack(context: context);
                        // Navigator.of(context).popUntil(ModalRoute.withName("/page_list_sim"));
                      },
                    ),
                  ),
                  body: SingleChildScrollView(
                    physics: ClampingScrollPhysics(),
                    padding: EdgeInsets.symmetric(horizontal: 15, vertical: 15),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              SizedBox(
                                height: 53,
                                child: LineStepStatusWidget(
                                  currentStepIndex: controller.listStepOrdName
                                      .indexOf(StepOrdSimStatus.order),
                                  lisStepStatus: controller.listStepOrdName
                                      .map((e) => e.getName)
                                      .toList(),
                                ),
                              ),
                              const SizedBox(height: 18),
                              DashLineWidget(),
                              SizedBox(height: 17),
                              Obx(() => CountTimeOrderSimWidget(
                                  time: controller.stepPickProductController
                                      .countTime.value)),
                              const SizedBox(height: 18),
                              Obx(
                                () => controller.isLoading.value
                                    ? Center(child: LoadingWidget())
                                    : GetBuilder<SimOrderController>(
                                        id: SIMOrderControllerUpdateKey
                                            .updateFilter,
                                        builder: (controller) =>
                                            TicketOrderInforWidget(
                                          product: product,
                                          phoneNumber: phoneNumber,
                                          priceSim: controller
                                              .filterResponse?.item?.giaSim
                                              ?.getGiaSim(
                                                  controller.simTypeChoosed),
                                          simType: controller.simTypeChoosed,
                                        ),
                                      ),
                              ),
                              const SizedBox(height: 18),
                              CustomerInforWidget(controller: controller)
                            ])
                      ],
                    ),
                  ),
                ),
              ),
            ));
  }
}

class CustomerInforWidget extends StatelessWidget {
  CustomerInforWidget({super.key, required this.controller});
  final SimOrderController controller;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        Text(
          'Thông tin đơn hàng',
          style: AppTextStyle.s18SemiBold,
        ),
        SIMTextFieldWidget(
          errorText: controller.isValidateName.value == true
              ? controller.simOrder?.fullName?.isEmpty == true
                  ? 'Họ và tên không được để trống'
                  : null
              : null,
          inputBorder: OutlineInputBorder(
            borderSide: const BorderSide(width: 1, color: AppColors.greyD9D9D9),
            borderRadius: BorderRadius.circular(8),
          ),
          title: 'Họ và tên',
          maxLength: 200,
          isRequired: true,
          onChanged: (value) {
            controller.setFullName(value);
            controller.isValidateName.value = false;
          },
          onSubmitted: (value) {
            controller.setFullName(value);
            controller.isValidateName.value = true;
          },
          text: "",
          hintText: "Nhập họ và tên",
        ),
        SizedBox(
          height: 10,
        ),
        SIMTextFieldWidget(
          filled: true,
          colorFill: AppColors.greyF5F5F5,
          keyBoardType: TextInputType.number,
          title: 'Số điện thoại liên hệ',
          onChanged: (value) {
            // controller.editPhone(value);
            // controller.isValidatePhone.value = false;
          },
          onSubmitted: (value) {
            // controller.editPhone(value);
            // controller.isValidatePhone.value = true;
          },
          readOnly: true,
          isRequired: true,
          text: controller.userPhoneNumber,
          maxLength: 20,
        ),
        SizedBox(height: 10),
        LableWidget(isRequired: true, title: "Loại SIM"),
        Flexible(
            child: GetBuilder<SimOrderController>(
                id: SIMOrderControllerUpdateKey.updateSimType,
                builder: (controller) => ListView.builder(
                      padding: EdgeInsets.zero,
                      physics: NeverScrollableScrollPhysics(),
                      shrinkWrap: true,
                      itemCount: 1,
                      // itemCount: SimType.values.length,
                      itemBuilder: (context, index) {
                        SimType itemSimType = SimType.values[0];
                        // SimType itemSimType = SimType.values[index];
                        return InkWell(
                          splashColor: Colors.transparent,
                          onTap: () {
                            controller.updateSimType(itemSimType);
                          },
                          child: Padding(
                            padding: const EdgeInsets.only(bottom: 10),
                            child: AppRadioButtonWidget<SimType>(
                              item: itemSimType,
                              groupValue: controller.simTypeChoosed,
                              onChanged: (p0) async {
                                controller.updateSimType(itemSimType);
                              },
                              widget: Column(
                                mainAxisSize: MainAxisSize.min,
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    itemSimType.getName,
                                    style: AppTextStyle.s14Medium.copyWith(
                                        fontWeight: FontWeight.w500,
                                        height: 24 / 14,
                                        color: AppColors.black111928),
                                  ),
                                  itemSimType.getDescription == null
                                      ? SizedBox()
                                      : Text(
                                          itemSimType.getDescription ?? 'N/A',
                                          style: AppTextStyle.s11Regular
                                              .copyWith(
                                                  fontStyle: FontStyle.italic,
                                                  color: AppColors.black),
                                        )
                                ],
                              ),
                            ),
                          ),
                        );
                      },
                    ))),
        SizedBox(height: 10),
        GetBuilder<SimOrderController>(
            id: SIMOrderControllerUpdateKey.updateSimType,
            builder: (controller) => Visibility(
                visible: controller.isSimPhysical(),
                child:
                    LableWidget(isRequired: true, title: "Địa chỉ nhận hàng"))),
        GetBuilder<SimOrderController>(
            id: SIMOrderControllerUpdateKey.updateAddressReceive,
            builder: (_) => Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Flexible(
                      child: GetBuilder<SimOrderController>(
                          id: SIMOrderControllerUpdateKey.updateSimType,
                          builder: (controller) => Visibility(
                                visible: controller.isSimPhysical(),
                                child: ListView.builder(
                                  padding: EdgeInsets.zero,
                                  physics: NeverScrollableScrollPhysics(),
                                  shrinkWrap: true,
                                  itemCount: ChoosenAddressReceiving
                                      .listReceiveMethod.length,
                                  itemBuilder: (context, index) {
                                    return GestureDetector(
                                      onTap: () async {
                                        context.loaderOverlay.show();
                                        controller.onSelectReceivingMethod(
                                            ChoosenAddressReceiving
                                                .listReceiveMethod[index]);
                                        controller.resetData();
                                        controller.setListAddressIndex();
                                        await controller.getListAddress(
                                            isAll: true);
                                        context.loaderOverlay.hide();
                                      },
                                      child: Padding(
                                        padding:
                                            const EdgeInsets.only(bottom: 10),
                                        child: CustomRadioBtnLineWidget(
                                            item: ChoosenAddressReceiving
                                                .listReceiveMethod[index],
                                            groupValue: controller
                                                .receivingMethodChoosed,
                                            onChanged: (p0) async {
                                              context.loaderOverlay.show();
                                              controller.onSelectReceivingMethod(
                                                  ChoosenAddressReceiving
                                                          .listReceiveMethod[
                                                      index]);
                                              controller.resetData();
                                              controller.setListAddressIndex();
                                              await controller.getListAddress(
                                                  isAll: true);
                                              context.loaderOverlay.hide();
                                            }),
                                      ),
                                    );
                                  },
                                ),
                              )),
                    ),
                    Obx(
                      () => RequiredDropdownSeachFormField<Address>(
                        textEditingController:
                            controller.textEditingCityController,
                        label: 'Tỉnh/TP',
                        isRequired: true,
                        valueSelectedNotifier: controller.selectedCityNotifier,
                        items: controller.listCity
                            .map(
                              (e) => DropdownItem(
                                  value: e, child: Text(e.name ?? 'N/A')),
                            )
                            .toList(),
                        onChanged: (value) {
                          controller.pickAddress(index: 0, value: value);
                        },
                        hintText: 'Chọn Tỉnh/TP',
                      ),
                    ),
                    // SIMPickerWidget(
                    //   key: ValueKey('diachi'),
                    //   title: 'Tỉnh/TP',
                    //   isRequire: true,
                    //   hintText: 'Chọn Tỉnh/TP',
                    //   listData: controller.listAddress?[0],
                    //   value: controller.listAddress?[0]?.firstWhereOrNull(
                    //       (element) =>
                    //           element.id == controller.listIntAddress[0]),
                    //   pickData: (value) {
                    //     controller.pickAddress(index: 0, value: value);
                    //   },
                    // ),
                    Obx(
                      () => RequiredDropdownSeachFormField<Address>(
                        textEditingController:
                            controller.textEditingDistrictController,
                        label: 'Quận/Huyện',
                        isRequired: true,
                        valueSelectedNotifier:
                            controller.selectedDistrictNotifier,
                        items: controller.listDistrict
                            .map(
                              (e) => DropdownItem(
                                  value: e, child: Text(e.name ?? 'N/A')),
                            )
                            .toList(),
                        onChanged: (value) {
                          controller.pickAddress(index: 1, value: value);
                        },
                        hintText: 'Chọn Quận/Huyện',
                      ),
                    ),
                    // SIMPickerWidget(
                    //   key: ValueKey('quan/huyen'),
                    //   value: controller.listAddress?[1]?.firstWhereOrNull(
                    //       (element) =>
                    //           element.id == controller.listIntAddress[1]),
                    //   isRequire: true,
                    //   hintText: 'Chọn Quận/Huyện',
                    //   title: 'Quận/Huyện',
                    //   listData: controller.listAddress?[1],
                    //   pickData: (value) {
                    //     controller.pickAddress(index: 1, value: value);
                    //   },
                    // ),
                    Obx(
                      () => RequiredDropdownSeachFormField<Address>(
                        textEditingController:
                            controller.textEditingVillageController,
                        label: 'Xã/Phường',
                        isRequired: true,
                        valueSelectedNotifier:
                            controller.selectedVillageNotifier,
                        items: controller.listVillage
                            .map(
                              (e) => DropdownItem(
                                  value: e, child: Text(e.name ?? 'N/A')),
                            )
                            .toList(),
                        onChanged: (value) {
                          controller.pickAddress(index: 2, value: value);
                        },
                        hintText: 'Chọn Xã/Phường',
                      ),
                    ),
                    // SIMPickerWidget(
                    //   key: ValueKey('xa/phuong'),
                    //   value: controller.listAddress?[2]?.firstWhereOrNull(
                    //       (element) =>
                    //           element.id == controller.listIntAddress[2]),
                    //   isRequire: true,
                    //   hintText: 'Chọn Xã/Phường',
                    //   title: 'Xã/Phường',
                    //   listData: controller.listAddress?[2],
                    //   pickData: (value) {
                    //     controller.pickAddress(index: 2, value: value);
                    //   },
                    // ),
                    Obx(
                      () => RequiredDropdownSeachFormField<Address>(
                        textEditingController:
                            controller.textEditingHamletController,
                        label: 'Phố/Ấp/Khu',
                        isRequired: true,
                        valueSelectedNotifier:
                            controller.selectedHamletNotifier,
                        items: controller.listHamlet
                            .map(
                              (e) => DropdownItem(
                                  value: e, child: Text(e.name ?? 'N/A')),
                            )
                            .toList(),
                        onChanged: (value) {
                          controller.pickAddress(index: 3, value: value);
                        },
                        hintText: 'Chọn Phố/Ấp/Khu',
                      ),
                    ),
                    // SIMPickerWidget(
                    //   key: ValueKey('pho/ap/khu'),
                    //   value: controller.listAddress?[3]?.firstWhereOrNull(
                    //       (element) =>
                    //           element.id == controller.listIntAddress[3]),
                    //   isRequire: true,
                    //   title: 'Phố/Ấp/Khu',
                    //   hintText: 'Chọn Phố/Ấp/Khu',
                    //   listData: controller.listAddress?[3],
                    //   pickData: (value) {
                    //     controller.pickAddress(index: 3, value: value);
                    //   },
                    // ),
                    Visibility(
                      visible: controller.receivingMethodChoosed ==
                          ReceivingMethodByType.tradingLocation,
                      child: Obx(
                        () => RequiredDropdownSeachFormField<PhongGiaoDich>(
                          textEditingController:
                              controller.textEditingPGDController,
                          label: 'Cửa hàng/Điểm giao dịch',
                          isRequired: true,
                          valueSelectedNotifier: controller.selectedPGDNotifier,
                          items: controller.listPGD
                              .map(
                                (e) => DropdownItem(
                                    value: e, child: Text(e.name ?? 'N/A')),
                              )
                              .toList(),
                          onChanged: (value) {
                            controller.pickPhongGiaoDich(
                                index: 4, value: value);
                          },
                          hintText: 'Chọn Cửa hàng/Điểm giao dịch',
                        ),
                      ),
                      // SIMPickerWidget(
                      //   key: ValueKey('cuahang'),
                      //   title: 'Cửa hàng/Điểm giao dịch',
                      //   isRequire: true,
                      //   hintText: 'Chọn Cửa hàng/Điểm giao dịch',
                      //   listData: controller.listPhongGiaoDich?[0],
                      //   value: controller.listPhongGiaoDich?[0]
                      //       ?.firstWhereOrNull((element) =>
                      //           element.idPgd == controller.listIntAddress[4]),
                      //   pickData: (value) {
                      //     controller.pickPhongGiaoDich(index: 4, value: value);
                      //   },
                      // ),
                    ),
                    Visibility(
                      visible: controller.receivingMethodChoosed !=
                          ReceivingMethodByType.tradingLocation,
                      child: Padding(
                        padding: const EdgeInsets.only(bottom: 10),
                        child: SIMTextFieldWidget(
                          isRequired: true,
                          maxLength: 500,
                          hintText: 'Nhập địa chỉ chi tiết',
                          onChanged: (value) {
                            controller.setAddress(value);
                          },
                          onSubmitted: (value) {
                            controller.setAddress(value);
                          },
                          title: "Địa chỉ chi tiết",
                          text: "",
                          errorText: controller.isValidateName.value == true
                              ? controller.simOrder?.diachi?.isEmpty == true
                                  ? 'Địa chỉ chi tiết không được để trống'
                                  : null
                              : null,
                          inputBorder: OutlineInputBorder(
                            borderSide: const BorderSide(
                                width: 1, color: AppColors.greyD9D9D9),
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                      ),
                    ),
                    GetBuilder<SimOrderController>(
                        id: SIMOrderControllerUpdateKey.updateSimType,
                        builder: (controller) => Visibility(
                              visible: !controller.isSimPhysical(),
                              child: SIMTextFieldWidget(
                                isRequired: true,
                                maxLength: 200,
                                hintText: 'Nhập địa email',
                                onChanged: (value) {
                                  controller.setEmail(value);
                                  controller.isValidateEmail.value = false;
                                },
                                onSubmitted: (value) {
                                  controller.setEmail(value);
                                  controller.isValidateEmail.value = true;
                                },
                                title: "Email",
                                text: "",
                                errorText: controller.emailValidateErr(),
                                inputBorder: OutlineInputBorder(
                                  borderSide: const BorderSide(
                                      width: 1, color: AppColors.greyD9D9D9),
                                  borderRadius: BorderRadius.circular(8),
                                ),
                              ),
                            )),
                  ],
                )),
        SizedBox(height: 30),
        GestureDetector(
            onTap: () async {
              await controller.onCreateOrderSim(context: context);
            },
            child: Container(
                width: double.infinity,
                padding: EdgeInsets.all(12),
                decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(8),
                    color: AppColors.redCE3723),
                child: Center(
                    child: Text(
                  "TIẾN HÀNH THANH TOÁN",
                  style:
                      AppTextStyle.s16Medium.copyWith(color: AppColors.white),
                )))),
      ],
    );
  }
}


