import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:shopping/constants/app_assets_paths.dart';
import 'package:shopping/constants/app_colors.dart';
import 'package:shopping/constants/app_text_style.dart';
import 'package:shopping/shopping_controller.dart';
import 'package:shopping/tabs/home/<USER>';
import 'package:shopping/tabs/order/order_controller.dart';
import 'package:shopping/tabs/tabs.dart';

import 'access_permisstion_screen.dart';
import 'error_page/views/connection_error/no_connection.dart';
import 'error_page/views/something_wrong/certain_error.dart';
import 'loading_screen.dart';


// ignore: must_be_immutable
class ShoppingScreen extends GetView<ShoppingController> {
  var isShowNavigatorBar = false;
  int product_id = 0;
  ShoppingScreen(
    this.isShowNavigatorBar,
    this.product_id, {
    Key? key,
  }) : super(key: key) {
    print("ShoppingScreen Product id ${this.product_id}");
  }
  @override
  ShoppingController get controller => Get.put(ShoppingController(product_id));
  @override
  Widget build(BuildContext context) {
    return Obx(() => PopScope(
      onPopInvokedWithResult: (didPop, result) => Get.delete<ShoppingController>(),
      child: Container(color: Colors.white, child: on_load_view_stage(context))));
  }

  Widget _BuildTabView(int index) {
    return _buildContent(index);
  }

  Widget _buildContent(int tab) {
    switch (tab) {
      case 0:
        Get.lazyPut(() => ShoppingHomeController(this.product_id));
        return HomeTab(
          isShowNavigatorBar,
        );
      case 1:
        Get.lazyPut(() => OrderController());
        return OrderTab();
      case 2:
        return ProfileTab(isShowNavigatorBar);
      default:
        return HomeTab(isShowNavigatorBar);
    }
  }

  Widget on_load_view_stage(BuildContext context) {
    if (controller.shopState.value == ShopState.INIT) {
      return LoadingScreen();
    } else if (controller.shopState.value == ShopState.S_ERROR) {
      return CertainError(
        () {
          controller.update_onload();
        },
      );
    } else if (controller.shopState.value == ShopState.S_ERROR_NO_INTENET) {
      return NoConnection(
        () {
          controller.update_onload();
        },
      );
    } else if (controller.shopState.value == ShopState.USER_NOT_ACCEPT_ACCESS) {
      return build_main_shop_app(context);
      // if (controller.isAcceptPermisstion.value == false) {
      //   return PermissionAppScreen(
      //     name: controller.user?.value.fullname,
      //     phoneNumber: controller.user?.value.phoneNumber,
      //     email: controller.user?.value.email,
      //     onCancel: () {
      //       //cho vào mỗi thằng home
      //       controller.setAcceptPermission(true);
      //     },
      //     onContinue: () {
      //       controller.permissionAccept();
      //     },
      //   );
      // } else {
      //   return build_main_shop_app(context);
      // }
      // }
      // }
      // );
    } else {
      return build_main_shop_app(context);
    }
  }

  Widget build_main_shop_app(BuildContext context) {
    return Obx(
      () => Scaffold(
        bottomNavigationBar: _ListActionBottomBar(
          currentIndex: controller.currentIndex.value,
          onTap: (index) {
            if (index != 0) {
              if (controller.shopState.value == ShopState.USER_NOT_ACCEPT_ACCESS) {
                Navigator.of(context)
                    .push(
                  MaterialPageRoute(
                    builder: (_) => AccessPermissionScreen(
                      index: index,
                    ),
                  ),
                )
                    .then((value) {
                  if (value == true) {
                    //đồng ý vào cấp quyền truy cập
                    // controller.setAcceptPermission(true);
                    controller.permissionAccept();
                  } else {
                    controller.setAcceptPermission(true);
                  }
                });
              } else {
                controller.setCurrentIndex(index ?? 0);
              }
            } else {
              controller.setCurrentIndex(index ?? 0);
            }
          },
        ),
        body: _BuildTabView(controller.currentIndex.value),
      ),
    );
  }
}

class _ListActionBottomBar extends StatelessWidget {
  const _ListActionBottomBar({
    this.onTap,
    this.currentIndex,
  });
  final Function(int?)? onTap;
  final int? currentIndex;

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 78,
      decoration: BoxDecoration(
        color: Colors.white,
        // borderRadius: BorderRadius.only(topLeft: Radius.circular(40), topRight: Radius.circular(40)),
        boxShadow: [
          BoxShadow(
            offset: Offset(0, -15),
            blurRadius: 20,
            color: Color(0xffc8c8c8).withValues(alpha: 0.2),
          ),
        ],
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: List.generate(
          3,
          (index) => _buildBottomBarIcon(
            icon: SvgPath.listBottomIcon[index],
            lable: SvgPath.listBottomIconLable[index],
            isActive: currentIndex == index,
            onTap: () => onTap?.call(index),
          ),
        ),
      ),
    );
  }
}

Widget _buildBottomBarIcon({
  required VoidCallback onTap,
  required bool isActive,
  required String icon,
  required String lable,
}) {
  return InkWell(
    onTap: () => onTap.call(),
    child: Container(
      // width: 50,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          SvgPicture.asset(
            icon,
            colorFilter: ColorFilter.mode(isActive ? AppColors.redCE3722 : AppColors.grey7D7D7D, BlendMode.srcIn),
          ),

          Padding(
            padding: const EdgeInsets.only(top: 3.0),
            child: Text(
              lable,
              style: AppTextStyle.s12Medium.copyWith(
                  color: isActive ? AppColors.redCE3722 : AppColors.grey7D7D7D,
                  height: 15 / 12),
            ),
          )
          // Container(
          //   width: 5,
          //   height: 5,
          //   decoration: BoxDecoration(
          //     shape: BoxShape.circle,
          //     color: isActive ? AppColors.blue30AAB7 : null,
          //   ),
          // ).paddingOnly(top: 5),
        ],
      ),
    ),
  );
}
