import 'package:flutter/material.dart';
import '../../components/reusable_primary_button.dart';
import '../../constants/text_style.dart';


class NoConnection extends StatelessWidget {
  final Function() action;
  const NoConnection(this.action,{Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          Image.asset(
            'packages/shopping/assets/error/Connection_Lost.png',
            fit: BoxFit.cover,
            height: MediaQuery.of(context).size.height,
            width: MediaQuery.of(context).size.width,
          ),
          const Positioned(
            bottom: 200,
            left: 30,
            child: Text(
              'Không có kết nối internet',
              style: kTitleTextStyle,
            ),
          ),
          const Positioned(
            bottom: 150,
            left: 30,
            child: Text(
              'Vui lòng kiểm tra lại kết nối mạng\nvà thử lại.',
              style: kSubtitleTextStyle,
              textAlign: TextAlign.start,
            ),
          ),
          Positioned(
            bottom: 50,
            left: 40,
            right: 40,
            child: ReusablePrimaryButton(
              childText: 'Thử lại',
              buttonColor: Colors.blue[800]!,
              childTextColor: Colors.white,
              onPressed: () {action();},
            ),
          ),
        ],
      ),
    );
  }
}
