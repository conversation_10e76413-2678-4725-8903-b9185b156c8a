import 'package:shopping/constants/app_assets_paths.dart';

enum PackageType { hotDeal, data, SMS, combo, sim, insuranceABIC, all, unknown }

extension IconPackageExt on PackageType {
  String get nameTypePackage {
    switch (this) {
      case PackageType.hotDeal:
        return 'Hot deal';
      case PackageType.data:
        return 'Data';
      case PackageType.SMS:
        return 'Thoại/SMS';
      case PackageType.combo:
        return 'Combo';
      case PackageType.sim:
        return 'Sim Bundle';
      case PackageType.insuranceABIC:
        return 'Bảo hiểm';
      case PackageType.all:
        return 'Tất cả';
      default:
        return '';
    }
  }

  String get lablePackageType {
    switch (this) {
      case PackageType.hotDeal:
        return 'Hot deal';
      case PackageType.data:
        return 'Gói data';
      case PackageType.SMS:
        return 'Gói thoại/SMS';
      case PackageType.combo:
        return 'Gói combo';
      case PackageType.sim:
        return 'SIM số';
      case PackageType.insuranceABIC:
        return 'Bảo hiểm';
      case PackageType.all:
        return 'Tất cả';
      default:
        return '';
    }
  }

  String get icon {
    switch (this) {
      case PackageType.hotDeal:
        return SvgPath.svgIconHotDeal;
      case PackageType.data:
        return SvgPath.svgIconDataHome;
      case PackageType.SMS:
        return SvgPath.svgIconSMSHome;
      case PackageType.combo:
        return SvgPath.svgIconComboHome;
      case PackageType.sim:
        return SvgPath.svgIconSim;
      case PackageType.insuranceABIC:
        return SvgPath.svgIconInsurance;
      default:
        return '';
    }
  }

  int get getIdCount {
    switch (this) {
      case PackageType.data:
        return 1;
      case PackageType.SMS:
        return 2;
      case PackageType.combo:
        return 3;
      case PackageType.all:
        return 0;
      // chờ BE bổ sung
      default:
        return 0;
    }
  }

  int? get getValue {
    switch (this) {
      case PackageType.data:
        return 1;
      case PackageType.SMS:
        return 2;
      case PackageType.combo:
        return 3;
      case PackageType.sim:
        return 4;
      case PackageType.insuranceABIC:
        return 6;
      case PackageType.all:
        return null;
      default:
        return null;
    }
  }

  String getNameWithCount(int? count) {
    switch (this) {
      case PackageType.hotDeal:
        return 'Hot deal(${count ?? 0})';
      case PackageType.data:
        return 'Data(${count ?? 0})';
      case PackageType.SMS:
        return 'Thoại/SMS(${count ?? 0})';
      case PackageType.combo:
        return 'Combo(${count ?? 0})';
      case PackageType.all:
        return 'Tất cả(${count ?? 0})';
      default:
        return '';
    }
  }
}
