import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:shopping/constants/app_assets_paths.dart';
import 'package:shopping/constants/app_colors.dart';
import 'package:shopping/constants/app_text_style.dart';
import 'package:shopping/constants/enum_step_ord_insurance.dart';
import 'package:shopping/loading_screen.dart';
import 'package:shopping/pages/insurance/widgets/title_lable_widget.dart';
import 'package:shopping/pages/sim/widgets/dash_line_widget.dart';
import 'package:shopping/pages/sim/widgets/stepper_widget.dart';

class CommonBuyInsuranceView extends StatelessWidget {
  const CommonBuyInsuranceView({
    super.key,
    required this.title,
    required this.stepType,
    required this.body,
    required this.onClickBtn,
    required this.lableBtn,
    this.bottonWidget,
    this.enableBtn,
    this.isLoading,
    this.isLoadingBtn,
  });
  final String title;
  final StepOrdInsuranceType stepType;
  final Widget body;
  final Function() onClickBtn;
  final String lableBtn;
  final Widget? bottonWidget;
  final bool? enableBtn;
  final bool? isLoading;
  final bool? isLoadingBtn;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        backgroundColor: Colors.white,
        appBar: AppBar(
          titleSpacing: 0,
          leading: GestureDetector(
            child: Container(
              width: 20,
              height: 20,
              color: Colors.transparent,
              child: SvgPicture.asset(SvgPath.svgArrowBack,
                  colorFilter: ColorFilter.mode(AppColors.black, BlendMode.srcIn), fit: BoxFit.scaleDown),
            ),
            onTap: () async {
              Navigator.pop(context);
            },
          ),
          backgroundColor: AppColors.white,
          elevation: 5,
          shadowColor: AppColors.black.withValues(alpha: 0.4),
          surfaceTintColor: AppColors.white,
          title: Text(
            title.toUpperCase(),
            style: AppTextStyle.s16Bold.copyWith(color: AppColors.black2E2E2E),
          ),
          centerTitle: true,
        ),
        body: SafeArea(
          child: Padding(
            padding: EdgeInsets.only(top: 12, left: 11, right: 11),
            child: (isLoading == true)
                ? LoadingWidget()
                : Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      SizedBox(
                        height: 53,
                        child: LineStepStatusWidget(
                            checkStep: true,
                            currentStepIndex:
                                StepOrdInsuranceType.values.indexOf(stepType),
                            lisStepStatus: StepOrdInsuranceType.values
                                .map((e) => e.getName)
                                .toList()),
                      ),
                      Padding(
                        padding: const EdgeInsets.only(top: 18),
                        child: DashLineWidget(),
                      ),
                      Expanded(
                          child: SingleChildScrollView(
                              physics: ClampingScrollPhysics(), child: body)),
                      Container(
                        margin: EdgeInsets.only(bottom: 0),
                        padding: EdgeInsets.only(top: 11),
                        decoration: BoxDecoration(
                            border: Border(
                                top: BorderSide(color: AppColors.greyEEEEEE))),
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            bottonWidget ?? SizedBox(),
                            InkWell(
                              splashColor: Colors.transparent,
                              onTap: () {
                                if (enableBtn == false ||
                                    isLoadingBtn == true) {
                                  return;
                                }
                                onClickBtn.call();
                              },
                              child: Container(
                                margin: EdgeInsets.only(top: 4, bottom: 15),
                                padding: EdgeInsets.symmetric(vertical: 12),
                                decoration: BoxDecoration(
                                    color: AppColors.redCE3722.withValues(
                                        alpha: enableBtn == false ? 0.5 : 1),
                                    borderRadius: BorderRadius.circular(8)),
                                child: Center(
                                    child: isLoadingBtn == true
                                        ? CircularProgressIndicator(
                                            strokeWidth: 2,
                                            color: AppColors.white)
                                        : Text(
                                            lableBtn.toUpperCase(),
                                            style: AppTextStyle.s16Medium
                                                .copyWith(
                                                    color: AppColors.white),
                                          )),
                              ),
                            )
                          ],
                        ),
                      )
                    ],
                  ),
          ),
        ));
  }
}

class CustomCommonDropdown<T> extends StatelessWidget {
  final List<T> items;
  final T? value;
  final void Function(T?) onChanged;
  final String Function(T) itemAsString;
  final String? lable;
  final EdgeInsetsGeometry? padding;
  final TextStyle? lableTextStyle;
  final TextStyle? contentTextStyle;
  final bool? isRequired;
  final bool? isErr;

  const CustomCommonDropdown(
      {super.key,
      required this.items,
      required this.value,
      required this.onChanged,
      required this.itemAsString,
      this.lable,
      this.padding,
      this.lableTextStyle,
      this.isRequired,
      this.contentTextStyle,
      this.isErr});

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        lable != null
            ? Padding(
                padding: const EdgeInsets.only(bottom: 8),
                child: TitleLable(
                    lable: lable ?? '',
                    style: lableTextStyle,
                    isRequired: isRequired),
              )
            : SizedBox(),
        DropdownButtonFormField<T>(
          focusColor: AppColors.white,
          dropdownColor: AppColors.white,
          icon: SvgPicture.asset(SvgPath.svgArrowDown, colorFilter: ColorFilter.mode(AppColors.black, BlendMode.srcIn)),
          padding: padding,
          value: value,
          items: items.map((item) {
            return DropdownMenuItem<T>(
              value: item,
              child: Text(itemAsString(item), style: contentTextStyle),
            );
          }).toList(),
          onChanged: onChanged,
          decoration: InputDecoration(
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(
                  color: isErr == true ? Colors.red : AppColors.greyD9D9D9),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(
                  color: isErr == true ? Colors.red : AppColors.greyD9D9D9),
            ),
          ),
        ),
      ],
    );
  }
}
