import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

class AppTextStyle {
  AppTextStyle._();

  static const font = 'Montserrat';

  static TextStyle s5 = GoogleFonts.montserrat(fontSize: 5);
  static TextStyle s8 = GoogleFonts.montserrat(fontSize: 8);
  static TextStyle s9 = GoogleFonts.montserrat(fontSize: 9);
  // size 10
  static TextStyle s10 = GoogleFonts.montserrat(fontSize: 10);
  static TextStyle s10Medium = GoogleFonts.montserrat(
    fontSize: 10,
    fontWeight: FontWeight.w500,
  );
  static TextStyle s10Bold = GoogleFonts.montserrat(
    fontSize: 10,
    fontWeight: FontWeight.w700,
  );
  static TextStyle s10Regular = GoogleFonts.montserrat(
    fontSize: 10,
    fontWeight: FontWeight.w400,
  );
  static TextStyle s10SemiBold = GoogleFonts.montserrat(
    fontSize: 10,
    fontWeight: FontWeight.w600,
  );

  // size 11
  static TextStyle s11 = GoogleFonts.montserrat(fontSize: 11);
  static TextStyle s11Medium = GoogleFonts.montserrat(
    fontSize: 11,
    fontWeight: FontWeight.w500,
  );
  static TextStyle s11Bold = GoogleFonts.montserrat(
    fontSize: 11,
    fontWeight: FontWeight.w700,
  );
  static TextStyle s11Regular = GoogleFonts.montserrat(
    fontSize: 11,
    fontWeight: FontWeight.w400,
  );
  static TextStyle s11SemiBold = GoogleFonts.montserrat(
    fontSize: 11,
    fontWeight: FontWeight.w600,
  );
  // size 12
  static TextStyle s12 = GoogleFonts.montserrat(fontSize: 12);
  static TextStyle s12Inter = GoogleFonts.montserrat(fontSize: 12);
  static TextStyle s12Medium = GoogleFonts.montserrat(
    fontSize: 12,
    fontWeight: FontWeight.w500,
  );
  static TextStyle s12Bold = GoogleFonts.montserrat(
    fontSize: 12,
    fontWeight: FontWeight.w700,
  );
  static TextStyle s12Regular = GoogleFonts.montserrat(
    fontSize: 12,
    fontWeight: FontWeight.w400,
  );
  static TextStyle s12SemiBold = GoogleFonts.montserrat(
    fontSize: 12,
    fontWeight: FontWeight.w600,
  );
  // size 13
  static TextStyle s13 = GoogleFonts.montserrat(fontSize: 13);
  static TextStyle s13Medium = GoogleFonts.montserrat(
    fontSize: 13,
    fontWeight: FontWeight.w500,
  );
  static TextStyle s13Bold = GoogleFonts.montserrat(
    fontSize: 13,
    fontWeight: FontWeight.w700,
  );
  static TextStyle s13Regular = GoogleFonts.montserrat(
    fontSize: 13,
    fontWeight: FontWeight.w400,
  );
  static TextStyle s13SemiBold = GoogleFonts.montserrat(
    fontSize: 13,
    fontWeight: FontWeight.w600,
  );
  // size 14
  static TextStyle s14 = GoogleFonts.montserrat(fontSize: 14);
  static TextStyle s14Medium = GoogleFonts.montserrat(
    fontSize: 14,
    fontWeight: FontWeight.w500,
  );
  static TextStyle s14Bold = GoogleFonts.montserrat(
    fontSize: 14,
    fontWeight: FontWeight.w700,
  );
  static TextStyle s14Regular = GoogleFonts.montserrat(
    fontSize: 14,
    fontWeight: FontWeight.w400,
  );
  static TextStyle s14SemiBold = GoogleFonts.montserrat(
    fontSize: 14,
    fontWeight: FontWeight.w600,
  );
  // size 15
  static TextStyle s15 = GoogleFonts.montserrat(fontSize: 15);
  static TextStyle s15Regular = GoogleFonts.montserrat(
    fontSize: 15,
    fontWeight: FontWeight.w400,
  );
  static TextStyle s15Medium = GoogleFonts.montserrat(
    fontSize: 15,
    fontWeight: FontWeight.w500,
  );
  static TextStyle s15Bold = GoogleFonts.montserrat(
    fontSize: 15,
    fontWeight: FontWeight.w700,
  );
  static TextStyle s15SemiBold = GoogleFonts.montserrat(
    fontSize: 15,
    fontWeight: FontWeight.w600,
  );
  // size 16
  static TextStyle s16 = GoogleFonts.montserrat(fontSize: 16);
  static TextStyle s16Regular = GoogleFonts.montserrat(
    fontSize: 16,
    fontWeight: FontWeight.w400,
  );
  static TextStyle s16Medium = GoogleFonts.montserrat(
    fontSize: 16,
    fontWeight: FontWeight.w500,
  );
  static TextStyle s16Bold = GoogleFonts.montserrat(
    fontSize: 16,
    fontWeight: FontWeight.w700,
  );
  static TextStyle s16SemiBold = GoogleFonts.montserrat(
    fontSize: 16,
    fontWeight: FontWeight.w600,
  );
  //size 18
  static TextStyle s18Regular = GoogleFonts.montserrat(
    fontSize: 18,
    fontWeight: FontWeight.w400,
  );
  static TextStyle s18Medium = GoogleFonts.montserrat(
    fontSize: 18,
    fontWeight: FontWeight.w500,
  );
  static TextStyle s18Bold = GoogleFonts.montserrat(
    fontSize: 18,
    fontWeight: FontWeight.w700,
  );
  static TextStyle s18SemiBold = GoogleFonts.montserrat(
    fontSize: 18,
    fontWeight: FontWeight.w600,
  );
  // size 20
  static TextStyle s20Regular = GoogleFonts.montserrat(
    fontSize: 20,
    fontWeight: FontWeight.w400,
  );
  static TextStyle s20Medium = GoogleFonts.montserrat(
    fontSize: 20,
    fontWeight: FontWeight.w500,
  );
  static TextStyle s20Bold = GoogleFonts.montserrat(
    fontSize: 20,
    fontWeight: FontWeight.w700,
  );
  static TextStyle s20SemiBold = GoogleFonts.montserrat(
    fontSize: 20,
    fontWeight: FontWeight.w600,
  );
  // size 24
  static TextStyle s24Regular = GoogleFonts.montserrat(
    fontSize: 24,
    fontWeight: FontWeight.w400,
  );
  static TextStyle s24Medium = GoogleFonts.montserrat(
    fontSize: 24,
    fontWeight: FontWeight.w500,
  );
  static TextStyle s24Bold = GoogleFonts.montserrat(
    fontSize: 24,
    fontWeight: FontWeight.w700,
  );
  static TextStyle s24SemiBold = GoogleFonts.montserrat(
    fontSize: 24,
    fontWeight: FontWeight.w600,
  );
  //size 22
  static TextStyle s22 = GoogleFonts.montserrat(fontSize: 22);
  static TextStyle s22Medium = GoogleFonts.montserrat(
    fontSize: 22,
    fontWeight: FontWeight.w500,
  );
  static TextStyle s22Regular = GoogleFonts.montserrat(
    fontSize: 22,
    fontWeight: FontWeight.w400,
  );
  static TextStyle s22Bold = GoogleFonts.montserrat(
    fontSize: 22,
    fontWeight: FontWeight.w700,
  );
  static TextStyle s22SemiBold = GoogleFonts.montserrat(
    fontSize: 22,
    fontWeight: FontWeight.w600,
  );
}
