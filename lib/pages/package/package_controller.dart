import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:get/get.dart';
import 'package:infinite_scroll_pagination/infinite_scroll_pagination.dart';
import 'package:loader_overlay/loader_overlay.dart';
import 'package:shopping/apis/ShoppingRepository.dart';
import 'package:shopping/app_services/api_service.dart';
import 'package:shopping/constants/enum_filter_product.dart';
import 'package:shopping/constants/enum_package_type.dart';
import 'package:shopping/enum/home_type.dart';
import 'package:shopping/loading_screen.dart';
import 'package:shopping/models/base_response.dart';
import 'package:shopping/models/home_model.dart';
import 'package:shopping/models/product.dart';


class PackageController extends GetxController {
  final PackageType packageTypeInput;
  final HomeData? homeData;
  final BuildContext context;
  PackageController({required this.packageTypeInput, this.homeData, required this.context});

  final int perPage = 50;
  final PagingController<int, Product> pageController = PagingController(firstPageKey: 1);

  /// loading
  Rx<bool> isLoading = false.obs;
  // title
  String packageTitle = '';

  /// tab
  final List<PackageType> listPackage = [PackageType.data, PackageType.SMS, PackageType.combo];
  List<PackageType> listTabType = [PackageType.all];
  final Rx<PackageType> packageTypeChoosed = (PackageType.all).obs;
  bool isHotDeal = false;
  int? group;

  /// search
  Rx<bool> isSearching = false.obs;
  TextEditingController searchController = TextEditingController();
  FocusNode focusNode = FocusNode();
  String searchKeyWord = '';
  FocusNode searchFocusNode = FocusNode();

  /// filter
  // gói cước
  List<CycleType> listCycleTypeChoosed = [];
  List<CycleType> listCycleResult = [];
  // khoảng giá
  List<PriceFromType> listPriceFromTypeChoosed = [];
  List<PriceFromType> listPriceFromResult = [];
  // sắp xếp
  OrderByType? orderByTypeChoosed;
  OrderByType? orderByTypeResult;
  // số lượng tiêu chí filter
  Rx<int> countNumTypeFilter = 0.obs;

  /// list_data_result
  List<Product> listProductResult = [];
  // message lỗi
  Rx<String> messageFail = ''.obs;
  @override
  void onInit() {
    _initData();
    super.onInit();
  }

  @override
  void onReady() {
    print("onReady");
    super.onReady();
  }

  @override
  void onClose() {
    print("onClose");
    super.onClose();
  }

  void onDisMissKeyboard() {
    focusNode.unfocus();
  }

  void _initData() async {
    // get tab (lấy danh sách tab phụ thuộc type package đang xem)
    _getListTabBar();
    // get title
    _getTitle();
    _initGroupAndHotDeal();
    _initFilter();
    pageController.addPageRequestListener((pageKey) {
      getListData(pageKey: pageKey);
    });
    // await getListData(pageKey: 1);
  }

  void _initGroupAndHotDeal() {
    // case open từ config home
    if (isFromConfigHome) {
      // loại hot deal
      if (homeData?.type == (HomeType.hotDeal).getValue) {
        isHotDeal = true;
      }
      group = null;
      // loại sản phẩm bình thường thì chỉ cần page setting id
    } else {
      // open từ top action
      if (packageTypeInput == PackageType.hotDeal) {
        isHotDeal = true;
      } else {
        isHotDeal = false;
        group = packageTypeInput.getValue;
      }
    }
  }

  void _initFilter() {
    listCycleResult.clear();
    listPriceFromResult.clear();
    orderByTypeResult = null;
    countNumTypeFilter.value = 0;
  }

  void onInitDataToOpenFilter() {
    listCycleTypeChoosed.clear();
    listCycleTypeChoosed.addAll(listCycleResult);
    listPriceFromTypeChoosed.clear();
    listPriceFromTypeChoosed.addAll(listPriceFromResult);
    orderByTypeChoosed = null;
    orderByTypeChoosed = orderByTypeResult;
    update([PackageControllerUpdateKey.updateListCycleType, PackageControllerUpdateKey.updateListPriceType, PackageControllerUpdateKey.updateOrderByType]);
  }

  void onResetFilter() async {
    _initFilter();
    onInitDataToOpenFilter();
    countNumTypeFilter.value = countFilter();
    await getListData(pageKey: 1);
  }

  void onSelectFilterCycle(CycleType cycleType) {
    // case set giá trị false (do select chẵn lần)
    if (listCycleTypeChoosed.contains(cycleType)) {
      listCycleTypeChoosed.remove(cycleType);
    } else {
      listCycleTypeChoosed.add(cycleType);
    }
    update([PackageControllerUpdateKey.updateListCycleType]);
  }

  bool isFilterCycleSelected(CycleType cycleType) {
    return (listCycleTypeChoosed.contains(cycleType));
  }

  void onSelectFilterPrice(PriceFromType priceFromType) {
    // case set giá trị false (do select chẵn lần)
    if (listPriceFromTypeChoosed.contains(priceFromType)) {
      listPriceFromTypeChoosed.remove(priceFromType);
    } else {
      listPriceFromTypeChoosed.add(priceFromType);
    }
    update([PackageControllerUpdateKey.updateListPriceType]);
  }

  bool isFilterPriceSelected(PriceFromType priceFromType) {
    return (listPriceFromTypeChoosed.contains(priceFromType));
  }

  void onSelectFilterOrderBy(OrderByType orderByType) {
    if (orderByTypeChoosed == orderByType) {
      orderByTypeChoosed = null;
    } else {
      orderByTypeChoosed = orderByType;
    }
    update([PackageControllerUpdateKey.updateOrderByType]);
  }

  void onSubmitFilter() async {
    // gán giá trị
    _initFilter();
    listCycleResult.addAll(listCycleTypeChoosed);
    listPriceFromResult.addAll(listPriceFromTypeChoosed);
    orderByTypeResult = orderByTypeChoosed;
    // count lọc
    countNumTypeFilter.value = countFilter();
    await getListData(pageKey: 1);
  }

  int countFilter() {
    int tmp = 0;
    if (listCycleResult.isNotEmpty) tmp++;
    if (listPriceFromResult.isNotEmpty) tmp++;
    if (orderByTypeResult != null) tmp++;
    return tmp;
  }

  void _getListTabBar() async {
    // isLoading.value = true;
    // get tab phụ thuộc type package đang xem
    // case open từ config home
    if (isFromConfigHome) {
      if (homeData?.type == (HomeType.hotDeal).getValue) {
        listTabType.addAll(listPackage);
      } else {
        bool check = await _checkIsHaveHotDeal();
        if (check) listTabType.add(PackageType.hotDeal);
      }
    } // case open từ top action
    else {
      if (packageTypeInput == PackageType.hotDeal) {
        listTabType.addAll(listPackage);
      } else {
        bool check = await _checkIsHaveHotDeal();
        if (check) listTabType.add(PackageType.hotDeal);
      }
    }

    update([PackageControllerUpdateKey.updateTab]);
  }

  void toggleSearch() async {
    searchKeyWord = '';
    searchController.clear();
    isSearching.toggle();
    if (isSearching.value) {
      searchFocusNode.requestFocus();
    }
    if (!isSearching.value) {
      await getListData(pageKey: 1);
    }
  }

  // void onChangeSetValue(String value) {
  // set value
  // searchKeyWord = value.trim();
  // }

  void onTapSearch() async {
    searchKeyWord = searchController.text.trim();
    await getListData(pageKey: 1);
  }

  void _getTitle() {
    // case open từ config home
    if (isFromConfigHome) {
      packageTitle = homeData?.name ?? '';
    } else {
      // mở từ top action
      packageTitle = packageTypeInput.lablePackageType.toUpperCase();
    }
    update([PackageControllerUpdateKey.updatePackageTitle]);
  }

  Future getListData({int? pageKey}) async {
    context.loaderOverlay.show();
    onDisMissKeyboard();
    BaseResponse<ProductsResponse> res;
    // phần filter
    List<int>? _listCycle = _getListCycleFilter();
    List<int>? _listPrice = _getListPriceFilter();
    String? _orderBy = _getOrderByFilter();
    // mở từ top action
    if (!isFromConfigHome) {
      res = await DelayLoadingCallApi.delayCallApi(ShoppingRepository.instance.get_products2(isHotDeal: isHotDeal ? 1 : null, keyword: searchKeyWord, group: group, cycles: _listCycle, prices: _listPrice, orderBy: _orderBy, perPage: perPage, page: pageKey));
    } else {
      // mở từ config home
      res = await DelayLoadingCallApi.delayCallApi(ShoppingRepository.instance.get_products2(pageSettingId: homeData?.id, isHotDeal: isHotDeal ? 1 : null, keyword: searchKeyWord, group: group, cycles: _listCycle, prices: _listPrice, orderBy: _orderBy, perPage: perPage, page: pageKey));
    }
    context.loaderOverlay.hide();
    // nếu là trang đầu tiên -> clear data
    if (pageKey == 1) {
      // listProductResult.clear();
      pageController.itemList?.clear();
    }
    if (res.code == CODE_SUCCESS) {
      if (res.message != null && (res.message?.isNotEmpty ?? false)) {
        messageFail.value = res.message ?? '';
        pageController.itemList?.clear();
        // listProductResult = [];
      } else {
        final List<Product>? listP = res.data?.items;
        final isLastPage = listP!.length < perPage;
        if (isLastPage) {
          pageController.appendLastPage(listP);
        } else {
          final nextPageKey = pageKey! + 1;
          pageController.appendPage(listP, nextPageKey);
        }
        // listProductResult = pageController.itemList!;
        messageFail.value = '';
      }
    }
    // listProductResult.addAll(pageController.itemList ?? []);
    update([PackageControllerUpdateKey.updateListProduct]);
  }

  List<int>? _getListCycleFilter() {
    return (listCycleResult.isEmpty ? null : List<int>.from(listCycleResult.map((e) => e.getValue)));
  }

  List<int>? _getListPriceFilter() {
    List<int> _listTmp = [];
    if (listPriceFromResult.isNotEmpty) {
      // check case chứa giá 1tr => xoá r add vào sau thành phần tử cuối
      if (listPriceFromResult.contains(PriceFromType.f1M)) {
        listPriceFromResult.remove(PriceFromType.f1M);
        listPriceFromResult.add(PriceFromType.f1M);
      }
      for (PriceFromType e in listPriceFromResult) {
        _listTmp.addAll(e.getListPrice);
      }
    }
    return listPriceFromResult.isEmpty ? null : _listTmp;
  }

  String? _getOrderByFilter() {
    return orderByTypeResult?.getValue ?? null;
  }

  void onChangeTab(PackageType packageType) async {
    if (packageTypeChoosed.value == packageType) {
      return;
    }
    packageTypeChoosed.value = packageType;
    (isFromConfigHome) ? _updateGroupAndHotDealConfigHome() : _updateGroupAndHotDealTopAction();
    await getListData(pageKey: 1);
  }

  void _updateGroupAndHotDealConfigHome() {
    // nếu là mở tab "tất cả" -> hotdeal phụ thuộc vào home data (init lại)
    if (packageTypeChoosed.value == PackageType.all) {
      isHotDeal = (homeData?.type == (HomeType.hotDeal).getValue);
      group = null;
      return;
    }

    /// nếu là các tab khác thì update lại group hoặc hotdeal
    // update group (nếu home data là hotdeal)
    if (homeData?.type == (HomeType.hotDeal).getValue) {
      group = packageTypeChoosed.value.getValue;
      return;
    }
    // update isHotDeal (nếu home data là sản phẩm bthg: data, thoại, combo)
    if (packageTypeChoosed.value == PackageType.hotDeal) {
      isHotDeal = true;
      group = null;
      return;
    }
  }

  void _updateGroupAndHotDealTopAction() {
    // nếu là mở tab "tất cả" -> group và hotdeal phụ thuộc vào packageTypeInput
    if (packageTypeChoosed.value == PackageType.all) {
      isHotDeal = packageTypeInput == PackageType.hotDeal;
      group = (packageTypeInput == PackageType.hotDeal) ? null : packageTypeInput.getValue;
      return;
    }

    /// nếu là các tab khác thì update lại group hoặc hotdeal
    // update group (nếu input type là hotdeal)
    if (packageTypeInput == PackageType.hotDeal) {
      group = packageTypeChoosed.value.getValue;
      return;
    }
    // update isHotDeal (nếu input type là: data, thoại, combo)
    if (packageTypeChoosed.value == PackageType.hotDeal) {
      isHotDeal = true;
      return;
    }
  }

  // case open từ config home
  bool get isFromConfigHome {
    return (packageTypeInput == PackageType.unknown && homeData != null);
  }

  Future<bool> _checkIsHaveHotDeal() async {
    BaseResponse<ProductsResponse> res;
    // open từ config home
    if (isFromConfigHome) {
      if (homeData?.type != (HomeType.hotDeal).getValue) {
        res = await ShoppingRepository.instance.get_products2(pageSettingId: homeData?.id, isHotDeal: 1);
      } else {
        return true;
      }
    }
    // open từ top action
    else {
      if (packageTypeInput != PackageType.hotDeal) {
        res = await ShoppingRepository.instance.get_products2(isHotDeal: 1, group: packageTypeInput.getValue);
      } else {
        return true;
      }
    }
    if (res.code == CODE_SUCCESS) {
      if ((res.data?.items ?? []).isNotEmpty) {
        return true;
      }
    }
    return false;
  }
}

class PackageControllerUpdateKey {
  static const String updateTab = 'updatePackageTab';
  static const String updateListProduct = 'updateListProduct';
  static const String updatePackageTitle = 'updatePackageTitle';
  static const String updateListCycleType = 'updateListCycleType';
  static const String updateListPriceType = 'updateListPriceType';
  static const String updateOrderByType = 'updateOrderByType';
}
