class InsuranceGetPriceFormResquest {
    int? loaiBh;
    String? loaiXe;
    int? thoiHan;
    int? tgBatDau;
    String? mdSuDung;
    int? soChoNgoi;
    num? trongTai;
    int? namDaSd;
    String? gtriNgoiNha;
    String? maKm;
    List<String>? spMuaKem;

    InsuranceGetPriceFormResquest({
        this.loaiBh,
        this.loaiXe,
        this.thoiHan,
        this.tgBatDau,
        this.mdSuDung,
        this.soChoNgoi,
        this.trongTai,
        this.namDaSd,
        this.gtriNgoiNha,
        this.maKm,
        this.spMuaKem,
    });

    InsuranceGetPriceFormResquest copyWith({
        int? loaiBh,
        String? loaiXe,
        int? thoiHan,
        int? tgBatDau,
        String? mdSuDung,
        int? soChoNgoi,
        num? trongTai,
        int? namDaSd,
        String? gtriNgoiNha,
        String? maKm,
        List<String>? spMuaKem,
    }) => 
        InsuranceGetPriceFormResquest(
            loaiBh: loaiBh ?? this.loaiBh,
            loaiXe: loaiXe ?? this.loaiXe,
            thoiHan: thoiHan ?? this.thoiHan,
            tgBatDau: tgBatDau ?? this.tgBatDau,
            mdSuDung: mdSuDung ?? this.mdSuDung,
            soChoNgoi: soChoNgoi ?? this.soChoNgoi,
            trongTai: trongTai ?? this.trongTai,
            namDaSd: namDaSd ?? this.namDaSd,
            gtriNgoiNha: gtriNgoiNha ?? this.gtriNgoiNha,
            maKm: maKm ?? this.maKm,
            spMuaKem: spMuaKem ?? this.spMuaKem,
        );

    factory InsuranceGetPriceFormResquest.fromJson(Map<String, dynamic> json) => InsuranceGetPriceFormResquest(
        loaiBh: json["loai_bh"],
        loaiXe: json["loai_xe"],
        thoiHan: json["thoi_han"],
        tgBatDau: json["tg_bat_dau"],
        mdSuDung: json["md_su_dung"],
        soChoNgoi: json["so_cho_ngoi"],
        trongTai: json["trong_tai"],
        namDaSd: json["nam_da_sd"],
        gtriNgoiNha: json["gtri_ngoi_nha"],
        maKm: json["ma_km"],
        spMuaKem: json["sp_mua_kem"] == null ? [] : List<String>.from(json["sp_mua_kem"]!.map((x) => x)),
    );

    Map<String, dynamic> toJson() => {
        "loai_bh": loaiBh,
        "loai_xe": loaiXe,
        "thoi_han": thoiHan,
        "tg_bat_dau": tgBatDau,
        "md_su_dung": mdSuDung,
        "so_cho_ngoi": soChoNgoi,
        "trong_tai": trongTai,
        "nam_da_sd": namDaSd,
        "gtri_ngoi_nha": gtriNgoiNha,
        "ma_km": maKm,
        "sp_mua_kem": spMuaKem == null ? [] : List<dynamic>.from(spMuaKem!.map((x) => x)),
    };
}
